<?xml version="1.0" encoding="UTF-8"?>
<beans xmlns="http://www.springframework.org/schema/beans"
    xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
    xmlns:jee="http://www.springframework.org/schema/jee"
    xmlns:tx="http://www.springframework.org/schema/tx"
    xsi:schemaLocation="http://www.springframework.org/schema/beans http://www.springframework.org/schema/beans/spring-beans.xsd
        http://www.springframework.org/schema/tx http://www.springframework.org/schema/tx/spring-tx.xsd
        http://www.springframework.org/schema/jee http://www.springframework.org/schema/jee/spring-jee.xsd">

    <jee:jndi-lookup resource-ref="true" id="defaultIpayXmlMessageService" jndi-name="bean/DefaultIpayXmlMessageService" expected-type="za.co.ipay.metermng.ipayxml.IpayXmlMessageService" />

    <!-- *********************************************** -->
    <!-- *** iPay XML messaging to Bizswitch instances * -->
    <!-- *********************************************** -->

    <!-- A place holder BizSwitch bean that takes the place if there is no BizSwitch defined -->
    <bean id="placeHolderBizSwitch" class="za.co.ipay.ipayxml.io.IpayXmlMessageServiceConfig">
        <property name="defaultClient" value="" />
        <property name="defaultTerm" value="" />
        <property name="ip" value="" />
    </bean>

    <!-- BizSwitch used for topup/vend/fin requests -->
    <jee:jndi-lookup resource-ref="true" id="defaultServer" jndi-name="bean/DefaultIpayXmlMessageServiceConfig"
        expected-type="za.co.ipay.ipayxml.io.IpayXmlMessageServiceConfig"
        default-ref="placeHolderBizSwitch"/>

    <!-- SMS BizSwitch which is replaced with the defaultServer above if there is no smsServer defined -->
    <jee:jndi-lookup resource-ref="true" id="smsServer" jndi-name="bean/SmsIpayXmlMessageServiceConfig"
        expected-type="za.co.ipay.ipayxml.io.IpayXmlMessageServiceConfig" default-ref="defaultServer"/>

    <!-- ************************ -->
    <!-- ***	 Data Access    *** -->
    <!-- ************************ -->
    <!-- MeterMngCommon will have all the necessary data access classes -->
    <jee:jndi-lookup resource-ref="true" id="meterMngDataSource" jndi-name="jdbc/MeterMng" expected-type="javax.sql.DataSource"/>
    
    <jee:jndi-lookup resource-ref="true" id="readOnlyMeterMngDataSource" jndi-name="jdbc/ReadOnlyMeterMng" expected-type="javax.sql.DataSource" default-ref="meterMngDataSource"/>

    <!-- TransactionManager for ipay_meter_mng database -->
    <bean id="meterMngTxnManager" class="org.springframework.jdbc.datasource.DataSourceTransactionManager">
        <property name="dataSource" ref="meterMngDataSource"/>
    </bean>

    <tx:annotation-driven transaction-manager="meterMngTxnManager"/>

    <bean id="meterMngHistoryProxyFactory" class="za.co.ipay.dao.history.MybatisHistoryProxyFactory">
        <constructor-arg ref="currentUserNameProvider"></constructor-arg>
    </bean>

    <!-- MyBatis SqlSessionFactory for injection into mappers -->
    <bean id="sqlSessionFactory" class="org.mybatis.spring.SqlSessionFactoryBean">
        <property name="dataSource" ref="meterMngDataSource" />
        <property name="configLocation" value="classpath:/za/co/ipay/metermng/mybatis/mybatisConfig.xml"/>
    </bean>
    
    <bean id="readOnlySqlSessionFactory" class="org.mybatis.spring.SqlSessionFactoryBean">
        <property name="dataSource" ref="readOnlyMeterMngDataSource" />
        <property name="configLocation" value="classpath:/za/co/ipay/metermng/mybatis/mybatisConfig.xml"/>
    </bean>

    <bean id="mybatisJdbcTransactionFactory" class="org.apache.ibatis.transaction.jdbc.JdbcTransactionFactory">
    </bean>

    <!-- MyBatis SqlSessionFactory for injection into mappers -->
    <bean id="sqlSessionFactoryUnmanaged" class="org.mybatis.spring.SqlSessionFactoryBean">
        <property name="dataSource" ref="meterMngDataSource" />
        <property name="transactionFactory" ref="mybatisJdbcTransactionFactory" />
        <property name="configLocation" value="classpath:/za/co/ipay/metermng/mybatis/mybatisConfig.xml"/>
    </bean>

    <!-- Usage Point Mapper -->
    <bean id="usagePointMapperImpl" class="org.mybatis.spring.mapper.MapperFactoryBean">
        <property name="mapperInterface" value="za.co.ipay.metermng.mybatis.generated.mapper.UsagePointMapper" />
        <property name="sqlSessionFactory" ref="sqlSessionFactory" />
    </bean>
    <bean id="usagePointHistMapperImpl" class="org.mybatis.spring.mapper.MapperFactoryBean">
        <property name="mapperInterface" value="za.co.ipay.metermng.mybatis.generated.mapper.UsagePointHistMapper" />
        <property name="sqlSessionFactory" ref="sqlSessionFactory" />
    </bean>
    <bean id="usagePointHistProxy" factory-bean="meterMngHistoryProxyFactory" factory-method="createProxy">
        <constructor-arg ref="usagePointMapperImpl"></constructor-arg>
        <constructor-arg ref="usagePointHistMapperImpl"></constructor-arg>
    </bean>
    <bean id="usagePointMapper" class="za.co.ipay.metermng.mybatis.sessiongroup.UsagePointMapperStaticProxy">
        <constructor-arg ref="usagePointHistProxy"></constructor-arg>
        <constructor-arg ref="currentUserNameProvider"></constructor-arg>
    </bean>

    <bean id="usagePointCustomMapper" class="org.mybatis.spring.mapper.MapperFactoryBean">
        <property name="mapperInterface" value="za.co.ipay.metermng.mybatis.custom.mapper.UsagePointCustomMapper" />
        <property name="sqlSessionFactory" ref="sqlSessionFactory" />
    </bean>

    <!-- Gen Group Mapper -->
    <bean id="genGroupMapperImpl" class="org.mybatis.spring.mapper.MapperFactoryBean">
        <property name="mapperInterface" value="za.co.ipay.metermng.mybatis.generated.mapper.GenGroupMapper" />
        <property name="sqlSessionFactory" ref="sqlSessionFactory" />
    </bean>
    <bean id="genGroupHistMapperImpl" class="org.mybatis.spring.mapper.MapperFactoryBean">
        <property name="mapperInterface" value="za.co.ipay.metermng.mybatis.generated.mapper.GenGroupHistMapper" />
        <property name="sqlSessionFactory" ref="sqlSessionFactory" />
    </bean>
    <bean id="genGroupMapper" factory-bean="meterMngHistoryProxyFactory" factory-method="createProxy">
        <constructor-arg ref="genGroupMapperImpl"></constructor-arg>
        <constructor-arg ref="genGroupHistMapperImpl"></constructor-arg>
    </bean>

    <bean id="iGenGroupMapper" class="org.mybatis.spring.mapper.MapperFactoryBean">
        <property name="mapperInterface" value="za.co.ipay.metermng.mybatis.custom.mapper.IGenGroupMapper" />
        <property name="sqlSessionFactory" ref="sqlSessionFactory" />
    </bean>

    <bean id="genGroupCustomMapper" class="org.mybatis.spring.mapper.MapperFactoryBean">
        <property name="mapperInterface" value="za.co.ipay.metermng.mybatis.custom.mapper.GenGroupCustomMapper" />
        <property name="sqlSessionFactory" ref="sqlSessionFactory" />
    </bean>


    <bean id="groupFeatureMapper" class="org.mybatis.spring.mapper.MapperFactoryBean">
        <property name="mapperInterface" value="za.co.ipay.metermng.mybatis.generated.mapper.GroupFeatureMapper" />
        <property name="sqlSessionFactory" ref="sqlSessionFactory" />
    </bean>


    <!-- Group Type Feature Mapper -->
    <bean id="groupTypeFeatureMapperImpl" class="org.mybatis.spring.mapper.MapperFactoryBean">
        <property name="mapperInterface" value="za.co.ipay.metermng.mybatis.generated.mapper.GroupTypeFeatureMapper" />
        <property name="sqlSessionFactory" ref="sqlSessionFactory" />
    </bean>
    <bean id="groupTypeFeatureHistMapperImpl" class="org.mybatis.spring.mapper.MapperFactoryBean">
        <property name="mapperInterface" value="za.co.ipay.metermng.mybatis.generated.mapper.GroupTypeFeatureHistMapper" />
        <property name="sqlSessionFactory" ref="sqlSessionFactory" />
    </bean>
    <bean id="groupTypeFeatureMapper" factory-bean="meterMngHistoryProxyFactory" factory-method="createProxy">
        <constructor-arg ref="groupTypeFeatureMapperImpl"></constructor-arg>
        <constructor-arg ref="groupTypeFeatureHistMapperImpl"></constructor-arg>
    </bean>


    <!-- Sts Meter Mapper -->
    <bean id="stsMeterMapperImpl" class="org.mybatis.spring.mapper.MapperFactoryBean">
        <property name="mapperInterface" value="za.co.ipay.metermng.mybatis.generated.mapper.StsMeterMapper" />
        <property name="sqlSessionFactory" ref="sqlSessionFactory" />
    </bean>
    <bean id="stsMeterHistMapperImpl" class="org.mybatis.spring.mapper.MapperFactoryBean">
        <property name="mapperInterface" value="za.co.ipay.metermng.mybatis.generated.mapper.StsMeterHistMapper" />
        <property name="sqlSessionFactory" ref="sqlSessionFactory" />
    </bean>
    <bean id="stsMeterMapper" factory-bean="meterMngHistoryProxyFactory" factory-method="createProxy">
        <constructor-arg ref="stsMeterMapperImpl"></constructor-arg>
        <constructor-arg ref="stsMeterHistMapperImpl"></constructor-arg>
    </bean>

    <bean id="stsMeterCustomMapper" class="org.mybatis.spring.mapper.MapperFactoryBean">
        <property name="mapperInterface" value="za.co.ipay.metermng.mybatis.custom.mapper.StsMeterCustomMapper" />
        <property name="sqlSessionFactory" ref="sqlSessionFactory" />
    </bean>

    <!-- Meter Mapper -->
    <bean id="meterMapperImpl" class="org.mybatis.spring.mapper.MapperFactoryBean">
        <property name="mapperInterface" value="za.co.ipay.metermng.mybatis.generated.mapper.MeterMapper" />
        <property name="sqlSessionFactory" ref="sqlSessionFactory" />
    </bean>
    <bean id="meterHistMapperImpl" class="org.mybatis.spring.mapper.MapperFactoryBean">
        <property name="mapperInterface" value="za.co.ipay.metermng.mybatis.generated.mapper.MeterHistMapper" />
        <property name="sqlSessionFactory" ref="sqlSessionFactory" />
    </bean>
    <bean id="meterMapperHistProxy" factory-bean="meterMngHistoryProxyFactory" factory-method="createProxy">
        <constructor-arg ref="meterMapperImpl"></constructor-arg>
        <constructor-arg ref="meterHistMapperImpl"></constructor-arg>
    </bean>
    <bean id="meterMapper" class="za.co.ipay.metermng.mybatis.sessiongroup.MeterMapperStaticProxy">
        <constructor-arg ref="meterMapperHistProxy"></constructor-arg>
        <constructor-arg ref="currentUserNameProvider"></constructor-arg>
    </bean>

    <bean id="meterCustomMapper" class="org.mybatis.spring.mapper.MapperFactoryBean">
        <property name="mapperInterface" value="za.co.ipay.metermng.mybatis.custom.mapper.MeterCustomMapper" />
        <property name="sqlSessionFactory" ref="sqlSessionFactory" />
    </bean>


    <bean id="meterReadingMapper" class="org.mybatis.spring.mapper.MapperFactoryBean">
        <property name="mapperInterface" value="za.co.ipay.metermng.mybatis.generated.mapper.MeterReadingMapper" />
        <property name="sqlSessionFactory" ref="sqlSessionFactory" />
    </bean>

    <bean id="meterReadingCustomMapper" class="org.mybatis.spring.mapper.MapperFactoryBean">
        <property name="mapperInterface" value="za.co.ipay.metermng.mybatis.custom.mapper.MeterReadingCustomMapper" />
        <property name="sqlSessionFactory" ref="sqlSessionFactory" />
    </bean>

    <bean id="timeDimMapper" class="org.mybatis.spring.mapper.MapperFactoryBean">
        <property name="mapperInterface" value="za.co.ipay.metermng.mybatis.generated.mapper.TimeDimMapper" />
        <property name="sqlSessionFactory" ref="sqlSessionFactory" />
    </bean>

    <bean id="meterReadingFactMapper" class="org.mybatis.spring.mapper.MapperFactoryBean">
        <property name="mapperInterface" value="za.co.ipay.metermng.mybatis.generated.mapper.MeterReadingFactMapper" />
        <property name="sqlSessionFactory" ref="sqlSessionFactory" />
    </bean>

    <bean id="iMeterCountMapper" class="org.mybatis.spring.mapper.MapperFactoryBean">
        <property name="mapperInterface" value="za.co.ipay.metermng.server.mybatis.mapper.IMeterCountMapper" />
        <property name="sqlSessionFactory" ref="readOnlySqlSessionFactory" />
    </bean>

    <bean id="advancedSearchCustomMapper" class="org.mybatis.spring.mapper.MapperFactoryBean">
        <property name="mapperInterface" value="za.co.ipay.metermng.mybatis.custom.mapper.AdvancedSearchCustomMapper" />
        <property name="sqlSessionFactory" ref="sqlSessionFactory" />
    </bean>

    <bean id="groupSearchCustomMapper" class="org.mybatis.spring.mapper.MapperFactoryBean">
        <property name="mapperInterface" value="za.co.ipay.metermng.server.mybatis.mapper.GroupSearchCustomMapper" />
        <property name="sqlSessionFactory" ref="sqlSessionFactory" />
    </bean>

    <!-- Customer Mapper -->
    <bean id="customerMapperImpl" class="org.mybatis.spring.mapper.MapperFactoryBean">
        <property name="mapperInterface" value="za.co.ipay.metermng.mybatis.generated.mapper.CustomerMapper" />
        <property name="sqlSessionFactory" ref="sqlSessionFactory" />
    </bean>
    <bean id="customerHistMapperImpl" class="org.mybatis.spring.mapper.MapperFactoryBean">
        <property name="mapperInterface" value="za.co.ipay.metermng.mybatis.generated.mapper.CustomerHistMapper" />
        <property name="sqlSessionFactory" ref="sqlSessionFactory" />
    </bean>
    <bean id="customerMapperHistProxy" factory-bean="meterMngHistoryProxyFactory" factory-method="createProxy">
        <constructor-arg ref="customerMapperImpl"></constructor-arg>
        <constructor-arg ref="customerHistMapperImpl"></constructor-arg>
    </bean>
    <bean id="customerMapper" class="za.co.ipay.metermng.mybatis.sessiongroup.CustomerMapperStaticProxy">
        <constructor-arg ref="customerMapperHistProxy"></constructor-arg>
        <constructor-arg ref="currentUserNameProvider"></constructor-arg>
    </bean>


    <bean id="customerSuggestionMapper" class="org.mybatis.spring.mapper.MapperFactoryBean">
        <property name="mapperInterface" value="za.co.ipay.metermng.server.mybatis.mapper.ICustomerSuggestionMapper" />
        <property name="sqlSessionFactory" ref="sqlSessionFactory" />
    </bean>

    <bean id="readingsCustomMapper" class="org.mybatis.spring.mapper.MapperFactoryBean">
        <property name="mapperInterface" value="za.co.ipay.metermng.server.mybatis.mapper.ReadingsCustomMapper" />
        <property name="sqlSessionFactory" ref="sqlSessionFactory" />
    </bean>

    <!-- Customer Agreement -->
    <bean id="customerAgreementMapperImpl" class="org.mybatis.spring.mapper.MapperFactoryBean">
        <property name="mapperInterface" value="za.co.ipay.metermng.mybatis.generated.mapper.CustomerAgreementMapper" />
        <property name="sqlSessionFactory" ref="sqlSessionFactory" />
    </bean>
    <bean id="customerAgreementHistMapperImpl" class="org.mybatis.spring.mapper.MapperFactoryBean">
        <property name="mapperInterface" value="za.co.ipay.metermng.mybatis.generated.mapper.CustomerAgreementHistMapper" />
        <property name="sqlSessionFactory" ref="sqlSessionFactory" />
    </bean>
    <bean id="customerAgreementMapper" factory-bean="meterMngHistoryProxyFactory" factory-method="createProxy">
        <constructor-arg ref="customerAgreementMapperImpl"></constructor-arg>
        <constructor-arg ref="customerAgreementHistMapperImpl"></constructor-arg>
    </bean>


    <!-- Customer Account Mapper -->
    <bean id="customerAccountMapperImpl" class="org.mybatis.spring.mapper.MapperFactoryBean">
        <property name="mapperInterface" value="za.co.ipay.metermng.mybatis.generated.mapper.CustomerAccountMapper" />
        <property name="sqlSessionFactory" ref="sqlSessionFactory" />
    </bean>
    <bean id="customerAccountHistMapperImpl" class="org.mybatis.spring.mapper.MapperFactoryBean">
        <property name="mapperInterface" value="za.co.ipay.metermng.mybatis.generated.mapper.CustomerAccountHistMapper" />
        <property name="sqlSessionFactory" ref="sqlSessionFactory" />
    </bean>
    <bean id="customerAccountMapper" factory-bean="meterMngHistoryProxyFactory" factory-method="createProxy">
        <constructor-arg ref="customerAccountMapperImpl"></constructor-arg>
        <constructor-arg ref="customerAccountHistMapperImpl"></constructor-arg>
    </bean>


    <!-- Units Account Mapper -->
    <bean id="unitsAccountMapperImpl" class="org.mybatis.spring.mapper.MapperFactoryBean">
        <property name="mapperInterface" value="za.co.ipay.metermng.mybatis.generated.mapper.UnitsAccountMapper" />
        <property name="sqlSessionFactory" ref="sqlSessionFactory" />
    </bean>
    <bean id="unitsAccountHistMapperImpl" class="org.mybatis.spring.mapper.MapperFactoryBean">
        <property name="mapperInterface" value="za.co.ipay.metermng.mybatis.generated.mapper.UnitsAccountHistMapper" />
        <property name="sqlSessionFactory" ref="sqlSessionFactory" />
    </bean>
    <bean id="unitsAccountMapper" factory-bean="meterMngHistoryProxyFactory" factory-method="createProxy">
        <constructor-arg ref="unitsAccountMapperImpl"></constructor-arg>
        <constructor-arg ref="unitsAccountHistMapperImpl"></constructor-arg>
    </bean>

    <bean id="unitsTransMapper" class="org.mybatis.spring.mapper.MapperFactoryBean">
        <property name="mapperInterface" value="za.co.ipay.metermng.mybatis.generated.mapper.UnitsTransMapper" />
        <property name="sqlSessionFactory" ref="sqlSessionFactory" />
    </bean>

    <bean id="unitsAccountCustomMapper" class="org.mybatis.spring.mapper.MapperFactoryBean">
        <property name="mapperInterface" value="za.co.ipay.metermng.mybatis.custom.mapper.UnitsAccountCustomMapper" />
        <property name="sqlSessionFactory" ref="sqlSessionFactory" />
    </bean>


    <!-- Location Mapper -->
    <bean id="locationMapperImpl" class="org.mybatis.spring.mapper.MapperFactoryBean">
        <property name="mapperInterface" value="za.co.ipay.metermng.mybatis.generated.mapper.LocationMapper" />
        <property name="sqlSessionFactory" ref="sqlSessionFactory" />
    </bean>
    <bean id="locationHistMapperImpl" class="org.mybatis.spring.mapper.MapperFactoryBean">
        <property name="mapperInterface" value="za.co.ipay.metermng.mybatis.generated.mapper.LocationHistMapper" />
        <property name="sqlSessionFactory" ref="sqlSessionFactory" />
    </bean>
    <bean id="locationMapper" factory-bean="meterMngHistoryProxyFactory" factory-method="createProxy">
        <constructor-arg ref="locationMapperImpl"></constructor-arg>
        <constructor-arg ref="locationHistMapperImpl"></constructor-arg>
    </bean>


    <!-- Pricing Structure Mapper -->
    <bean id="pricingStructureMapperImpl" class="org.mybatis.spring.mapper.MapperFactoryBean">
        <property name="mapperInterface" value="za.co.ipay.metermng.mybatis.generated.mapper.PricingStructureMapper" />
        <property name="sqlSessionFactory" ref="sqlSessionFactory" />
    </bean>
    <bean id="pricingStructureHistMapperImpl" class="org.mybatis.spring.mapper.MapperFactoryBean">
        <property name="mapperInterface" value="za.co.ipay.metermng.mybatis.generated.mapper.PricingStructureHistMapper" />
        <property name="sqlSessionFactory" ref="sqlSessionFactory" />
    </bean>
    <bean id="pricingStructureMapperHistProxy" factory-bean="meterMngHistoryProxyFactory" factory-method="createProxy">
        <constructor-arg ref="pricingStructureMapperImpl"></constructor-arg>
        <constructor-arg ref="pricingStructureHistMapperImpl"></constructor-arg>
    </bean>
    <bean id="pricingStructureMapper" class="za.co.ipay.metermng.mybatis.sessiongroup.PricingStructureMapperStaticProxy">
        <constructor-arg ref="pricingStructureMapperHistProxy"></constructor-arg>
        <constructor-arg ref="currentUserNameProvider"></constructor-arg>
    </bean>

    <bean id="iCalendarPricingStructureMapper" class="org.mybatis.spring.mapper.MapperFactoryBean">
        <property name="mapperInterface" value="za.co.ipay.metermng.server.mybatis.mapper.ICalendarPricingStructureMapper" />
        <property name="sqlSessionFactory" ref="sqlSessionFactory" />
    </bean>


    <!-- Aux Charge Schedule -->
    <bean id="auxChargeScheduleMapperImpl" class="org.mybatis.spring.mapper.MapperFactoryBean">
        <property name="mapperInterface" value="za.co.ipay.metermng.mybatis.generated.mapper.AuxChargeScheduleMapper" />
        <property name="sqlSessionFactory" ref="sqlSessionFactory" />
    </bean>
    <bean id="auxChargeScheduleHistMapperImpl" class="org.mybatis.spring.mapper.MapperFactoryBean">
        <property name="mapperInterface" value="za.co.ipay.metermng.mybatis.generated.mapper.AuxChargeScheduleHistMapper" />
        <property name="sqlSessionFactory" ref="sqlSessionFactory" />
    </bean>
    <bean id="auxChargeScheduleMapper" factory-bean="meterMngHistoryProxyFactory" factory-method="createProxy">
        <constructor-arg ref="auxChargeScheduleMapperImpl"></constructor-arg>
        <constructor-arg ref="auxChargeScheduleHistMapperImpl"></constructor-arg>
    </bean>


    <!-- Aux Account -->
    <bean id="auxAccountMapperImpl" class="org.mybatis.spring.mapper.MapperFactoryBean">
        <property name="mapperInterface" value="za.co.ipay.metermng.mybatis.generated.mapper.AuxAccountMapper" />
        <property name="sqlSessionFactory" ref="sqlSessionFactory" />
    </bean>
    <bean id="auxAccountHistMapperImpl" class="org.mybatis.spring.mapper.MapperFactoryBean">
        <property name="mapperInterface" value="za.co.ipay.metermng.mybatis.generated.mapper.AuxAccountHistMapper" />
        <property name="sqlSessionFactory" ref="sqlSessionFactory" />
    </bean>
    <bean id="auxAccountMapper" factory-bean="meterMngHistoryProxyFactory" factory-method="createProxy">
        <constructor-arg ref="auxAccountMapperImpl"></constructor-arg>
        <constructor-arg ref="auxAccountHistMapperImpl"></constructor-arg>
    </bean>

    <!-- AuxSupportMapper -->
    <bean id="auxSupportMapper" class="org.mybatis.spring.mapper.MapperFactoryBean">
        <property name="mapperInterface" value="za.co.ipay.metermng.mybatis.custom.mapper.AuxSupportMapper" />
        <property name="sqlSessionFactory" ref="sqlSessionFactory" />
    </bean>
    <!-- UpGenGroupLnkMapper -->
    <bean id="upGenGroupLnkMapperImpl" class="org.mybatis.spring.mapper.MapperFactoryBean">
        <property name="mapperInterface" value="za.co.ipay.metermng.mybatis.generated.mapper.UpGenGroupLnkMapper" />
        <property name="sqlSessionFactory" ref="sqlSessionFactory" />
    </bean>
    <bean id="upGenGroupLnkHistMapperImpl" class="org.mybatis.spring.mapper.MapperFactoryBean">
        <property name="mapperInterface" value="za.co.ipay.metermng.mybatis.generated.mapper.UpGenGroupLnkHistMapper" />
        <property name="sqlSessionFactory" ref="sqlSessionFactory" />
    </bean>
    <bean id="upGenGroupLnkMapper" factory-bean="meterMngHistoryProxyFactory" factory-method="createProxy">
        <constructor-arg ref="upGenGroupLnkMapperImpl"></constructor-arg>
        <constructor-arg ref="upGenGroupLnkHistMapperImpl"></constructor-arg>
    </bean>


    <bean id="tariffClassMapper" class="org.mybatis.spring.mapper.MapperFactoryBean">
        <property name="mapperInterface" value="za.co.ipay.metermng.mybatis.generated.mapper.TariffClassMapper" />
        <property name="sqlSessionFactory" ref="sqlSessionFactory" />
    </bean>

    <!-- Tariff mapper -->
    <bean id="tariffMapperImpl" class="org.mybatis.spring.mapper.MapperFactoryBean">
        <property name="mapperInterface" value="za.co.ipay.metermng.mybatis.generated.mapper.TariffMapper" />
        <property name="sqlSessionFactory" ref="sqlSessionFactory" />
    </bean>
    <bean id="tariffHistMapperImpl" class="org.mybatis.spring.mapper.MapperFactoryBean">
        <property name="mapperInterface" value="za.co.ipay.metermng.mybatis.generated.mapper.TariffHistMapper" />
        <property name="sqlSessionFactory" ref="sqlSessionFactory" />
    </bean>
    <bean id="tariffMapper" factory-bean="meterMngHistoryProxyFactory" factory-method="createProxy">
        <constructor-arg ref="tariffMapperImpl"></constructor-arg>
        <constructor-arg ref="tariffHistMapperImpl"></constructor-arg>
    </bean>

    <bean id="customerTransMapper" class="org.mybatis.spring.mapper.MapperFactoryBean">
        <property name="mapperInterface" value="za.co.ipay.metermng.mybatis.generated.mapper.CustomerTransMapper" />
        <property name="sqlSessionFactory" ref="sqlSessionFactory" />
    </bean>

    <bean id="customerTransItemMapper" class="org.mybatis.spring.mapper.MapperFactoryBean">
        <property name="mapperInterface" value="za.co.ipay.metermng.mybatis.generated.mapper.CustomerTransItemMapper" />
        <property name="sqlSessionFactory" ref="sqlSessionFactory" />
    </bean>

    <bean id="customerTransTypeMapper" class="org.mybatis.spring.mapper.MapperFactoryBean">
        <property name="mapperInterface" value="za.co.ipay.metermng.mybatis.generated.mapper.CustomerTransTypeMapper" />
        <property name="sqlSessionFactory" ref="sqlSessionFactory" />
    </bean>

    <bean id="customerTransExtraMapper" class="org.mybatis.spring.mapper.MapperFactoryBean">
        <property name="mapperInterface" value="za.co.ipay.metermng.mybatis.generated.mapper.CustomerTransExtraMapper" />
        <property name="sqlSessionFactory" ref="sqlSessionFactory" />
    </bean>

    <bean id="customerTransCustomMapper" class="org.mybatis.spring.mapper.MapperFactoryBean">
        <property name="mapperInterface" value="za.co.ipay.metermng.mybatis.custom.mapper.CustomerTransCustomMapper" />
        <property name="sqlSessionFactory" ref="sqlSessionFactory" />
    </bean>

    <bean id="stsEngineeringTokenMapper" class="org.mybatis.spring.mapper.MapperFactoryBean">
        <property name="mapperInterface" value="za.co.ipay.metermng.mybatis.generated.mapper.StsEngineeringTokenMapper" />
        <property name="sqlSessionFactory" ref="sqlSessionFactory" />
    </bean>

    <bean id="stsEngTokenTypeMapper" class="org.mybatis.spring.mapper.MapperFactoryBean">
        <property name="mapperInterface" value="za.co.ipay.metermng.mybatis.generated.mapper.StsEngTokenTypeMapper" />
        <property name="sqlSessionFactory" ref="sqlSessionFactory" />
    </bean>

    <bean id="transItemTypeMapper" class="org.mybatis.spring.mapper.MapperFactoryBean">
        <property name="mapperInterface" value="za.co.ipay.metermng.mybatis.generated.mapper.TransItemTypeMapper" />
        <property name="sqlSessionFactory" ref="sqlSessionFactory" />
    </bean>

    <bean id="usagePointHistoryMapper" class="org.mybatis.spring.mapper.MapperFactoryBean">
        <property name="mapperInterface" value="za.co.ipay.metermng.server.mybatis.mapper.IUsagePointHistoryMapper" />
        <property name="sqlSessionFactory" ref="sqlSessionFactory" />
    </bean>

    <bean id="stsAlgorithmMapper" class="org.mybatis.spring.mapper.MapperFactoryBean">
        <property name="mapperInterface" value="za.co.ipay.metermng.mybatis.generated.mapper.StsAlgorithmMapper" />
        <property name="sqlSessionFactory" ref="sqlSessionFactory" />
    </bean>

    <bean id="stsTokenTechMapper" class="org.mybatis.spring.mapper.MapperFactoryBean">
        <property name="mapperInterface" value="za.co.ipay.metermng.mybatis.generated.mapper.StsTokenTechMapper" />
        <property name="sqlSessionFactory" ref="sqlSessionFactory" />
    </bean>

    <bean id="customerKindMapper" class="org.mybatis.spring.mapper.MapperFactoryBean">
        <property name="mapperInterface" value="za.co.ipay.metermng.mybatis.generated.mapper.CustomerKindMapper" />
        <property name="sqlSessionFactory" ref="sqlSessionFactory" />
    </bean>

    <!-- Sts Supply Group Mapper -->
    <bean id="stsSupplyGroupMapperImpl" class="org.mybatis.spring.mapper.MapperFactoryBean">
        <property name="mapperInterface" value="za.co.ipay.metermng.mybatis.generated.mapper.StsSupplyGroupMapper" />
        <property name="sqlSessionFactory" ref="sqlSessionFactory" />
    </bean>
    <!-- Sts Supply Group History mapper -->
    <bean id="stsSupplyGroupHistMapper" class="org.mybatis.spring.mapper.MapperFactoryBean">
        <property name="mapperInterface" value="za.co.ipay.metermng.mybatis.generated.mapper.StsSupplyGroupHistMapper" />
        <property name="sqlSessionFactory" ref="sqlSessionFactory" />
    </bean>
    <!-- Combo Supply Group Mapper -->
    <bean id="stsSupplyGroupMapper" factory-bean="meterMngHistoryProxyFactory" factory-method="createProxy">
        <constructor-arg ref="stsSupplyGroupMapperImpl"></constructor-arg>
        <constructor-arg ref="stsSupplyGroupHistMapper"></constructor-arg>
    </bean>

    <!-- Aux Type Mapper -->
    <bean id="auxTypeMapperImpl" class="org.mybatis.spring.mapper.MapperFactoryBean">
        <property name="mapperInterface" value="za.co.ipay.metermng.mybatis.generated.mapper.AuxTypeMapper" />
        <property name="sqlSessionFactory" ref="sqlSessionFactory" />
    </bean>
    <bean id="auxTypeHistMapper" class="org.mybatis.spring.mapper.MapperFactoryBean">
        <property name="mapperInterface" value="za.co.ipay.metermng.mybatis.generated.mapper.AuxTypeHistMapper" />
        <property name="sqlSessionFactory" ref="sqlSessionFactory" />
    </bean>
    <bean id="auxTypeMapper" factory-bean="meterMngHistoryProxyFactory" factory-method="createProxy">
        <constructor-arg ref="auxTypeMapperImpl"></constructor-arg>
        <constructor-arg ref="auxTypeHistMapper"></constructor-arg>
    </bean>


    <!-- Group Type Mapper -->
    <bean id="groupTypeMapperImpl" class="org.mybatis.spring.mapper.MapperFactoryBean">
        <property name="mapperInterface" value="za.co.ipay.metermng.mybatis.generated.mapper.GroupTypeMapper" />
        <property name="sqlSessionFactory" ref="sqlSessionFactory" />
    </bean>
    <bean id="groupTypeHistMapperImpl" class="org.mybatis.spring.mapper.MapperFactoryBean">
        <property name="mapperInterface" value="za.co.ipay.metermng.mybatis.generated.mapper.GroupTypeHistMapper" />
        <property name="sqlSessionFactory" ref="sqlSessionFactory" />
    </bean>
    <bean id="groupTypeMapper" factory-bean="meterMngHistoryProxyFactory" factory-method="createProxy">
        <constructor-arg ref="groupTypeMapperImpl"></constructor-arg>
        <constructor-arg ref="groupTypeHistMapperImpl"></constructor-arg>
    </bean>


    <bean id="meterTypeMapper" class="org.mybatis.spring.mapper.MapperFactoryBean">
        <property name="mapperInterface" value="za.co.ipay.metermng.mybatis.generated.mapper.MeterTypeMapper" />
        <property name="sqlSessionFactory" ref="sqlSessionFactory" />
    </bean>

    <bean id="iGroupTypeMapper" class="org.mybatis.spring.mapper.MapperFactoryBean">
        <property name="mapperInterface" value="za.co.ipay.metermng.mybatis.custom.mapper.IGroupTypeMapper" />
        <property name="sqlSessionFactory" ref="sqlSessionFactory" />
    </bean>


    <!-- Group Hierarchy Mapper -->
    <bean id="groupHierarchyMapperImpl" class="org.mybatis.spring.mapper.MapperFactoryBean">
        <property name="mapperInterface" value="za.co.ipay.metermng.mybatis.generated.mapper.GroupHierarchyMapper" />
        <property name="sqlSessionFactory" ref="sqlSessionFactory" />
    </bean>
    <bean id="groupHierarchyHistMapperImpl" class="org.mybatis.spring.mapper.MapperFactoryBean">
        <property name="mapperInterface" value="za.co.ipay.metermng.mybatis.generated.mapper.GroupHierarchyHistMapper" />
        <property name="sqlSessionFactory" ref="sqlSessionFactory" />
    </bean>
    <bean id="groupHierarchyMapper" factory-bean="meterMngHistoryProxyFactory" factory-method="createProxy">
        <constructor-arg ref="groupHierarchyMapperImpl"></constructor-arg>
        <constructor-arg ref="groupHierarchyHistMapperImpl"></constructor-arg>
    </bean>

    <bean id="groupHierarchyCustomMapper" class="org.mybatis.spring.mapper.MapperFactoryBean">
        <property name="mapperInterface" value="za.co.ipay.metermng.mybatis.custom.mapper.GroupHierarchyCustomMapper" />
        <property name="sqlSessionFactory" ref="sqlSessionFactory" />
    </bean>


    <!-- Group Entity -->
    <bean id="groupEntityMapperImpl" class="org.mybatis.spring.mapper.MapperFactoryBean">
        <property name="mapperInterface" value="za.co.ipay.metermng.mybatis.generated.mapper.GroupEntityMapper" />
        <property name="sqlSessionFactory" ref="sqlSessionFactory" />
    </bean>
    <bean id="groupEntityHistMapperImpl" class="org.mybatis.spring.mapper.MapperFactoryBean">
        <property name="mapperInterface" value="za.co.ipay.metermng.mybatis.generated.mapper.GroupEntityHistMapper" />
        <property name="sqlSessionFactory" ref="sqlSessionFactory" />
    </bean>
    <bean id="groupEntityMapper" factory-bean="meterMngHistoryProxyFactory" factory-method="createProxy">
        <constructor-arg ref="groupEntityMapperImpl"></constructor-arg>
        <constructor-arg ref="groupEntityHistMapperImpl"></constructor-arg>
    </bean>
    <bean id="groupEntityCustomMapper" class="org.mybatis.spring.mapper.MapperFactoryBean">
        <property name="mapperInterface" value="za.co.ipay.metermng.mybatis.custom.mapper.GroupEntityCustomMapper" />
        <property name="sqlSessionFactory" ref="sqlSessionFactory" />
    </bean>

    <!-- Cust Acc Thresholds Mapper -->
    <bean id="customerAccThresholdsMapperImpl" class="org.mybatis.spring.mapper.MapperFactoryBean">
        <property name="mapperInterface" value="za.co.ipay.metermng.mybatis.generated.mapper.CustomerAccThresholdsMapper" />
        <property name="sqlSessionFactory" ref="sqlSessionFactory" />
    </bean>
    <bean id="customerAccThresholdsHistMapperImpl" class="org.mybatis.spring.mapper.MapperFactoryBean">
        <property name="mapperInterface" value="za.co.ipay.metermng.mybatis.generated.mapper.CustAccThresholdsHistMapper" />
        <property name="sqlSessionFactory" ref="sqlSessionFactory" />
    </bean>
    <bean id="customerAccThresholdsMapper" factory-bean="meterMngHistoryProxyFactory" factory-method="createProxy">
        <constructor-arg ref="customerAccThresholdsMapperImpl"></constructor-arg>
        <constructor-arg ref="customerAccThresholdsHistMapperImpl"></constructor-arg>
    </bean>


    <bean id="customerAgreementAuxAccountMapper" class="org.mybatis.spring.mapper.MapperFactoryBean">
        <property name="mapperInterface" value="za.co.ipay.metermng.server.mybatis.mapper.ICustomerAgreementAuxAccntMapper" />
        <property name="sqlSessionFactory" ref="sqlSessionFactory" />
    </bean>

    <bean id="vendMapper" class="org.mybatis.spring.mapper.MapperFactoryBean">
        <property name="mapperInterface" value="za.co.ipay.metermng.mybatis.custom.mapper.VendMapper" />
        <property name="sqlSessionFactory" ref="sqlSessionFactory" />
    </bean>


    <!-- End Device Store Mapper -->
    <bean id="endDeviceStoreMapperImpl" class="org.mybatis.spring.mapper.MapperFactoryBean">
        <property name="mapperInterface" value="za.co.ipay.metermng.mybatis.generated.mapper.EndDeviceStoreMapper" />
        <property name="sqlSessionFactory" ref="sqlSessionFactory" />
    </bean>
    <bean id="endDeviceStoreHistMapperImpl" class="org.mybatis.spring.mapper.MapperFactoryBean">
        <property name="mapperInterface" value="za.co.ipay.metermng.mybatis.generated.mapper.EndDeviceStoreHistMapper" />
        <property name="sqlSessionFactory" ref="sqlSessionFactory" />
    </bean>
    <bean id="endDeviceStoreMapperHistProxy" factory-bean="meterMngHistoryProxyFactory" factory-method="createProxy">
        <constructor-arg ref="endDeviceStoreMapperImpl"></constructor-arg>
        <constructor-arg ref="endDeviceStoreHistMapperImpl"></constructor-arg>
    </bean>
    <bean id="endDeviceStoreMapper" class="za.co.ipay.metermng.mybatis.sessiongroup.EndDeviceStoreMapperStaticProxy">
        <constructor-arg ref="endDeviceStoreMapperHistProxy"></constructor-arg>
        <constructor-arg ref="currentUserNameProvider"></constructor-arg>
    </bean>
    
     <!-- Cust Acc Notify Mapper -->
    <bean id="custAccNotifyMapperImpl" class="org.mybatis.spring.mapper.MapperFactoryBean">
        <property name="mapperInterface" value="za.co.ipay.metermng.mybatis.generated.mapper.CustAccNotifyMapper" />
        <property name="sqlSessionFactory" ref="sqlSessionFactory" />
    </bean>
    <bean id="custAccNotifyHistMapperImpl" class="org.mybatis.spring.mapper.MapperFactoryBean">
        <property name="mapperInterface" value="za.co.ipay.metermng.mybatis.generated.mapper.CustAccNotifyHistMapper" />
        <property name="sqlSessionFactory" ref="sqlSessionFactory" />
    </bean>
    <bean id="custAccNotifyMapper" factory-bean="meterMngHistoryProxyFactory" factory-method="createProxy">
        <constructor-arg ref="custAccNotifyMapperImpl"></constructor-arg>
        <constructor-arg ref="custAccNotifyHistMapperImpl"></constructor-arg>
    </bean>

    <!-- User Group Mapper -->
    <bean id="userGroupMapperImpl" class="org.mybatis.spring.mapper.MapperFactoryBean">
        <property name="mapperInterface" value="za.co.ipay.metermng.mybatis.generated.mapper.UserGroupMapper" />
        <property name="sqlSessionFactory" ref="sqlSessionFactory" />
    </bean>
    <bean id="userGroupHistMapperImpl" class="org.mybatis.spring.mapper.MapperFactoryBean">
        <property name="mapperInterface" value="za.co.ipay.metermng.mybatis.generated.mapper.UserGroupHistMapper" />
        <property name="sqlSessionFactory" ref="sqlSessionFactory" />
    </bean>
    <bean id="userGroupMapper" factory-bean="meterMngHistoryProxyFactory" factory-method="createProxy">
        <constructor-arg ref="userGroupMapperImpl"></constructor-arg>
        <constructor-arg ref="userGroupHistMapperImpl"></constructor-arg>
    </bean>


    <bean id="touMapper" class="org.mybatis.spring.mapper.MapperFactoryBean">
        <property name="mapperInterface" value="za.co.ipay.metermng.mybatis.custom.mapper.TouMapper" />
        <property name="sqlSessionFactory" ref="sqlSessionFactory" />
    </bean>

    <bean id="touSeasonMapper" class="org.mybatis.spring.mapper.MapperFactoryBean">
        <property name="mapperInterface" value="za.co.ipay.metermng.mybatis.generated.mapper.TouSeasonMapper" />
        <property name="sqlSessionFactory" ref="sqlSessionFactory" />
    </bean>

    <bean id="touPeriodMapper" class="org.mybatis.spring.mapper.MapperFactoryBean">
        <property name="mapperInterface" value="za.co.ipay.metermng.mybatis.generated.mapper.TouPeriodMapper" />
        <property name="sqlSessionFactory" ref="sqlSessionFactory" />
    </bean>

    <bean id="touCalendarMapper" class="org.mybatis.spring.mapper.MapperFactoryBean">
        <property name="mapperInterface" value="za.co.ipay.metermng.mybatis.generated.mapper.TouCalendarMapper" />
        <property name="sqlSessionFactory" ref="sqlSessionFactory" />
    </bean>

    <bean id="calendarSuggestionMapper" class="org.mybatis.spring.mapper.MapperFactoryBean">
        <property name="mapperInterface" value="za.co.ipay.metermng.server.mybatis.mapper.ICalendarSuggestionMapper" />
        <property name="sqlSessionFactory" ref="sqlSessionFactory" />
    </bean>

    <bean id="touDayProfileMapper" class="org.mybatis.spring.mapper.MapperFactoryBean">
        <property name="mapperInterface" value="za.co.ipay.metermng.mybatis.generated.mapper.TouDayProfileMapper" />
        <property name="sqlSessionFactory" ref="sqlSessionFactory" />
    </bean>

    <bean id="touDayProfileTimeMapper" class="org.mybatis.spring.mapper.MapperFactoryBean">
        <property name="mapperInterface" value="za.co.ipay.metermng.mybatis.generated.mapper.TouDayProfileTimeMapper" />
        <property name="sqlSessionFactory" ref="sqlSessionFactory" />
    </bean>

    <bean id="touCalendarSeasonMapper" class="org.mybatis.spring.mapper.MapperFactoryBean">
        <property name="mapperInterface" value="za.co.ipay.metermng.mybatis.generated.mapper.TouCalendarSeasonMapper" />
        <property name="sqlSessionFactory" ref="sqlSessionFactory" />
    </bean>

    <bean id="touSpecialDayMapper" class="org.mybatis.spring.mapper.MapperFactoryBean">
        <property name="mapperInterface" value="za.co.ipay.metermng.mybatis.generated.mapper.TouSpecialDayMapper" />
        <property name="sqlSessionFactory" ref="sqlSessionFactory" />
    </bean>

    <bean id="touSeasonDateMapper" class="org.mybatis.spring.mapper.MapperFactoryBean">
        <property name="mapperInterface" value="za.co.ipay.metermng.mybatis.generated.mapper.TouSeasonDateMapper" />
        <property name="sqlSessionFactory" ref="sqlSessionFactory" />
    </bean>


    <!-- Manufacturer mapper -->
    <bean id="manufacturerMapperImpl" class="org.mybatis.spring.mapper.MapperFactoryBean">
        <property name="mapperInterface" value="za.co.ipay.metermng.mybatis.generated.mapper.ManufacturerMapper" />
        <property name="sqlSessionFactory" ref="sqlSessionFactory" />
    </bean>
    <bean id="manufacturerHistMapper" class="org.mybatis.spring.mapper.MapperFactoryBean">
        <property name="mapperInterface" value="za.co.ipay.metermng.mybatis.generated.mapper.ManufacturerHistMapper" />
        <property name="sqlSessionFactory" ref="sqlSessionFactory" />
    </bean>
    <bean id="manufacturerMapper" factory-bean="meterMngHistoryProxyFactory" factory-method="createProxy">
        <constructor-arg ref="manufacturerMapperImpl"></constructor-arg>
        <constructor-arg ref="manufacturerHistMapper"></constructor-arg>
    </bean>


    <!-- Mdc mapper -->
    <bean id="mdcMapperImpl" class="org.mybatis.spring.mapper.MapperFactoryBean">
        <property name="mapperInterface" value="za.co.ipay.metermng.mybatis.generated.mapper.MdcMapper" />
        <property name="sqlSessionFactory" ref="sqlSessionFactory" />
    </bean>
    <bean id="mdcHistMapper" class="org.mybatis.spring.mapper.MapperFactoryBean">
        <property name="mapperInterface" value="za.co.ipay.metermng.mybatis.generated.mapper.MdcHistMapper" />
        <property name="sqlSessionFactory" ref="sqlSessionFactory" />
    </bean>
    <bean id="mdcMapper" factory-bean="meterMngHistoryProxyFactory" factory-method="createProxy">
        <constructor-arg ref="mdcMapperImpl"></constructor-arg>
        <constructor-arg ref="mdcHistMapper"></constructor-arg>
    </bean>


    <!-- Mdc Channelmapper -->
    <bean id="mdcChannelMapperImpl" class="org.mybatis.spring.mapper.MapperFactoryBean">
        <property name="mapperInterface" value="za.co.ipay.metermng.mybatis.generated.mapper.MdcChannelMapper" />
        <property name="sqlSessionFactory" ref="sqlSessionFactory" />
    </bean>
    <bean id="mdcChannelHistMapper" class="org.mybatis.spring.mapper.MapperFactoryBean">
        <property name="mapperInterface" value="za.co.ipay.metermng.mybatis.generated.mapper.MdcChannelHistMapper" />
        <property name="sqlSessionFactory" ref="sqlSessionFactory" />
    </bean>
    <bean id="mdcChannelMapper" factory-bean="meterMngHistoryProxyFactory" factory-method="createProxy">
        <constructor-arg ref="mdcChannelMapperImpl"></constructor-arg>
        <constructor-arg ref="mdcChannelHistMapper"></constructor-arg>
    </bean>

    <!-- MdcChannelCustomMapper -->
    <bean id="mdcChannelCustomMapper" class="org.mybatis.spring.mapper.MapperFactoryBean">
        <property name="mapperInterface" value="za.co.ipay.metermng.mybatis.custom.mapper.MdcChannelCustomMapper" />
        <property name="sqlSessionFactory" ref="sqlSessionFactory" />
    </bean>

    <!-- ModelChannelConfigMapper -->
    <bean id="modelChannelConfigMapperImpl" class="org.mybatis.spring.mapper.MapperFactoryBean">
        <property name="mapperInterface" value="za.co.ipay.metermng.mybatis.generated.mapper.ModelChannelConfigMapper" />
        <property name="sqlSessionFactory" ref="sqlSessionFactory" />
    </bean>
    <bean id="modelChannelConfigHistMapper" class="org.mybatis.spring.mapper.MapperFactoryBean">
        <property name="mapperInterface" value="za.co.ipay.metermng.mybatis.generated.mapper.ModelChannelConfigHistMapper" />
        <property name="sqlSessionFactory" ref="sqlSessionFactory" />
    </bean>
    <bean id="modelChannelConfigMapper" factory-bean="meterMngHistoryProxyFactory" factory-method="createProxy">
        <constructor-arg ref="modelChannelConfigMapperImpl"></constructor-arg>
        <constructor-arg ref="modelChannelConfigHistMapper"></constructor-arg>
    </bean>

    <!-- TimeIntervalMapper -->
    <bean id="timeIntervalMapper" class="org.mybatis.spring.mapper.MapperFactoryBean">
        <property name="mapperInterface" value="za.co.ipay.metermng.mybatis.generated.mapper.TimeIntervalMapper" />
        <property name="sqlSessionFactory" ref="sqlSessionFactory" />
    </bean>

    <!-- MdcChanBillDetMapper -->
    <bean id="mdcChanBillDetMapper" class="org.mybatis.spring.mapper.MapperFactoryBean">
        <property name="mapperInterface" value="za.co.ipay.metermng.mybatis.generated.mapper.MdcChanBillDetMapper" />
        <property name="sqlSessionFactory" ref="sqlSessionFactory" />
    </bean>

    <!-- MeterModel mapper -->
    <bean id="meterModelMapperImpl" class="org.mybatis.spring.mapper.MapperFactoryBean">
        <property name="mapperInterface" value="za.co.ipay.metermng.mybatis.generated.mapper.MeterModelMapper" />
        <property name="sqlSessionFactory" ref="sqlSessionFactory" />
    </bean>
    <bean id="meterModelHistMapper" class="org.mybatis.spring.mapper.MapperFactoryBean">
        <property name="mapperInterface" value="za.co.ipay.metermng.mybatis.generated.mapper.MeterModelHistMapper" />
        <property name="sqlSessionFactory" ref="sqlSessionFactory" />
    </bean>
    <bean id="meterModelMapper" factory-bean="meterMngHistoryProxyFactory" factory-method="createProxy">
        <constructor-arg ref="meterModelMapperImpl"></constructor-arg>
        <constructor-arg ref="meterModelHistMapper"></constructor-arg>
    </bean>


    <!-- PaymentMode mapper -->
    <bean id="paymentModeMapper" class="org.mybatis.spring.mapper.MapperFactoryBean">
        <property name="mapperInterface" value="za.co.ipay.metermng.mybatis.generated.mapper.PaymentModeMapper" />
        <property name="sqlSessionFactory" ref="sqlSessionFactory" />
    </bean>

    <!-- DeviceEventType mapper -->
    <bean id="deviceEventTypeMapper" class="org.mybatis.spring.mapper.MapperFactoryBean">
        <property name="mapperInterface" value="za.co.ipay.metermng.mybatis.generated.mapper.DeviceEventTypeMapper" />
        <property name="sqlSessionFactory" ref="sqlSessionFactory" />
    </bean>

    <!-- ServiceResource mapper -->
    <bean id="serviceResourceMapper" class="org.mybatis.spring.mapper.MapperFactoryBean">
        <property name="mapperInterface" value="za.co.ipay.metermng.mybatis.generated.mapper.ServiceResourceMapper" />
        <property name="sqlSessionFactory" ref="sqlSessionFactory" />
    </bean>

    <!-- ModelSupportsPayMode mapper -->
    <bean id="modelSupportsPayModeMapper" class="org.mybatis.spring.mapper.MapperFactoryBean">
        <property name="mapperInterface" value="za.co.ipay.metermng.mybatis.generated.mapper.ModelSupportsPayModeMapper" />
        <property name="sqlSessionFactory" ref="sqlSessionFactory" />
    </bean>

    <!-- meterModelCustomMapper mapper -->
    <bean id="meterModelCustomMapper" class="org.mybatis.spring.mapper.MapperFactoryBean">
        <property name="mapperInterface" value="za.co.ipay.metermng.mybatis.custom.mapper.MeterModelCustomMapper" />
        <property name="sqlSessionFactory" ref="sqlSessionFactory" />
    </bean>

    <!-- MeterReadingTypeMapper -->
    <bean id="meterReadingTypeMapper" class="org.mybatis.spring.mapper.MapperFactoryBean">
        <property name="mapperInterface" value="za.co.ipay.metermng.mybatis.generated.mapper.MeterReadingTypeMapper" />
        <property name="sqlSessionFactory" ref="sqlSessionFactory" />
    </bean>

    <!-- CycleMapper -->
    <bean id="cycleMapper" class="org.mybatis.spring.mapper.MapperFactoryBean">
        <property name="mapperInterface" value="za.co.ipay.metermng.mybatis.generated.mapper.CycleMapper" />
        <property name="sqlSessionFactory" ref="sqlSessionFactory" />
    </bean>

    <!-- TouTariffCalendarMapper -->
    <bean id="touTariffCalendarMapper" class="org.mybatis.spring.mapper.MapperFactoryBean">
        <property name="mapperInterface" value="za.co.ipay.metermng.mybatis.generated.mapper.TouTariffCalendarMapper" />
        <property name="sqlSessionFactory" ref="sqlSessionFactory" />
    </bean>

    <!-- CalendarCustomMapper -->
    <bean id="calendarCustomMapper" class="org.mybatis.spring.mapper.MapperFactoryBean">
        <property name="mapperInterface" value="za.co.ipay.metermng.mybatis.custom.mapper.CalendarCustomMapper" />
        <property name="sqlSessionFactory" ref="sqlSessionFactory" />
    </bean>

    <!-- TouSeasonChgMapper -->
    <bean id="touSeasonChgMapper" class="org.mybatis.spring.mapper.MapperFactoryBean">
        <property name="mapperInterface" value="za.co.ipay.metermng.mybatis.generated.mapper.TouSeasonChgMapper" />
        <property name="sqlSessionFactory" ref="sqlSessionFactory" />
    </bean>

    <!-- ReadingQualityMapper -->
    <bean id="readingQualityMapper" class="org.mybatis.spring.mapper.MapperFactoryBean">
        <property name="mapperInterface" value="za.co.ipay.metermng.mybatis.generated.mapper.ReadingQualityMapper" />
        <property name="sqlSessionFactory" ref="sqlSessionFactory" />
    </bean>

    <!-- ReadingQualityCustomMapper -->
    <bean id="readingQualityCustomMapper" class="org.mybatis.spring.mapper.MapperFactoryBean">
        <property name="mapperInterface" value="za.co.ipay.metermng.mybatis.custom.mapper.ReadingQualityCustomMapper" />
        <property name="sqlSessionFactory" ref="sqlSessionFactory" />
    </bean>

    <!-- MeterBalancingMapper -->
    <bean id="meterBalancingMapper" class="org.mybatis.spring.mapper.MapperFactoryBean">
        <property name="mapperInterface" value="za.co.ipay.metermng.mybatis.generated.mapper.MeterBalancingMapper" />
        <property name="sqlSessionFactory" ref="sqlSessionFactory" />
    </bean>

    <!-- MeterBalancingCustomMapper -->
    <bean id="meterBalancingCustomMapper" class="org.mybatis.spring.mapper.MapperFactoryBean">
        <property name="mapperInterface" value="za.co.ipay.metermng.mybatis.custom.mapper.MeterBalancingCustomMapper" />
        <property name="sqlSessionFactory" ref="sqlSessionFactory" />
    </bean>

    <!-- MdcTransMapper -->
    <bean id="mdcTransMapper" class="org.mybatis.spring.mapper.MapperFactoryBean">
        <property name="mapperInterface" value="za.co.ipay.metermng.mybatis.generated.mapper.MdcTransMapper" />
        <property name="sqlSessionFactory" ref="sqlSessionFactory" />
    </bean>

    <!-- BillingDet mapper -->
    <bean id="billingDetMapperImpl" class="org.mybatis.spring.mapper.MapperFactoryBean">
        <property name="mapperInterface" value="za.co.ipay.metermng.mybatis.generated.mapper.BillingDetMapper" />
        <property name="sqlSessionFactory" ref="sqlSessionFactory" />
    </bean>
    <bean id="billingDetHistMapper" class="org.mybatis.spring.mapper.MapperFactoryBean">
        <property name="mapperInterface" value="za.co.ipay.metermng.mybatis.generated.mapper.BillingDetHistMapper" />
        <property name="sqlSessionFactory" ref="sqlSessionFactory" />
    </bean>
    <bean id="billingDetMapper" factory-bean="meterMngHistoryProxyFactory" factory-method="createProxy">
        <constructor-arg ref="billingDetMapperImpl"></constructor-arg>
        <constructor-arg ref="billingDetHistMapper"></constructor-arg>
    </bean>
    
    <!-- BillingDetAppliesLnk mapper -->
    <bean id="billingDetAppliesLnkMapperImpl" class="org.mybatis.spring.mapper.MapperFactoryBean">
        <property name="mapperInterface" value="za.co.ipay.metermng.mybatis.generated.mapper.BillingDetAppliesLnkMapper" />
        <property name="sqlSessionFactory" ref="sqlSessionFactory" />
    </bean>
    <bean id="billingDetAppliesLnkHistMapper" class="org.mybatis.spring.mapper.MapperFactoryBean">
        <property name="mapperInterface" value="za.co.ipay.metermng.mybatis.generated.mapper.BillingDetAppliesLnkHistMapper" />
        <property name="sqlSessionFactory" ref="sqlSessionFactory" />
    </bean>
    <bean id="billingDetAppliesLnkMapper" factory-bean="meterMngHistoryProxyFactory" factory-method="createProxy">
        <constructor-arg ref="billingDetAppliesLnkMapperImpl"></constructor-arg>
        <constructor-arg ref="billingDetAppliesLnkHistMapper"></constructor-arg>
    </bean>
    <bean id="billingDetAppliesLnkSupportMapper" class="org.mybatis.spring.mapper.MapperFactoryBean">
        <property name="mapperInterface" value="za.co.ipay.metermng.mybatis.custom.mapper.BillingDetAppliesLnkSupportMapper" />
        <property name="sqlSessionFactory" ref="sqlSessionFactory" />
    </bean>

    <!-- AppSetting mapper -->
    <bean id="appSettingMapperImpl" class="org.mybatis.spring.mapper.MapperFactoryBean">
        <property name="mapperInterface" value="za.co.ipay.metermng.mybatis.generated.mapper.AppSettingMapper" />
        <property name="sqlSessionFactory" ref="sqlSessionFactory" />
    </bean>
    <bean id="appSettingHistMapper" class="org.mybatis.spring.mapper.MapperFactoryBean">
        <property name="mapperInterface" value="za.co.ipay.metermng.mybatis.generated.mapper.AppSettingHistMapper" />
        <property name="sqlSessionFactory" ref="sqlSessionFactory" />
    </bean>
    <bean id="appSettingMapper" factory-bean="meterMngHistoryProxyFactory" factory-method="createProxy">
        <constructor-arg ref="appSettingMapperImpl"></constructor-arg>
        <constructor-arg ref="appSettingHistMapper"></constructor-arg>
    </bean>

    <!-- AccountTrans mapper -->
    <bean id="accountTransMapper" class="org.mybatis.spring.mapper.MapperFactoryBean">
        <property name="mapperInterface" value="za.co.ipay.metermng.mybatis.generated.mapper.AccountTransMapper" />
        <property name="sqlSessionFactory" ref="sqlSessionFactory" />
    </bean>

    <!-- AccountTransTYPE mapper -->
    <bean id="accountTransTypeMapper" class="org.mybatis.spring.mapper.MapperFactoryBean">
        <property name="mapperInterface" value="za.co.ipay.metermng.mybatis.generated.mapper.AccountTransTypeMapper" />
        <property name="sqlSessionFactory" ref="sqlSessionFactory" />
    </bean>


    <!-- TaskScheduleMapper -->
    <bean id="taskScheduleMapperImpl" class="org.mybatis.spring.mapper.MapperFactoryBean">
        <property name="mapperInterface" value="za.co.ipay.metermng.mybatis.generated.mapper.TaskScheduleMapper" />
        <property name="sqlSessionFactory" ref="sqlSessionFactory" />
    </bean>
    <bean id="taskScheduleHistMapper" class="org.mybatis.spring.mapper.MapperFactoryBean">
        <property name="mapperInterface" value="za.co.ipay.metermng.mybatis.generated.mapper.TaskScheduleHistMapper" />
        <property name="sqlSessionFactory" ref="sqlSessionFactory" />
    </bean>
    <bean id="taskScheduleMapper" factory-bean="meterMngHistoryProxyFactory" factory-method="createProxy">
        <constructor-arg ref="taskScheduleMapperImpl"></constructor-arg>
        <constructor-arg ref="taskScheduleHistMapper"></constructor-arg>
    </bean>


    <!-- ScheduledTaskMapper -->
    <bean id="scheduledTaskMapperImpl" class="org.mybatis.spring.mapper.MapperFactoryBean">
        <property name="mapperInterface" value="za.co.ipay.metermng.mybatis.generated.mapper.ScheduledTaskMapper" />
        <property name="sqlSessionFactory" ref="sqlSessionFactory" />
    </bean>
    <bean id="scheduledTaskHistMapper" class="org.mybatis.spring.mapper.MapperFactoryBean">
        <property name="mapperInterface" value="za.co.ipay.metermng.mybatis.generated.mapper.ScheduledTaskHistMapper" />
        <property name="sqlSessionFactory" ref="sqlSessionFactory" />
    </bean>
    <bean id="scheduledTaskMapper" factory-bean="meterMngHistoryProxyFactory" factory-method="createProxy">
        <constructor-arg ref="scheduledTaskMapperImpl"></constructor-arg>
        <constructor-arg ref="scheduledTaskHistMapper"></constructor-arg>
    </bean>


    <!-- TaskClassMapper -->
    <bean id="taskClassMapper" class="org.mybatis.spring.mapper.MapperFactoryBean">
        <property name="mapperInterface" value="za.co.ipay.metermng.mybatis.generated.mapper.TaskClassMapper" />
        <property name="sqlSessionFactory" ref="sqlSessionFactory" />
    </bean>


    <!-- ScheduledTaskUserMapper -->
    <bean id="scheduledTaskUserMapperImpl" class="org.mybatis.spring.mapper.MapperFactoryBean">
        <property name="mapperInterface" value="za.co.ipay.metermng.mybatis.generated.mapper.ScheduledTaskUserMapper" />
        <property name="sqlSessionFactory" ref="sqlSessionFactory" />
    </bean>
    <bean id="scheduledTaskUserHistMapper" class="org.mybatis.spring.mapper.MapperFactoryBean">
        <property name="mapperInterface" value="za.co.ipay.metermng.mybatis.generated.mapper.ScheduledTaskUserHistMapper" />
        <property name="sqlSessionFactory" ref="sqlSessionFactory" />
    </bean>
    <bean id="scheduledTaskUserMapper" factory-bean="meterMngHistoryProxyFactory" factory-method="createProxy">
        <constructor-arg ref="scheduledTaskUserMapperImpl"></constructor-arg>
        <constructor-arg ref="scheduledTaskUserHistMapper"></constructor-arg>
    </bean>


    <bean id="customerAccountCustomMapper" class="org.mybatis.spring.mapper.MapperFactoryBean">
        <property name="mapperInterface" value="za.co.ipay.metermng.mybatis.custom.mapper.CustomerAccountCustomMapper" />
        <property name="sqlSessionFactory" ref="sqlSessionFactory" />
    </bean>

    <bean id="customerAccountHistMapper" class="org.mybatis.spring.mapper.MapperFactoryBean">
        <property name="mapperInterface" value="za.co.ipay.metermng.mybatis.generated.mapper.CustomerAccountHistMapper" />
        <property name="sqlSessionFactory" ref="sqlSessionFactory" />
    </bean>


    <!-- UpMeterInstallMapper -->
    <bean id="upMeterInstallMapperImpl" class="org.mybatis.spring.mapper.MapperFactoryBean">
        <property name="mapperInterface" value="za.co.ipay.metermng.mybatis.generated.mapper.UpMeterInstallMapper" />
        <property name="sqlSessionFactory" ref="sqlSessionFactory" />
    </bean>
    <bean id="upMeterInstallHistMapperImpl" class="org.mybatis.spring.mapper.MapperFactoryBean">
        <property name="mapperInterface" value="za.co.ipay.metermng.mybatis.generated.mapper.UpMeterInstallHistMapper" />
        <property name="sqlSessionFactory" ref="sqlSessionFactory" />
    </bean>
    <bean id="upMeterInstallMapper" factory-bean="meterMngHistoryProxyFactory" factory-method="createProxy">
        <constructor-arg ref="upMeterInstallMapperImpl"></constructor-arg>
        <constructor-arg ref="upMeterInstallHistMapperImpl"></constructor-arg>
    </bean>


    <bean id="registerReadingMapper" class="org.mybatis.spring.mapper.MapperFactoryBean">
        <property name="mapperInterface" value="za.co.ipay.metermng.mybatis.generated.mapper.RegisterReadingMapper" />
        <property name="sqlSessionFactory" ref="sqlSessionFactory" />
    </bean>

    <bean id="registerReadingCustomMapper" class="org.mybatis.spring.mapper.MapperFactoryBean">
        <property name="mapperInterface" value="za.co.ipay.metermng.mybatis.custom.mapper.RegisterReadingCustomMapper" />
        <property name="sqlSessionFactory" ref="sqlSessionFactory" />
    </bean>

    <!-- ITranslationAware -->
    <bean id="translationAware" class="za.co.ipay.utils.i18n.SpringMessageSourceTranslationAware" />

    <!-- TariffSupportMapper -->
    <bean id="tariffSupportMapper" class="org.mybatis.spring.mapper.MapperFactoryBean">
        <property name="mapperInterface" value="za.co.ipay.metermng.mybatis.custom.mapper.TariffSupportMapper" />
        <property name="sqlSessionFactory" ref="sqlSessionFactory" />
    </bean>

    <!-- EventNotificationMapper -->
    <bean id="eventNotificationMapper" class="org.mybatis.spring.mapper.MapperFactoryBean">
        <property name="mapperInterface" value="za.co.ipay.metermng.mybatis.generated.mapper.EventNotificationMapper" />
        <property name="sqlSessionFactory" ref="sqlSessionFactory" />
    </bean>

    <!-- EventNotificationTypeMapper -->
    <bean id="eventNotificationTypeMapper" class="org.mybatis.spring.mapper.MapperFactoryBean">
        <property name="mapperInterface" value="za.co.ipay.metermng.mybatis.generated.mapper.EventNotificationTypeMapper" />
        <property name="sqlSessionFactory" ref="sqlSessionFactory" />
    </bean>


    <!-- NdpScheduleMapper -->
    <bean id="ndpScheduleMapperImpl" class="org.mybatis.spring.mapper.MapperFactoryBean">
        <property name="mapperInterface" value="za.co.ipay.metermng.mybatis.generated.mapper.NdpScheduleMapper" />
        <property name="sqlSessionFactory" ref="sqlSessionFactory" />
    </bean>
    <bean id="ndpScheduleHistMapper" class="org.mybatis.spring.mapper.MapperFactoryBean">
        <property name="mapperInterface" value="za.co.ipay.metermng.mybatis.generated.mapper.NdpScheduleHistMapper" />
        <property name="sqlSessionFactory" ref="sqlSessionFactory" />
    </bean>
    <bean id="ndpScheduleMapper" factory-bean="meterMngHistoryProxyFactory" factory-method="createProxy">
        <constructor-arg ref="ndpScheduleMapperImpl"></constructor-arg>
        <constructor-arg ref="ndpScheduleHistMapper"></constructor-arg>
    </bean>


    <!-- NdpSeasonMapper -->
    <bean id="ndpSeasonMapperImpl" class="org.mybatis.spring.mapper.MapperFactoryBean">
        <property name="mapperInterface" value="za.co.ipay.metermng.mybatis.generated.mapper.NdpSeasonMapper" />
        <property name="sqlSessionFactory" ref="sqlSessionFactory" />
    </bean>
    <bean id="ndpSeasonHistMapper" class="org.mybatis.spring.mapper.MapperFactoryBean">
        <property name="mapperInterface" value="za.co.ipay.metermng.mybatis.generated.mapper.NdpSeasonHistMapper" />
        <property name="sqlSessionFactory" ref="sqlSessionFactory" />
    </bean>
    <bean id="ndpSeasonMapper" factory-bean="meterMngHistoryProxyFactory" factory-method="createProxy">
        <constructor-arg ref="ndpSeasonMapperImpl"></constructor-arg>
        <constructor-arg ref="ndpSeasonHistMapper"></constructor-arg>
    </bean>


    <!-- NdpDayProfileMapper -->
    <bean id="ndpDayProfileMapperImpl" class="org.mybatis.spring.mapper.MapperFactoryBean">
        <property name="mapperInterface" value="za.co.ipay.metermng.mybatis.generated.mapper.NdpDayProfileMapper" />
        <property name="sqlSessionFactory" ref="sqlSessionFactory" />
    </bean>
    <bean id="ndpDayProfileHistMapper" class="org.mybatis.spring.mapper.MapperFactoryBean">
        <property name="mapperInterface" value="za.co.ipay.metermng.mybatis.generated.mapper.NdpDayProfileHistMapper" />
        <property name="sqlSessionFactory" ref="sqlSessionFactory" />
    </bean>
    <bean id="ndpDayProfileMapper" factory-bean="meterMngHistoryProxyFactory" factory-method="createProxy">
        <constructor-arg ref="ndpDayProfileMapperImpl"></constructor-arg>
        <constructor-arg ref="ndpDayProfileHistMapper"></constructor-arg>
    </bean>


    <!-- NdpSpecialDayMapper -->
    <bean id="ndpSpecialDayMapperImpl" class="org.mybatis.spring.mapper.MapperFactoryBean">
        <property name="mapperInterface" value="za.co.ipay.metermng.mybatis.generated.mapper.NdpSpecialDayMapper" />
        <property name="sqlSessionFactory" ref="sqlSessionFactory" />
    </bean>
    <bean id="ndpSpecialDayHistMapper" class="org.mybatis.spring.mapper.MapperFactoryBean">
        <property name="mapperInterface" value="za.co.ipay.metermng.mybatis.generated.mapper.NdpSpecialDayHistMapper" />
        <property name="sqlSessionFactory" ref="sqlSessionFactory" />
    </bean>
    <bean id="ndpSpecialDayMapper" factory-bean="meterMngHistoryProxyFactory" factory-method="createProxy">
        <constructor-arg ref="ndpSpecialDayMapperImpl"></constructor-arg>
        <constructor-arg ref="ndpSpecialDayHistMapper"></constructor-arg>
    </bean>

    <!-- SpecialActions mapper -->
    <bean id="specialActionsMapperImpl" class="org.mybatis.spring.mapper.MapperFactoryBean">
        <property name="mapperInterface" value="za.co.ipay.metermng.mybatis.generated.mapper.SpecialActionsMapper" />
        <property name="sqlSessionFactory" ref="sqlSessionFactory" />
    </bean>
    <bean id="specialActionsHistMapper" class="org.mybatis.spring.mapper.MapperFactoryBean">
        <property name="mapperInterface" value="za.co.ipay.metermng.mybatis.generated.mapper.SpecialActionsHistMapper" />
        <property name="sqlSessionFactory" ref="sqlSessionFactory" />
    </bean>
    <bean id="specialActionsMapper" factory-bean="meterMngHistoryProxyFactory" factory-method="createProxy">
        <constructor-arg ref="specialActionsMapperImpl"></constructor-arg>
        <constructor-arg ref="specialActionsHistMapper"></constructor-arg>
    </bean>

    <bean id="specialActionReasonsMapperImpl" class="org.mybatis.spring.mapper.MapperFactoryBean">
        <property name="mapperInterface" value="za.co.ipay.metermng.mybatis.generated.mapper.SpecialActionReasonsMapper" />
        <property name="sqlSessionFactory" ref="sqlSessionFactory" />
    </bean>
    <bean id="specialActionReasonsHistMapper" class="org.mybatis.spring.mapper.MapperFactoryBean">
        <property name="mapperInterface" value="za.co.ipay.metermng.mybatis.generated.mapper.SpecialActionReasonsHistMapper" />
        <property name="sqlSessionFactory" ref="sqlSessionFactory" />
    </bean>
    <bean id="specialActionReasonsMapper" factory-bean="meterMngHistoryProxyFactory" factory-method="createProxy">
        <constructor-arg ref="specialActionReasonsMapperImpl"></constructor-arg>
        <constructor-arg ref="specialActionReasonsHistMapper"></constructor-arg>
    </bean>

    <bean id="specialActionReasonsLogMapper" class="org.mybatis.spring.mapper.MapperFactoryBean">
        <property name="mapperInterface" value="za.co.ipay.metermng.mybatis.generated.mapper.SpecialActionReasonsLogMapper" />
        <property name="sqlSessionFactory" ref="sqlSessionFactory" />
    </bean>

    <bean id="iKeyIndicatorsMapper" class="org.mybatis.spring.mapper.MapperFactoryBean">
        <property name="mapperInterface" value="za.co.ipay.metermng.server.mybatis.mapper.IKeyIndicatorsMapper" />
        <property name="sqlSessionFactory" ref="readOnlySqlSessionFactory" />
    </bean>

    <bean id="iUsagePointGroupsMapper" class="org.mybatis.spring.mapper.MapperFactoryBean">
        <property name="mapperInterface" value="za.co.ipay.metermng.server.mybatis.mapper.IUsagePointGroupsMapper" />
        <property name="sqlSessionFactory" ref="readOnlySqlSessionFactory" />
    </bean>

    <bean id="iSalesPerResourceMapper" class="org.mybatis.spring.mapper.MapperFactoryBean">
        <property name="mapperInterface" value="za.co.ipay.metermng.server.mybatis.mapper.ISalesPerResourceMapper" />
        <property name="sqlSessionFactory" ref="readOnlySqlSessionFactory" />
    </bean>

    <bean id="iBuyingIndexMapper" class="org.mybatis.spring.mapper.MapperFactoryBean">
        <property name="mapperInterface" value="za.co.ipay.metermng.server.mybatis.mapper.IBuyingIndexMapper" />
        <property name="sqlSessionFactory" ref="readOnlySqlSessionFactory" />
    </bean>

    <bean id="iVendingActivityMapper" class="org.mybatis.spring.mapper.MapperFactoryBean">
        <property name="mapperInterface" value="za.co.ipay.metermng.server.mybatis.mapper.IVendingActivityMapper" />
        <property name="sqlSessionFactory" ref="readOnlySqlSessionFactory" />
    </bean>

    <!-- Charge writeoff mapper -->
    <bean id="chargeWriteoffMapper" class="org.mybatis.spring.mapper.MapperFactoryBean">
        <property name="mapperInterface" value="za.co.ipay.metermng.mybatis.generated.mapper.ChargeWriteoffMapper" />
        <property name="sqlSessionFactory" ref="sqlSessionFactory" />
    </bean>

    <!-- BlockingType Mapper -->
    <bean id="blockingTypeMapperImpl" class="org.mybatis.spring.mapper.MapperFactoryBean">
        <property name="mapperInterface" value="za.co.ipay.metermng.mybatis.generated.mapper.BlockingTypeMapper" />
        <property name="sqlSessionFactory" ref="sqlSessionFactory" />
    </bean>
    <bean id="blockingTypeHistMapperImpl" class="org.mybatis.spring.mapper.MapperFactoryBean">
        <property name="mapperInterface" value="za.co.ipay.metermng.mybatis.generated.mapper.BlockingTypeHistMapper" />
        <property name="sqlSessionFactory" ref="sqlSessionFactory" />
    </bean>
    <bean id="blockingTypeMapper" factory-bean="meterMngHistoryProxyFactory" factory-method="createProxy">
        <constructor-arg ref="blockingTypeMapperImpl" />
        <constructor-arg ref="blockingTypeHistMapperImpl" />
    </bean>

    <!-- ImportFileType Mapper -->
    <bean id="importFileTypeMapper" class="org.mybatis.spring.mapper.MapperFactoryBean">
        <property name="mapperInterface" value="za.co.ipay.metermng.mybatis.generated.mapper.ImportFileTypeMapper" />
        <property name="sqlSessionFactory" ref="sqlSessionFactory" />
    </bean>

    <!-- ImportFile Mapper -->
    <bean id="importFileMapper" class="org.mybatis.spring.mapper.MapperFactoryBean">
        <property name="mapperInterface" value="za.co.ipay.metermng.mybatis.generated.mapper.ImportFileMapper" />
        <property name="sqlSessionFactory" ref="sqlSessionFactory" />
    </bean>

    <!-- ImportFileItem Mapper -->
    <bean id="importFileItemMapper" class="org.mybatis.spring.mapper.MapperFactoryBean">
        <property name="mapperInterface" value="za.co.ipay.metermng.mybatis.generated.mapper.ImportFileItemMapper" />
        <property name="sqlSessionFactory" ref="sqlSessionFactory" />
    </bean>

    <!-- ImportFileItemImport Mapper -->
    <bean id="importFileItemImportMapper" class="org.mybatis.spring.mapper.MapperFactoryBean">
        <property name="mapperInterface" value="za.co.ipay.metermng.mybatis.generated.mapper.ImportFileItemImportMapper" />
        <property name="sqlSessionFactory" ref="sqlSessionFactory" />
    </bean>

    <!-- ImportFile CustomMapper -->
    <bean id="importFileCustomMapper" class="org.mybatis.spring.mapper.MapperFactoryBean">
        <property name="mapperInterface" value="za.co.ipay.metermng.server.mybatis.mapper.ImportFileCustomMapper" />
        <property name="sqlSessionFactory" ref="sqlSessionFactory" />
    </bean>

    <bean id="formMapper" class="org.mybatis.spring.mapper.MapperFactoryBean">
        <property name="mapperInterface" value="za.co.ipay.metermng.mybatis.generated.mapper.FormMapper" />
        <property name="sqlSessionFactory" ref="sqlSessionFactory" />
    </bean>

    <bean id="formFieldsMapper" class="org.mybatis.spring.mapper.MapperFactoryBean">
        <property name="mapperInterface" value="za.co.ipay.metermng.mybatis.generated.mapper.FormFieldsMapper" />
        <property name="sqlSessionFactory" ref="sqlSessionFactory" />
    </bean>

    <!-- ModelSupportsPayMode mapper -->
    <bean id="meterDataDecoderMapper" class="org.mybatis.spring.mapper.MapperFactoryBean">
        <property name="mapperInterface" value="za.co.ipay.metermng.mybatis.generated.mapper.MeterDataDecoderMapper" />
        <property name="sqlSessionFactory" ref="sqlSessionFactory" />
    </bean>

    <bean id="accessGroupMapperMMA" class="org.mybatis.spring.mapper.MapperFactoryBean">
        <property name="mapperInterface" value="za.co.ipay.metermng.server.mybatis.mapper.AccessGroupMapper" />
        <property name="sqlSessionFactory" ref="sqlSessionFactory" />
    </bean>

    <bean id="billPayTransMapper" class="org.mybatis.spring.mapper.MapperFactoryBean">
        <property name="mapperInterface" value="za.co.ipay.metermng.mybatis.generated.mapper.BillPayTransMapper" />
        <property name="sqlSessionFactory" ref="sqlSessionFactory" />
    </bean>

    <bean id="payTypeMapper" class="org.mybatis.spring.mapper.MapperFactoryBean">
        <property name="mapperInterface" value="za.co.ipay.metermng.mybatis.generated.mapper.PayTypeMapper" />
        <property name="sqlSessionFactory" ref="sqlSessionFactory" />
    </bean>

    <bean id="payTypeDetailsMapper" class="org.mybatis.spring.mapper.MapperFactoryBean">
        <property name="mapperInterface" value="za.co.ipay.metermng.mybatis.generated.mapper.PayTypeDetailsMapper" />
        <property name="sqlSessionFactory" ref="sqlSessionFactory" />
    </bean>

    <bean id="voteMapper" class="org.mybatis.spring.mapper.MapperFactoryBean">
        <property name="mapperInterface" value="za.co.ipay.metermng.mybatis.generated.mapper.VoteMapper" />
        <property name="sqlSessionFactory" ref="sqlSessionFactory" />
    </bean>

    <bean id="productInfoMapper" class="org.mybatis.spring.mapper.MapperFactoryBean">
        <property name="mapperInterface" value="za.co.ipay.metermng.mybatis.generated.mapper.ProductInfoMapper" />
        <property name="sqlSessionFactory" ref="sqlSessionFactory" />
    </bean>

    <!-- **Meter****************************** -->
    <!-- ***  End Data Access Mappers *** -->
    <!-- ******************************** -->


    <!-- ********************** -->
    <!-- *** Start Services *** -->
    <!-- ********************** -->
    <bean id="keyIndicatorService" class="za.co.ipay.metermng.server.mybatis.service.KeyIndicatorService">
        <property name="iKeyIndicatorsMapper" ref="iKeyIndicatorsMapper" />
        <property name="messageSource" ref="messageSource" />
    </bean>

    <bean id="usagePointGroupsService" class="za.co.ipay.metermng.server.mybatis.service.UsagePointGroupsService">
        <property name="iUsagePointGroupsMapper" ref="iUsagePointGroupsMapper" />
        <property name="groupHierarchyMapper" ref="groupHierarchyMapper"/>
        <property name="groupService" ref="groupService"/>
        <property name="appSettingService" ref="appSettingService"/>
    </bean>

    <bean id="salesPerResourceService" class="za.co.ipay.metermng.server.mybatis.service.SalesPerResourceService">
        <property name="iSalesPerResourceMapper" ref="iSalesPerResourceMapper" />
        <property name="serviceResourceMapper" ref="serviceResourceMapper" />
    </bean>

    <bean id="dashBoardService" class="za.co.ipay.metermng.server.mybatis.service.DashBoardService">
        <property name="iBuyingIndexMapper" ref="iBuyingIndexMapper" />
    </bean>

    <bean id="vendingActivityService" class="za.co.ipay.metermng.server.mybatis.service.VendingActivityService">
        <property name="iVendingActivityMapper" ref="iVendingActivityMapper" />
    </bean>

    <bean id="searchService" class="za.co.ipay.metermng.server.mybatis.service.SearchService">
        <property name="advancedSearchCustomMapper" ref="advancedSearchCustomMapper"/>
    </bean>

    <bean id="groupSearchService" class="za.co.ipay.metermng.server.mybatis.service.GroupSearchService">
        <property name="groupSearchCustomMapper" ref="groupSearchCustomMapper"/>
        <property name="usagePointService" ref="usagePointService"/>
    </bean>

    <bean id="meterReadingGeneratorService" class="za.co.ipay.metermng.server.mybatis.service.MeterReadingGeneratorService">
        <property name="meterReadingMapper" ref="meterReadingMapper"/>
        <property name="readingQualityMapper" ref="readingQualityMapper" />
        <property name="meterReadingCustomMapper" ref="meterReadingCustomMapper" />
        <property name="timeDimMapper" ref="timeDimMapper" />
        <property name="meterReadingFactMapper" ref="meterReadingFactMapper" />
        <property name="registerReadingMapper" ref="registerReadingMapper"/>
        <property name="mdcChannelService" ref="mdcChannelService"/>
    </bean>

    <bean id="meterService" class="za.co.ipay.metermng.server.mybatis.service.MeterService">
        <property name="meterMapper" ref="meterMapper"/>
        <property name="meterCustomMapper" ref="meterCustomMapper" />
        <property name="meterReadingMapper" ref="meterReadingMapper" />
        <property name="meterReadingCustomMapper" ref="meterReadingCustomMapper" />
        <property name="meterReadingTypeMapper" ref="meterReadingTypeMapper" />
        <property name="readingQualityCustomMapper" ref="readingQualityCustomMapper" />
        <property name="meterReadingGeneratorService" ref="meterReadingGeneratorService" />
        <property name="meterBalancingMapper" ref="meterBalancingMapper" />
        <property name="meterBalancingCustomMapper" ref="meterBalancingCustomMapper" />
        <property name="stsMeterService" ref="stsMeterService"/>
        <property name="meterCountMapper" ref="iMeterCountMapper"/>
        <property name="pricingStructureService" ref="pricingStructureService"/>
        <property name="messageService" ref="messageServiceImpl"/>
        <property name="usagePointMapper" ref="usagePointMapper"/>
        <property name="vendMapper" ref="vendMapper"/>
        <property name="registerReadingMapper" ref="registerReadingMapper"/>
        <property name="supplyGroupService" ref="supplyGroupService"/>
    </bean>

    <bean id="stsMeterService" class="za.co.ipay.metermng.server.mybatis.service.STSMeterService">
        <property name="stsMeterMapper" ref="stsMeterMapper"/>
        <property name="stsMeterCustomMapper" ref="stsMeterCustomMapper"/>
    </bean>

    <bean id="meterTypeService" class="za.co.ipay.metermng.server.mybatis.service.MeterTypeService">
        <property name="meterTypeMapper" ref="meterTypeMapper"/>
    </bean>

    <bean id="accountAdjustmentProcessorFactory" class="za.co.ipay.metermng.tariff.thin.AccountAdjustmentProcessorFactory">
        <property name="tariffSupportMapper" ref="tariffSupportMapper"/>
        <property name="eventNotificationMapper" ref="eventNotificationMapper"/>
        <property name="eventNotificationTypeMapper" ref="eventNotificationTypeMapper"/>
        <property name="appSettingMapper" ref="appSettingMapper"/>
        <property name="accountTransMapper" ref="accountTransMapper"/>
        <property name="mdcTransMapper" ref="mdcTransMapper"/>
        <property name="mdcMapper" ref="mdcMapper"/>
        <property name="customerAccountMapper" ref="customerAccountMapper"/>
        <property name="usagePointCustomMapper" ref="usagePointCustomMapper"/>
        <property name="meterCustomMapper" ref="meterCustomMapper"/>
        <property name="unitsAccountCustomMapper" ref="unitsAccountCustomMapper"/>
        <property name="unitsAccountMapper" ref="unitsAccountMapper"/>
        <property name="unitsTransMapper" ref="unitsTransMapper"/>
        <property name="meterMapper" ref="meterMapper"/>
        <property name="meterModelMapper" ref="meterModelMapper"/>
    </bean>

    <bean id="accountAdjustmentProcessor" factory-bean="accountAdjustmentProcessorFactory" factory-method="build"/>

    <bean id="unitsAccountAdjustmentProcessor" factory-bean="accountAdjustmentProcessorFactory" factory-method="buildUnitsAdjustmentProcessor" />

    <bean id="customerService" class="za.co.ipay.metermng.server.mybatis.service.CustomerService">
        <property name="customerMapper" ref="customerMapper"/>
        <property name="customerSuggestionMapper" ref="customerSuggestionMapper"/>
        <property name="customerAgreementMapper" ref="customerAgreementMapper"/>
        <property name="customerAccountMapper" ref="customerAccountMapper"/>
        <property name="customerAccountCustomMapper" ref="customerAccountCustomMapper" />
        <property name="customerAccountHistMapper" ref="customerAccountHistMapper" />
        <property name="locationService" ref="locationService"/>
        <property name="customerAgreementService" ref="customerAgreementService"/>
        <property name="customerAccountService" ref="customerAccountService"/>
        <property name="ipayXmlMessageService" ref="defaultIpayXmlMessageService"/>
        <property name="mdcMapper" ref="mdcMapper"/>
        <property name="accountTransMapper" ref="accountTransMapper"/>
        <property name="accountTransTypeMapper" ref="accountTransTypeMapper"/>
        <property name="auxAccountMapper" ref="auxAccountMapper"/>
        <property name="auxSupportMapper" ref="auxSupportMapper"/>
        <property name="meterMapper" ref="meterMapper"/>
        <property name="translationAware" ref="translationAware"/>
        <property name="usagePointService" ref="usagePointService" />
        <property name="meterModelService" ref="meterModelService"/>
        <property name="messageSource" ref="messageSource" />
        <property name="emailService" ref="emailService" />
        <property name="specialActionReasonsLogMapper" ref="specialActionReasonsLogMapper"/>
        <property name="accountAdjustmentProcessor" ref="accountAdjustmentProcessor" />
        <property name="billPayTransMapper" ref="billPayTransMapper" />
        <property name="payTypeMapper" ref="payTypeMapper" />
        <property name="payTypeDetailsMapper" ref="payTypeDetailsMapper" />
        <property name="voteMapper" ref="voteMapper" />
        <property name="messageService" ref="messageServiceImpl"/>
        <property name="appSettingService" ref="appSettingService"/>
    </bean>

    <bean id="customerAgreementService" class="za.co.ipay.metermng.server.mybatis.service.CustomerAgreementService">
        <property name="customerAgreementMapper" ref="customerAgreementMapper"/>
        <property name="customerSuggestionMapper" ref="customerSuggestionMapper"/>
    </bean>

    <bean id="customerAccountService" class="za.co.ipay.metermng.server.mybatis.service.CustomerAccountService">
        <property name="customerAccountMapper" ref="customerAccountMapper"/>
        <property name="customerSuggestionMapper" ref="customerSuggestionMapper"/>
    </bean>

    <bean id="customerHistService" class="za.co.ipay.metermng.server.mybatis.service.CustomerHistService">
        <property name="customerHistMapperImpl" ref="customerHistMapperImpl"/>
        <property name="customerAgreementHistMapperImpl" ref="customerAgreementHistMapperImpl"/>
        <property name="customerAccountHistMapperImpl" ref="customerAccountHistMapperImpl"/>
        <property name="accountTransMapper" ref="accountTransMapper"/>
        <property name="auxAccountMapper" ref="auxAccountMapperImpl"/>

    </bean>

    <bean id="usagePointService" class="za.co.ipay.metermng.server.mybatis.service.UsagePointService">
        <property name="usagePointMapper" ref="usagePointMapper"/>
        <property name="usagePointCustomMapper" ref="usagePointCustomMapper"/>
        <property name="upPricingStructureMapper" ref="upPricingStructureMapper"/>
        <property name="tariffSupportMapper" ref="tariffSupportMapper"/>
        <property name="upPricingStructureHistMapper" ref="upPricingStructureHistMapperImpl" />
        <property name="pricingStructureMapper" ref="pricingStructureMapper"/>
        <property name="locationService" ref="locationService"/>
        <property name="upGenGroupLnkMapper" ref="upGenGroupLnkMapper"/>
        <property name="stsMeterService" ref="stsMeterService"/>
        <property name="meterService" ref="meterService"/>
        <property name="pricingStructureService" ref="pricingStructureService"/>
        <property name="blockingTypeService" ref="blockingTypeService"/>
        <property name="upMeterInstallMapper" ref="upMeterInstallMapper"/>
        <property name="meterReadingGeneratorService" ref="meterReadingGeneratorService" />
        <property name="meterReadingMapper" ref="meterReadingMapper" />
        <property name="registerReadingService" ref="registerReadingService" />
        <property name="specialActionReasonsLogMapper" ref="specialActionReasonsLogMapper"/>
        <property name="specialActionsService" ref="specialActionsService"/>
        <property name="vendMapper" ref="vendMapper"/>
        <property name="appSettingMapper" ref="appSettingMapper"/>
        <property name="chargeWriteoffMapper" ref="chargeWriteoffMapper"/>
        <property name="transItemTypeMapper" ref="transItemTypeMapper"/>
        <property name="ipayXmlMessageService" ref="defaultIpayXmlMessageService"/>
        <property name="groupService" ref="groupService" />
        <property name="eventNotificationService" ref="eventNotificationService" />
        <property name="mdcChannelService" ref="mdcChannelService"/>
        <property name="readingsCustomMapper" ref="readingsCustomMapper" />
        <property name="appSettingService" ref="appSettingService"/>
        <property name="unitsTransMapper" ref="unitsTransMapper" />
        <property name="translationAware" ref="translationAware" />
        <property name="unitsAccountAdjustmentProcessor" ref="unitsAccountAdjustmentProcessor" />
        <property name="customerService" ref="customerService" />
        <property name="meterMapper" ref="meterMapper" />
        <property name="meterModelService" ref="meterModelService" />
        <property name="messageSource" ref="messageSource" />
        <property name="customerAgreementService" ref="customerAgreementService" />
        <property name="unitsAccountMapper" ref="unitsAccountMapper" />
        <property name="customerTransMapper" ref="customerTransMapper" />
        <property name="accessGroupMapper" ref="accessGroupMapperMMA"/>
    </bean>

    <bean id="eventNotificationService" class="za.co.ipay.metermng.event.DefaultEventNotificationService">
        <property name="eventCustomMapper" ref="eventCustomMapper"/>
        <property name="eventNotificationMapper" ref="eventNotificationMapper"/>
    </bean>

    <bean id="eventCustomMapper" class="org.mybatis.spring.mapper.MapperFactoryBean">
        <property name="mapperInterface" value="za.co.ipay.metermng.mybatis.custom.mapper.EventCustomMapper" />
        <property name="sqlSessionFactory" ref="sqlSessionFactory" />
    </bean>

    <!-- UpPricingStructureMapper -->
    <bean id="upPricingStructureMapperImpl" class="org.mybatis.spring.mapper.MapperFactoryBean">
        <property name="mapperInterface" value="za.co.ipay.metermng.mybatis.generated.mapper.UpPricingStructureMapper" />
        <property name="sqlSessionFactory" ref="sqlSessionFactory" />
    </bean>
    <bean id="upPricingStructureHistMapperImpl" class="org.mybatis.spring.mapper.MapperFactoryBean">
        <property name="mapperInterface" value="za.co.ipay.metermng.mybatis.generated.mapper.UpPricingStructureHistMapper" />
        <property name="sqlSessionFactory" ref="sqlSessionFactory" />
    </bean>
    <bean id="upPricingStructureMapper" factory-bean="meterMngHistoryProxyFactory" factory-method="createProxy">
        <constructor-arg ref="upPricingStructureMapperImpl"></constructor-arg>
        <constructor-arg ref="upPricingStructureHistMapperImpl"></constructor-arg>
    </bean>

    <bean id="locationService" class="za.co.ipay.metermng.server.mybatis.service.LocationService">
        <property name="locationMapper" ref="locationMapper"/>
        <property name="IGenGroupMapper" ref="iGenGroupMapper"/>
        <property name="locationHistMapper" ref="locationHistMapperImpl"/>
    </bean>

    <bean id="lookupService" class="za.co.ipay.metermng.server.mybatis.service.LookupService">
        <property name="pricingStructureMapper" ref="pricingStructureMapper"/>
        <property name="mdcChannelCustomMapper" ref="mdcChannelCustomMapper" />
        <property name="stsAlgorithmMapper" ref="stsAlgorithmMapper"/>
        <property name="stsTokenTechMapper" ref="stsTokenTechMapper"/>
        <property name="stsSupplyGroupMapper" ref="stsSupplyGroupMapper"/>
        <property name="auxChargeScheduleMapper" ref="auxChargeScheduleMapper"/>
        <property name="auxTypeMapper" ref="auxTypeMapper"/>
        <property name="auxAccountMapper" ref="auxAccountMapper"/>
        <property name="endDeviceStoreMapper" ref="endDeviceStoreMapper"/>
        <property name="meterTypeMapper" ref="meterTypeMapper"/>
        <property name="touSeasonMapper" ref="touSeasonMapper"/>
        <property name="meterTypeService" ref="meterTypeService" />
        <property name="serviceResourceService" ref="serviceResourceService" />
        <property name="paymentModeService" ref="paymentModeService" />
        <property name="meterService" ref="meterService" />
        <property name="meterModelService" ref="meterModelService"/>
        <property name="blockingTypeService" ref="blockingTypeService"/>
        <property name="registerReadingService" ref="registerReadingService"/>
        <property name="pricingStructureService" ref="pricingStructureService"/>
        <property name="mdcService" ref="mdcService" />
    </bean>

    <bean id="upGenGroupLnkService" class="za.co.ipay.metermng.server.mybatis.service.UpGenGroupLnkService">
        <property name="upGenGroupLnkMapper" ref="upGenGroupLnkMapper"/>
        <property name="iGenGroupMapper" ref="iGenGroupMapper" />
    </bean>

    <bean id="bulkUploadService" class="za.co.ipay.metermng.server.mybatis.service.BulkUploadService">
        <property name="meterService" ref="meterService" />
        <property name="customerService" ref="customerService"/>
        <property name="auxAccountService" ref="auxAccountService" />
        <property name="importMessageSource" ref="importMessageSource" />
    </bean>

    <bean id="auxFreeIssue" class="za.co.ipay.metermng.engineering.AuxFreeIssue">
        <property name="ipayXmlMessageService" ref="defaultIpayXmlMessageService"/>
        <property name="currentUserNameProvider" ref="currentUserNameProvider"/>
    </bean>

    <bean id="displayTokens" class="za.co.ipay.metermng.engineering.DisplayTokens">
        <property name="ipayXmlMessageService" ref="defaultIpayXmlMessageService"/>
    </bean>

    <bean id="pricingStructureService" class="za.co.ipay.metermng.server.mybatis.service.PricingStructureService">
        <property name="pricingStructureMapper" ref="pricingStructureMapper"/>
        <property name="upPricingStructureMapper" ref="upPricingStructureMapper"/>
        <property name="tariffClassMapper" ref="tariffClassMapper"/>
        <property name="tariffMapper" ref="tariffMapper"/>
        <property name="ipayXmlMessageService" ref="defaultIpayXmlMessageService"/>
        <property name="iCalendarPricingStructureMapper" ref="iCalendarPricingStructureMapper"/>
        <property name="tariffSupportMapper" ref="tariffSupportMapper"/>
        <property name="mdcChannelService" ref="mdcChannelService"/>
        <property name="pricingStructureCustomService" ref="pricingStructureCustomService"/>
    </bean>

    <bean id="auxChargeScheduleService" class="za.co.ipay.metermng.server.mybatis.service.AuxChargeScheduleService">
        <property name="auxChargeScheduleMapper" ref="auxChargeScheduleMapper"/>
        <property name="auxAccountMapper" ref="auxAccountMapper"/>
    </bean>

    <bean id="auxTypeService" class="za.co.ipay.metermng.server.mybatis.service.AuxTypeService">
        <property name="auxTypeMapper" ref="auxTypeMapper"/>
    </bean>

    <bean id="stsMeterHistService" class="za.co.ipay.metermng.server.mybatis.service.STSMeterHistService">
        <property name="stsMeterHistMapperImpl" ref="stsMeterHistMapperImpl"/>
        <property name="stsAlgorithmMapper" ref="stsAlgorithmMapper"/>
        <property name="stsTokenTechMapper" ref="stsTokenTechMapper"/>
        <property name="stsSupplyGroupMapper" ref="stsSupplyGroupMapper"/>
    </bean>

    <bean id="meterHistService" class="za.co.ipay.metermng.server.mybatis.service.MeterHistService">
        <property name="meterHistMapperImpl" ref="meterHistMapperImpl"/>
    </bean>

    <bean id="usagePointHistService" class="za.co.ipay.metermng.server.mybatis.service.UsagePointHistService">
        <property name="usagePointHistoryMapper" ref="usagePointHistoryMapper"/>
        <property name="upMeterInstallHistMapper" ref="upMeterInstallHistMapperImpl" />
        <property name="upPricingStructureHistMapper" ref="upPricingStructureHistMapperImpl" />
        <property name="usagePointService" ref="usagePointService"/>
        <property name="meterService" ref="meterService"/>
        <property name="pricingStructureService" ref="pricingStructureService"/>
        <property name="specialActionsService" ref="specialActionsService"/>
    </bean>

    <bean id="auxAccountHistService" class="za.co.ipay.metermng.server.mybatis.service.AuxAccountHistService">
        <property name="auxAccountHistMapper" ref="auxAccountHistMapperImpl"/>
    </bean>

    <bean id="unitTransService" class="za.co.ipay.metermng.server.mybatis.service.UnitTransService">
        <property name="unitsTransMapper" ref="unitsTransMapper"/>
    </bean>

    <bean id="customerTransService" class="za.co.ipay.metermng.server.mybatis.service.CustomerTransService">
        <property name="customerTransMapper" ref="customerTransMapper"/>
        <property name="customerTransItemMapper" ref="customerTransItemMapper"/>
        <property name="usagePointMapper" ref="usagePointMapper"/>
        <property name="customerAgreementMapper" ref="customerAgreementMapper"/>
        <property name="customerMapper" ref="customerMapper"/>
        <property name="stsMeterMapper" ref="stsMeterMapper"/>
        <property name="meterMapper" ref="meterMapper"/>
        <property name="transItemTypeMapper" ref="transItemTypeMapper"/>
        <property name="customerTransCustomMapper" ref="customerTransCustomMapper"/>
        <property name="customerTransExtraMapper" ref="customerTransExtraMapper"/>
        <property name="tariffMapper" ref="tariffMapper"/>
    </bean>

    <bean id="engineeringTokensService" class="za.co.ipay.metermng.server.mybatis.service.EngineeringTokensService">
        <property name="usagePointMapper" ref="usagePointMapper"/>
        <property name="stsEngineeringTokenMapper" ref="stsEngineeringTokenMapper"/>
        <property name="stsEngTokenTypeMapper" ref="stsEngTokenTypeMapper"/>
        <property name="stsMeterMapper" ref="stsMeterMapper"/>
        <property name="stsSupplyGroupMapper" ref="stsSupplyGroupMapper"/>
        <property name="importFileDataService" ref="importFileDataService"/>
    </bean>

    <bean id="supplyGroupService" class="za.co.ipay.metermng.server.mybatis.service.SupplyGroupService">
        <property name="stsSupplyGroupMapper" ref="stsSupplyGroupMapper"/>
        <property name="stsMeterMapper" ref="stsMeterMapper"/>
    </bean>
    
    <bean id="auxAccountCustomService" class="za.co.ipay.metermng.service.auxaccount.AuxAccountCustomService">
        <property name="sqlSessionFactory" ref="sqlSessionFactoryUnmanaged" />
    </bean>

    <bean id="mdcChannelCustomService" class="za.co.ipay.metermng.service.mdcchannel.MdcChannelCustomService">
        <property name="mdcChannelMapper" ref="mdcChannelMapper" />
        <property name="mdcChanBillDetMapper" ref="mdcChanBillDetMapper" />
    </bean>

    <bean id="pricingStructureCustomService" class="za.co.ipay.metermng.service.pricingstructure.PricingStructureCustomService">
        <property name="mdcChannelCustomService" ref="mdcChannelCustomService" />
        <property name="tariffSupportMapper" ref="tariffSupportMapper" />
        <property name="appSettingMapper" ref="appSettingMapper" />
    </bean>

    <bean id="auxAccountService" class="za.co.ipay.metermng.server.mybatis.service.AuxAccountService">
        <property name="auxAccountMapper" ref="auxAccountMapper"/>
        <property name="auxChargeScheduleMapper" ref="auxChargeScheduleMapper"/>
        <property name="auxTypeMapper" ref="auxTypeMapper"/>
        <property name="customerAgreementAuxAccountMapper" ref="customerAgreementAuxAccountMapper"/>
        <property name="specialActionReasonsLogMapper" ref="specialActionReasonsLogMapper"/>
        <property name="auxSupportMapper" ref="auxSupportMapper"/>
        <property name="auxAccountCustomService" ref="auxAccountCustomService"/>
    </bean>

    <bean id="groupService" class="za.co.ipay.metermng.server.mybatis.service.GroupService">
        <property name="groupTypeMapper" ref="groupTypeMapper"/>
        <property name="iGroupTypeMapper" ref="iGroupTypeMapper"/>
        <property name="groupHierarchyMapper" ref="groupHierarchyMapper"/>
        <property name="groupHierarchyCustomMapper" ref="groupHierarchyCustomMapper"/>
        <property name="groupEntityMapper" ref="groupEntityMapper"/>
        <property name="genGroupMapper" ref="genGroupMapper" />
        <property name="iGenGroupMapper" ref="iGenGroupMapper" />
        <property name="groupFeatureMapper" ref="groupFeatureMapper" />
        <property name="groupTypeFeatureMapper" ref="groupTypeFeatureMapper" />
        <property name="customerAccThresholdsMapper" ref="customerAccThresholdsMapper" />
        <property name="genGroupCustomMapper" ref="genGroupCustomMapper" />
        <property name="userGroupService" ref="userGroupService"/>
        <property name="customerService" ref="customerService" />
        <property name="usagePointService" ref="usagePointService" />
        <property name="auxChargeScheduleService" ref="auxChargeScheduleService" />
        <property name="pricingStructureService" ref="pricingStructureService" />
        <property name="deviceStoreService" ref="deviceStoreService" />
        <property name="upGenGroupLnkService" ref="upGenGroupLnkService" />
        <property name="ndpService" ref="ndpService" />
        <property name="appSettingService" ref="appSettingService" />
        <property name="custAccNotifyMapper" ref="custAccNotifyMapper" />
    </bean>

    <bean id="deviceStoreService" class="za.co.ipay.metermng.server.mybatis.service.DeviceStoreService">
        <property name="endDeviceStoreMapper" ref="endDeviceStoreMapper"/>
        <property name="endDeviceStoreHistMapper" ref="endDeviceStoreHistMapperImpl"/>
        <property name="locationService" ref="locationService"/>
        <property name="meterService" ref="meterService"/>
    </bean>

    <bean id="userGroupService" class="za.co.ipay.metermng.server.mybatis.service.UserGroupService">
        <property name="userGroupMapper" ref="userGroupMapper"/>
        <property name="groupService" ref="groupService" />
        <property name="accessControlService" ref="accessControlService" />
        <property name="meterMngPermission" value="mm_meter_mng_user" />
    </bean>

    <bean id="touThinSmartTariffDataService" class="za.co.ipay.metermng.service.tariffdata.TouThinSmartTariffDataService">
        <property name="touMapper" ref="touMapper"/>
        <property name="touCalendarMapper" ref="touCalendarMapper" />
        <property name="meterReadingTypeMapper" ref="meterReadingTypeMapper" />
        <property name="cycleMapper" ref="cycleMapper" />
        <property name="touTariffCalendarMapper" ref="touTariffCalendarMapper" />
        <property name="touSeasonChgMapper" ref="touSeasonChgMapper" />
        <property name="pricingStructureMapper" ref="pricingStructureMapper"/>
    </bean>

    <bean id="registerReadingThinDataService" class="za.co.ipay.metermng.service.tariffdata.RegisterReadingThinDataService">
        <property name="cycleMapper" ref="cycleMapper" />
        <property name="billingDetMapper" ref="billingDetMapper" />
        <property name="billingDetAppliesLnkSupportMapper" ref="billingDetAppliesLnkSupportMapper"/>
        <property name="tariffClassMapper" ref="tariffClassMapper"/>
    </bean>

    <bean id="kenyaSTSTariffDataService" class="za.co.ipay.metermng.service.tariffdata.KenyaSTSTariffDataService">
    </bean>

    <bean id="samoaSTSTariffDataService" class="za.co.ipay.metermng.service.tariffdata.SamoaSTSTariffDataService">
    </bean>

    <bean id="blockThinTariffDataService" class="za.co.ipay.metermng.service.tariffdata.BlockThinTariffDataService">
    </bean>

    <bean id="blockTariffDataService" class="za.co.ipay.metermng.service.tariffdata.BlockTariffDataService">
    </bean>

    <bean id="basicPrivateUtilityTariffDataService" class="za.co.ipay.metermng.service.tariffdata.BasicPrivateUtilityTariffDataService">
    </bean>


    <bean id="calendarService" class="za.co.ipay.metermng.server.mybatis.service.CalendarService">
        <property name="touSeasonMapper" ref="touSeasonMapper"/>
        <property name="touPeriodMapper" ref="touPeriodMapper"/>
        <property name="touCalendarMapper" ref="touCalendarMapper"/>
        <property name="calendarSuggestionMapper" ref="calendarSuggestionMapper"/>
        <property name="touDayProfileMapper" ref="touDayProfileMapper"/>
        <property name="touCalendarSeasonMapper" ref="touCalendarSeasonMapper"/>
        <property name="touSpecialDayMapper" ref="touSpecialDayMapper"/>
        <property name="touSeasonDateMapper" ref="touSeasonDateMapper"/>
        <property name="touDayProfileTimeMapper" ref="touDayProfileTimeMapper"/>
        <property name="calendarCustomMapper" ref="calendarCustomMapper" />
        <property name="pricingStructureService" ref="pricingStructureService" />
    </bean>

    <bean id="manufacturerService" class="za.co.ipay.metermng.server.mybatis.service.ManufacturerService">
        <property name="manufacturerMapper" ref="manufacturerMapper"/>
    </bean>

    <bean id="mdcService" class="za.co.ipay.metermng.server.mybatis.service.MdcService">
        <property name="mdcMapper" ref="mdcMapper"/>
        <property name="mdcChannelCustomMapper" ref="mdcChannelCustomMapper" />
    </bean>

    <bean id="mdcChannelService" class="za.co.ipay.metermng.server.mybatis.service.MdcChannelService">
        <property name="mdcMapper" ref="mdcMapper"/>
        <property name="mdcChannelMapper" ref="mdcChannelMapper"/>
        <property name="billingDetMapper" ref="billingDetMapper"/>
        <property name="mdcChanBillDetMapper" ref="mdcChanBillDetMapper"/>
        <property name="modelChannelConfigMapper" ref="modelChannelConfigMapper"/>
        <property name="meterReadingTypeMapper" ref="meterReadingTypeMapper"/>
        <property name="mdcChannelCustomMapper" ref="mdcChannelCustomMapper"/>
        <property name="timeIntervalService" ref="timeIntervalService"/>
        <property name="meterService" ref="meterService"/>
        <property name="meterModelService" ref="meterModelService"/>
        <property name="mdcChannelCustomService" ref="mdcChannelCustomService"/>
    </bean>

    <bean id="timeIntervalService" class="za.co.ipay.metermng.server.mybatis.service.TimeIntervalService">
        <property name="timeIntervalMapper" ref="timeIntervalMapper"/>
    </bean>

    <bean id="billingDetService" class="za.co.ipay.metermng.server.mybatis.service.BillingDetService">
        <property name="billingDetMapper" ref="billingDetMapper"/>
        <property name="billingDetAppliesLnkMapper" ref="billingDetAppliesLnkMapper"/>
        <property name="billingDetAppliesLnkSupportMapper" ref="billingDetAppliesLnkSupportMapper"/>
        <property name="tariffSupportMapper" ref="tariffSupportMapper" />
    </bean>

    <bean id="mdcTransService" class="za.co.ipay.metermng.server.mybatis.service.MdcTransService">
        <property name="mdcTransMapper" ref="mdcTransMapper"/>
        <property name="meterMapper" ref="meterMapper"/>
        <property name="usagePointMapper" ref="usagePointMapper"/>
        <property name="customerAgreementMapper" ref="customerAgreementMapper"/>
        <property name="customerMapper" ref="customerMapper"/>
    </bean>

    <bean id="paymentModeService" class="za.co.ipay.metermng.server.mybatis.service.PaymentModeService">
        <property name="paymentModeMapper" ref="paymentModeMapper"/>
    </bean>

    <bean id="serviceResourceService" class="za.co.ipay.metermng.server.mybatis.service.ServiceResourceService">
        <property name="serviceResourceMapper" ref="serviceResourceMapper"/>
    </bean>

    <bean id="meterModelService" class="za.co.ipay.metermng.server.mybatis.service.MeterModelService">
        <property name="meterModelMapper" ref="meterModelMapper"/>
        <property name="modelSupportsPayModeMapper" ref="modelSupportsPayModeMapper"/>
        <property name="meterModelCustomMapper" ref="meterModelCustomMapper"/>
        <property name="iMeterCountMapper" ref="iMeterCountMapper"/>
        <property name="manufacturerService" ref="manufacturerService" />
        <property name="meterTypeService" ref="meterTypeService" />
        <property name="mdcService" ref="mdcService" />
        <property name="serviceResourceService" ref="serviceResourceService" />
        <property name="lookupService" ref="lookupService" />
        <property name="pricingStructureService" ref="pricingStructureService"/>
        <property name="meterDataDecoderMapper" ref="meterDataDecoderMapper"/>
        <property name="meterService" ref="meterService"/>
    </bean>

    <bean id="appSettingService" class="za.co.ipay.metermng.server.mybatis.service.AppSettingService">
        <property name="appSettingMapper" ref="appSettingMapper"/>
        <property name="appSettingHistMapper" ref="appSettingHistMapper"/>
        <property name="groupEntityCustomMapper" ref="groupEntityCustomMapper"/>
        <property name="messageSource" ref="messageSource" />
    </bean>

    <bean id="registerReadingService" class="za.co.ipay.metermng.server.mybatis.service.RegisterReadingService">
        <property name="registerReadingMapper" ref="registerReadingMapper"/>
    </bean>

    <!-- CRUD service for schedule data -->
    <bean id="scheduleService" class="za.co.ipay.metermng.server.mybatis.service.ScheduleService">
        <property name="taskScheduleMapper" ref="taskScheduleMapper"/>
        <property name="scheduledTaskMapper" ref="scheduledTaskMapper"/>
        <property name="taskClassMapper" ref="taskClassMapper"/>
        <property name="scheduledTaskUserMapper" ref="scheduledTaskUserMapper"/>
        <property name="accessControlService" ref="accessControlService" />
        <property name="customerService" ref="customerService" />
    </bean>

    <!-- Service to set up and manage task schedules -->
    <bean id="schedulingService" class="za.co.ipay.metermng.server.mybatis.service.SchedulingService">
        <property name="defaultLocaleName" value="${defaultLocale}" />
        <property name="useDefaultLocaleOnly" value="${useDefaultLocaleOnly}" />
        <property name="scheduleService" ref="scheduleService"/>
        <property name="meterService" ref="meterService" />
        <property name="appSettingService" ref="appSettingService"/>
        <property name="scheduler" ref="scheduler"/>
        <property name="messageSource" ref="messageSource" />
        <property name="formatSource" ref="formatSource" />
        <property name="emailService" ref="emailService" />
    </bean>

    <!-- NdpService -->
    <bean id="ndpService" class="za.co.ipay.metermng.server.mybatis.service.NdpService">
        <property name="ndpScheduleMapper" ref="ndpScheduleMapper"/>
        <property name="ndpSeasonMapper" ref="ndpSeasonMapper"/>
        <property name="ndpDayProfileMapper" ref="ndpDayProfileMapper"/>
        <property name="ndpSpecialDayMapper" ref="ndpSpecialDayMapper"/>
    </bean>

    <!-- Special Actions service -->
    <bean id="specialActionsService" class="za.co.ipay.metermng.server.mybatis.service.SpecialActionsService">
        <property name="specialActionsMapper" ref="specialActionsMapper"/>
        <property name="specialActionReasonsMapper" ref="specialActionReasonsMapper"/>
        <property name="specialActionReasonsLogMapper" ref="specialActionReasonsLogMapper"/>
        <property name="messageSource" ref="messageSource" />
    </bean>

    <!-- OnlineBulkMeterService -->
    <bean id="onlineBulkMeterService" class="za.co.ipay.metermng.server.mybatis.service.OnlineBulkMeterService">
        <property name="stsMeterService" ref="stsMeterService"/>
        <property name="locationService" ref="locationService"/>
        <property name="upGenGroupLnkService" ref="upGenGroupLnkService"/>
        <property name="deviceStoreService" ref="deviceStoreService"/>
        <property name="meterService" ref="meterService" />
        <property name="meterTypeService" ref="meterTypeService" />
        <property name="meterModelService" ref="meterModelService" />
        <property name="groupSearchService" ref="groupSearchService" />
        <property name="meterReadingGeneratorService" ref="meterReadingGeneratorService" />
        <property name="registerReadingService" ref="registerReadingService" />
        <property name="usagePointService" ref="usagePointService"/>
        <property name="stsAlgorithmMapper" ref="stsAlgorithmMapper" />
        <property name="stsTokenTechMapper" ref="stsTokenTechMapper" />
        <property name="stsSupplyGroupMapper" ref="stsSupplyGroupMapper" />
        <property name="customerMapper" ref="customerMapper" />
        <property name="customerAccountMapper" ref="customerAccountMapper" />
        <property name="customerAgreementMapper" ref="customerAgreementMapper" />
        <property name="locationMapper" ref="locationMapper" />
        <property name="usagePointMapper" ref="usagePointMapper" />
        <property name="unitsAccountMapper" ref="unitsAccountMapper" />
        <property name="upPricingStructureMapper" ref="upPricingStructureMapper" />
        <property name="upMeterInstallMapper" ref="upMeterInstallMapper" />
        <property name="upGenGroupLnkMapper" ref="upGenGroupLnkMapper" />
        <property name="smsService" ref="smsService"/>
        <property name="messageSource" ref="messageSource" />
        <property name="appSettingService" ref="appSettingService"/>
        <property name="tokenGenerationService" ref="tokenGenerationService"/>
        <property name="mdcService" ref="mdcService"/>
    </bean>

    <!-- BlockingTypeService -->
    <bean id="blockingTypeService" class="za.co.ipay.metermng.server.mybatis.service.BlockingTypeService">
        <property name="blockingTypeMapper" ref="blockingTypeMapper" />
    </bean>
    <bean id="blockingTypeHistService" class="za.co.ipay.metermng.server.mybatis.service.BlockingTypeHistService">
        <property name="blockingTypeHistMapper" ref="blockingTypeHistMapperImpl" />
    </bean>

    <!-- ImportFileDataService -->
    <bean id="importFileDataService" class="za.co.ipay.metermng.server.mybatis.service.ImportFileDataService">
        <property name="importFileMapper" ref="importFileMapper"/>
        <property name="importFileTypeMapper" ref="importFileTypeMapper"/>
        <property name="importFileItemMapper" ref="importFileItemMapper"/>
        <property name="importFileItemImportMapper" ref="importFileItemImportMapper"/>
        <property name="defaultFileImportService" ref="defaultFileImportService"/>
        <property name="importFileCustomMapper" ref="importFileCustomMapper"/>
        <property name="pricingStructureService" ref="pricingStructureService"/>
        <property name="ipayXmlMessageService" ref="defaultIpayXmlMessageService"/>
        <property name="engineeringTokensService" ref="engineeringTokensService"/>
        <property name="productInfoMapper" ref="productInfoMapper"/>
    </bean>

    <!-- DefaultFileImportService -->
    <bean id="defaultFileImportService" class="za.co.ipay.metermng.fileimport.DefaultFileImportService">
        <property name="sqlSessionFactory" ref="sqlSessionFactoryUnmanaged" />
        <property name="mybatisHistoryProxyFactory" ref="meterMngHistoryProxyFactory"/>
    </bean>

    <bean id="tokenGenerationService" class="za.co.ipay.metermng.server.mybatis.service.TokenGenerationService">
        <property name="ipayXmlMessageService" ref="defaultIpayXmlMessageService"/>
        <property name="currentUserNameProvider" ref="currentUserNameProvider"/>
        <property name="specialActionReasonsMapper" ref="specialActionReasonsMapper"/>
    </bean>

    <bean id="userInterfaceService" class="za.co.ipay.metermng.server.mybatis.service.UserInterfaceService">
        <property name="formMapper" ref="formMapper"/>
        <property name="formFieldsMapper" ref="formFieldsMapper"/>
    </bean>

    <!-- ******************** -->
    <!-- *** End Services *** -->
    <!-- ******************** -->

    <!-- ************************* -->
    <!-- *** Start scheduling ***  -->
    <!-- ************************* -->

    <!-- Scheduling factory bean used for setting up scheduling manually -->
    <bean id="scheduler" class="org.springframework.scheduling.quartz.SchedulerFactoryBean">
        <property name="quartzProperties">
            <props>
                <prop key="org.quartz.jobStore.class">org.quartz.simpl.RAMJobStore</prop>
                <prop key="org.quartz.threadPool.class">org.quartz.simpl.SimpleThreadPool</prop>
                <prop key="org.quartz.threadPool.threadCount">10</prop>
                <prop key="org.quartz.scheduler.skipUpdateCheck">true</prop>
            </props>
        </property>
    </bean>

    <!-- ************************* -->
    <!-- *** End scheduling   ***  -->
    <!-- ************************* -->

    <!-- ************************* -->
    <!-- *** Start email ***       -->
    <!-- ************************* -->

    <jee:jndi-lookup resource-ref="true" id="session" jndi-name="mail/Session" expected-type="javax.mail.Session"/>

    <bean id="emailService" class="za.co.ipay.metermng.server.mybatis.service.EmailService">
        <property name="session" ref="session" />
    </bean>

    <bean id="securityServiceImpl" class="za.co.ipay.metermng.network.security.SecurityServiceImpl" />

    <bean id="messageServiceImpl" class="za.co.ipay.metermng.network.MessageServiceImpl">
        <property name="currentServer" ref="defaultServer" />
        <property name="smsServer" ref="smsServer" />
        <property name="securityService" ref="securityServiceImpl" />
    </bean>

    <bean id="smsService" class="za.co.ipay.metermng.service.SmsServiceImpl">
        <property name="messageService" ref="messageServiceImpl" />
    </bean>

    <!-- ************************* -->
    <!-- *** End email ***         -->
    <!-- ************************* -->

    <!-- ************************* -->
    <!-- *** Start Controllers *** -->
    <!-- ************************* -->

    <bean id="searchRpc" class="za.co.ipay.metermng.server.rpc.SearchRpcImpl">
        <property name="stsMeterService" ref="stsMeterService"/>
        <property name="customerService" ref="customerService"/>
        <property name="customerAgreementService" ref="customerAgreementService"/>
        <property name="customerAccountService" ref="customerAccountService"/>
        <property name="usagePointService" ref="usagePointService"/>
        <property name="locationService" ref="locationService"/>
        <property name="upGenGroupLnkService" ref="upGenGroupLnkService"/>
        <property name="searchService" ref="searchService" />
        <property name="meterTypeService" ref="meterTypeService" />
        <property name="meterService" ref="meterService" />
        <property name="meterHistService" ref="meterHistService" />
        <property name="stsMeterHistService" ref="stsMeterHistService"/>
        <property name="usagePointHistService" ref="usagePointHistService" />
        <property name="customerHistService" ref="customerHistService" />
        <property name="meterModelService" ref="meterModelService" />
        <property name="deviceStoreService" ref="deviceStoreService"/>
        <property name="specialActionsService" ref="specialActionsService"/>
        <property name="onlineBulkMeterService" ref="onlineBulkMeterService" />
        <property name="blockingTypeService" ref="blockingTypeService" />
        <property name="auxAccountHistService" ref="auxAccountHistService" />
        <property name="unitTransService" ref="unitTransService" />
        <property name="groupService" ref="groupService" />
    </bean>

    <bean id="lookupRpc" class="za.co.ipay.metermng.server.rpc.LookupRpcImpl">
        <property name="lookupService" ref="lookupService"/>
    </bean>

    <bean id="usagePointRpc" class="za.co.ipay.metermng.server.rpc.UsagePointRpcImpl">
        <property name="usagePointService" ref="usagePointService"/>
        <property name="usagePointHistService" ref="usagePointHistService"/>
        <property name="customerTransService" ref="customerTransService"/>
        <property name="upGenGroupLnkService" ref="upGenGroupLnkService"/>
        <property name="mdcTransService" ref="mdcTransService" />
        <property name="specialActionsService" ref="specialActionsService"/>
    </bean>

    <bean id="customerRpc" class="za.co.ipay.metermng.server.rpc.CustomerRpcImpl">
        <property name="customerService" ref="customerService"/>
        <property name="auxAccountService" ref="auxAccountService"/>
        <property name="customerHistService" ref="customerHistService"/>
        <property name="usagePointService" ref="usagePointService"/>
    </bean>

    <bean id="locationRpc" class="za.co.ipay.metermng.server.rpc.LocationRpcImpl">
        <property name="locationService" ref="locationService"/>
        <property name="groupService" ref="groupService"/>
    </bean>

    <bean id="meterRpc" class="za.co.ipay.metermng.server.rpc.MeterRpcImpl">
        <property name="stsMeterService" ref="stsMeterService"/>
        <property name="meterTypeService" ref="meterTypeService"/>
        <property name="stsMeterHistService" ref="stsMeterHistService"/>
        <property name="meterHistService" ref="meterHistService"/>
        <property name="customerTransService" ref="customerTransService"/>
        <property name="customerService" ref="customerService"/>
        <property name="engineeringTokensService" ref="engineeringTokensService"/>
        <property name="deviceStoreService" ref="deviceStoreService"/>
        <property name="meterService" ref="meterService"/>
        <property name="meterModelService" ref="meterModelService" />
        <property name="mdcTransService" ref="mdcTransService" />
        <property name="registerReadingService" ref="registerReadingService"/>
        <property name="registerReadingCustomMapper" ref="registerReadingCustomMapper" />
        <property name="mdcChannelCustomMapper" ref="mdcChannelCustomMapper" />
    </bean>

    <bean id="keyIndicatorRpc" class="za.co.ipay.metermng.server.rpc.KeyIndicatorRpcImpl">
        <property name="keyIndicatorService" ref="keyIndicatorService"/>
    </bean>

    <bean id="usagePointGroupsRpc" class="za.co.ipay.metermng.server.rpc.UsagePointGroupsRpcImpl">
        <property name="usagePointGroupsService" ref="usagePointGroupsService"/>
    </bean>

    <bean id="salesPerResourceRpc" class="za.co.ipay.metermng.server.rpc.SalesPerResourceRpcImpl">
        <property name="salesPerResourceService" ref="salesPerResourceService"/>
    </bean>

    <bean id="dashBoardRpc" class="za.co.ipay.metermng.server.rpc.DashBoardRpcImpl">
        <property name="vendingActivityService" ref="vendingActivityService"/>
        <property name="dashBoardService" ref="dashBoardService"/>
    </bean>

    <bean id="pricingStructureRpc" class="za.co.ipay.metermng.server.rpc.PricingStructureRpcImpl">
        <property name="pricingStructureService" ref="pricingStructureService"/>
    </bean>

    <bean id="tokenGenerationRpc" class="za.co.ipay.metermng.server.rpc.TokenGenerationRpcImpl">
        <property name="auxFreeIssue" ref="auxFreeIssue"/>
        <property name="displayTokens" ref="displayTokens"/>
        <property name="tokenGenerationService" ref="tokenGenerationService"/>
    </bean>

    <bean id="auxChargeScheduleRpc" class="za.co.ipay.metermng.server.rpc.AuxChargeScheduleRpcImpl">
        <property name="auxChargeScheduleService" ref="auxChargeScheduleService"/>
    </bean>

    <bean id="auxTypeRpc" class="za.co.ipay.metermng.server.rpc.AuxTypeRpcImpl">
        <property name="auxTypeService" ref="auxTypeService"/>
        <property name="auxAccountService" ref="auxAccountService"/>
    </bean>

    <bean id="supplyGroupRpc" class="za.co.ipay.metermng.server.rpc.SupplyGroupRpcImpl">
        <property name="supplyGroupService" ref="supplyGroupService"/>
    </bean>

    <bean id="auxAccountRpc" class="za.co.ipay.metermng.server.rpc.AuxAccountsRpcImpl">
        <property name="auxAccountService" ref="auxAccountService"/>
    </bean>

    <bean id="groupRpc" class="za.co.ipay.metermng.server.rpc.GroupRpcImpl">
        <property name="groupService" ref="groupService"/>
        <property name="appSettingService" ref="appSettingService"/>
        <property name="accessControlService" ref="accessControlService" />
    </bean>

    <bean id="deviceStoreRpc" class="za.co.ipay.metermng.server.rpc.DeviceStoreRpcImpl">
        <property name="deviceStoreService" ref="deviceStoreService"/>
    </bean>

    <bean id="userRpc" class="za.co.ipay.metermng.server.rpc.UserRpcImpl">
        <property name="userGroupService" ref="userGroupService"/>
        <property name="groupService" ref="groupService" />
        <property name="accessControlService" ref="accessControlService"/>
        <property name="passwordEncoder" ref="passwordEncoder" />
        <property name="passwordValidationPolicy" ref="passwordValidationPolicy" />
    </bean>

    <bean id="calendarRpc" class="za.co.ipay.metermng.server.rpc.CalendarRpcImpl">
        <property name="calendarService" ref="calendarService"/>
    </bean>

    <bean id="manufacturerRpc" class="za.co.ipay.metermng.server.rpc.ManufacturerRpcImpl">
        <property name="manufacturerService" ref="manufacturerService"/>
    </bean>

    <bean id="mdcRpc" class="za.co.ipay.metermng.server.rpc.MdcRpcImpl">
        <property name="mdcService" ref="mdcService"/>
    </bean>

    <bean id="mdcChannelRpc" class="za.co.ipay.metermng.server.rpc.MdcChannelRpcImpl">
        <property name="mdcChannelService" ref="mdcChannelService"/>
        <property name="timeIntervalService" ref="timeIntervalService"/>
    </bean>

    <bean id="billingDetRpc" class="za.co.ipay.metermng.server.rpc.BillingDetRpcImpl">
        <property name="billingDetService" ref="billingDetService"/>
    </bean>

    <bean id="meterModelRpc" class="za.co.ipay.metermng.server.rpc.MeterModelRpcImpl">
        <property name="meterModelService" ref="meterModelService"/>
    </bean>

    <bean id="appSettingRpc" class="za.co.ipay.metermng.server.rpc.AppSettingRpcImpl">
        <property name="appSettingService" ref="appSettingService"/>
    </bean>

    <bean id="ndpRpc" class="za.co.ipay.metermng.server.rpc.NdpRpcImpl">
        <property name="ndpService" ref="ndpService"/>
    </bean>

    <bean id="scheduleRpc" class="za.co.ipay.metermng.server.rpc.ScheduleRpcImpl">
        <property name="scheduleService" ref="scheduleService"/>
        <property name="schedulingService" ref="schedulingService"/>
    </bean>

    <bean id="specialActionsRpc" class="za.co.ipay.metermng.server.rpc.SpecialActionsRpcImpl">
        <property name="specialActionsService" ref="specialActionsService"/>
    </bean>

    <bean id="notificationRpc" class="za.co.ipay.metermng.server.rpc.NotificationRpcImpl">
        <property name="emailService" ref="emailService"/>
        <property name="smsService" ref="smsService"/>
    </bean>

    <bean id="blockingTypeRpc" class="za.co.ipay.metermng.server.rpc.BlockingTypeRpcImpl">
        <property name="blockingTypeService" ref="blockingTypeService"/>
    </bean>

    <bean id="importFileDataRpc" class="za.co.ipay.metermng.server.rpc.ImportFileDataRpcImpl">
        <property name="importFileDataService" ref="importFileDataService"/>
    </bean>

    <bean id="userInterfaceRpc" class="za.co.ipay.metermng.server.rpc.UserInterfaceRpcImpl">
        <property name="userInterfaceService" ref="userInterfaceService"/>
    </bean>

    <!-- *********************** -->
    <!-- *** End Controllers *** -->
    <!-- *********************** -->

    <!--
    Here we specify defaults for context init params, localOverride=false means these properties
    will be used if the init param by the same name is not found in the context.xml or web.xml files.
    -->
    <bean class="org.springframework.context.support.PropertySourcesPlaceholderConfigurer">
        <property name="localOverride" value="false"/>
        <property name="properties">
            <value>
                messagesLocation = classpath:messages
                formatsLocation = classpath:formats
                timezonesLocation = classpath:timezones
                defaultLocale = en_ZA
                useDefaultLocaleOnly=false
                ldapUrl = ldap://localhost:389
                ldapUserPattern = NONE
                demoMode = false
                logoUrl = styles/ipay/ipay-logo.png
                cssUrl = styles/ipay/ipay-MeterMngAdmin.css
                enableSTS = true
                useMancoLogo = false
                fallbackToSystemLocale = true
                enableCentianSTS = false
                enableNonBillable = false
                allowReversalsLastTrans = false
                allowReversalsOlderTrans = false
                enableAccessGroups = false
                googleMapsKey=
                storeLocations=
            </value>
        </property>
    </bean>

    <bean id="messageSource" class="za.co.ipay.gwt.common.server.util.ExposedReloadableResourceBundleMessageSource">
        <property name="basename"><value>${messagesLocation}</value></property>
        <property name="defaultEncoding" value="UTF-8" />
        <property name="fallbackToSystemLocale"><value>${fallbackToSystemLocale}</value></property>
    </bean>

    <bean id="formatSource" class="za.co.ipay.gwt.common.server.util.ExposedReloadableResourceBundleMessageSource">
        <property name="basename"><value>${formatsLocation}</value></property>
        <property name="defaultEncoding" value="UTF-8" />
        <property name="fallbackToSystemLocale"><value>${fallbackToSystemLocale}</value></property>
    </bean>

    <bean id="timezoneSource" class="za.co.ipay.gwt.common.server.util.ExposedReloadableResourceBundleMessageSource">
        <property name="basename"><value>${timezonesLocation}</value></property>
        <property name="defaultEncoding" value="UTF-8" />
        <property name="fallbackToSystemLocale"><value>${fallbackToSystemLocale}</value></property>
    </bean>


    <bean id="importMessageSource" class="za.co.ipay.gwt.common.server.util.ExposedReloadableResourceBundleMessageSource">
        <property name="basename"><value>${messagesLocation}</value></property>
        <property name="defaultEncoding" value="UTF-8" />
        <property name="fallbackToSystemLocale"><value>${fallbackToSystemLocale}</value></property>
    </bean>

    <bean id="meterMngConfig" class="za.co.ipay.metermng.shared.util.MeterMngConfig">
        <property name="demoMode"><value>${demoMode}</value></property>
        <property name="logoUrl"><value>${logoUrl}</value></property>
        <property name="cssUrl"><value>${cssUrl}</value></property>
        <property name="enableSTS"><value>${enableSTS}</value></property>
        <property name="enableCentianSTS"><value>${enableCentianSTS}</value></property>
        <property name="useMancoLogo"><value>${useMancoLogo}</value></property>
        <property name="allowReversalsLastTrans"><value>${allowReversalsLastTrans}</value></property>
        <property name="allowReversalsOlderTrans"><value>${allowReversalsOlderTrans}</value></property>
        <property name="googleMapsKey"><value>${googleMapsKey}</value></property>
        <property name="storeLocations"><value>${storeLocations}</value></property>
        <property name="enableNonBillable"><value>${enableNonBillable}</value></property>
        <property name="enableAccessGroups"><value>${enableAccessGroups}</value></property>
    </bean>

    <bean id="loginListener" class="za.co.ipay.metermng.server.access.MeterMngLoginListener">
        <property name="accessControlService" ref="accessControlService" />
        <property name="userGroupService" ref="userGroupService"/>
        <property name="groupService" ref="groupService"/>
        <property name="enableAccessGroups"><value>${enableAccessGroups}</value></property>
    </bean>

    <bean id="sessionAuthorizationListener" class="za.co.ipay.accesscontrol.gwt.server.listener.SessionAuthorizationListener">
        <property name="userSessionAttribute" value="meterMngUser" />
    </bean>
</beans>
