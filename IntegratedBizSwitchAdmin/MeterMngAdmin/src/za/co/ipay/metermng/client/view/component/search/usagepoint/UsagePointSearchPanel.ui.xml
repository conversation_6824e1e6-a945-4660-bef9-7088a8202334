<!DOCTYPE ui:UiBinder SYSTEM "http://dl.google.com/gwt/DTD/xhtml.ent">
<ui:UiBinder xmlns:ui="urn:ui:com.google.gwt.uibinder" 
             xmlns:g="urn:import:com.google.gwt.user.client.ui"
             xmlns:p3="urn:import:za.co.ipay.gwt.common.client.form"
             xmlns:pricingstructure="urn:import:za.co.ipay.metermng.client.widget.pricingstructure">
	<ui:style>
	    .spacingBelow {
            margin-bottom: 1px;
            padding_bottom: 0px;
        }
	</ui:style>

    <ui:with field="msg" type="za.co.ipay.metermng.client.i18n.UiMessages" />

    <g:FlowPanel>
        <p3:FormRowPanel>
            <p3:FormElement ui:field="nameElement" labelText="{msg.getSearchUsagePointName}:">
                <g:TextBox ui:field="nameBox" maxLength="255" visibleLength="25" debugId="usagePointNameBox"/>
            </p3:FormElement>
        </p3:FormRowPanel>
        <p3:FormRowPanel ui:field="customText1Row">
            <p3:FormElement ui:field="customText1Element">
                <g:TextBox ui:field="customText1Box" maxLength="255" visibleLength="25" debugId="customText1Box"/>
            </p3:FormElement>
        </p3:FormRowPanel>
       <p3:FormRowPanel>
            <p3:FormElement ui:field="pricingStructureElement" labelText="{msg.getUsagePointPricingStructure}: ">
                <pricingstructure:PricingStructureLookup debugId="pricingStructureLookup"
                                                         ui:field="pricingStructureLookup"/>
            </p3:FormElement>
        </p3:FormRowPanel>
        <p3:FormRowPanel>
            <p3:FormElement ui:field="paymentModeElement" labelText="{msg.getPaymentMode}:">
                <g:ListBox visibleItemCount="2" ui:field="lstbxPaymentMode" styleName="gwt-ListBox-ipay" multipleSelect="true" debugId="lstbxPaymentMode"/>
            </p3:FormElement>
        </p3:FormRowPanel>
        <g:HTMLPanel>
            <p3:FormElement ui:field="usagePointNoCustElement">
                <g:CheckBox text="{msg.getSearchUsagePointNoCustomer}" checked="false" ui:field="chckbxUsagePointNoCust" debugId="chckbxUsagePointNoCust"/>
            </p3:FormElement>                   
        </g:HTMLPanel>
        <p3:FormRowPanel>
            <p3:FormElement ui:field="usagePointNoMeterElement" >
                <g:CheckBox text="{msg.getSearchUsagePointNoMeter}" checked="false" ui:field="chckbxUsagePointNoMeter" debugId="chckbxUsagePointNoMeter"/>
            </p3:FormElement>                   
        </p3:FormRowPanel>
        <p3:FormRowPanel>
          <p3:FormElement ui:field="searchTypeElement" labelText="{msg.getSearchType}:">
                 <g:RadioButton ui:field="startWithBox" name="usagePointSearchTypeBox" value="true" enabled="true" text="{msg.getSearchStartsWith}" debugId="usagePointStartWithBox"/>
                 <g:RadioButton ui:field="containsBox" name="usagePointSearchTypeBox" value="false" enabled="true" text="{msg.getSearchContains}" debugId="usagePointContainsBox"/> 
            </p3:FormElement>
        </p3:FormRowPanel>
    </g:FlowPanel>

</ui:UiBinder> 