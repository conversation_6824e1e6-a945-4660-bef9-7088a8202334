package za.co.ipay.metermng.client.rpc;

import java.util.Date;
import java.util.List;

import com.google.gwt.user.client.rpc.RemoteService;
import com.google.gwt.user.client.rpc.RemoteServiceRelativePath;

import za.co.ipay.gwt.common.shared.exception.ServiceException;
import za.co.ipay.metermng.mybatis.generated.model.ImportFile;
import za.co.ipay.metermng.mybatis.generated.model.ImportFileItem;
import za.co.ipay.metermng.mybatis.generated.model.ImportFileType;
import za.co.ipay.metermng.shared.dto.importfile.ImportFileActionParamsDto;
import za.co.ipay.metermng.shared.dto.importfile.ImportFileDto;
import za.co.ipay.metermng.shared.dto.importfile.ImportFileItemDto;
import za.co.ipay.metermng.shared.dto.importfile.ImportFileItemListDto;
import za.co.ipay.metermng.shared.dto.importfile.ImportFileListDto;
import za.co.ipay.metermng.shared.dto.importfile.ImportFileResultDto;
import za.co.ipay.metermng.shared.dto.importfile.KeyChangeDto;

@RemoteServiceRelativePath("secure/importfiledata.do")
public interface ImportFileDataRpc extends RemoteService {

    public List<ImportFileType> getActiveImportFileTypes(boolean enableAccessGroups) throws ServiceException;
    
    public ImportFileType getImportFileTypeById(Long importFileTypeId);
    
    public ImportFileDto getImportFileDtoById(Long importFileId);
    
    public ImportFileActionParamsDto getImportFileParamData(String userName, String typeClass, String importFileName);

    public ImportFileListDto selectImportFiles(int start, int pageSize, String sortColumn, String filterColumn, String filterString, Date filterDate, String order, boolean enableAccessGroups);
    
    public ImportFileItemListDto selectImportFileItems(Long importFileId, int start, int pageSize, String sortColumn, String filterColumn, String filterString, Date filterDate, String order);
    
    public String updateImportFileItem(ImportFileItemDto importFileItem) throws Exception;
    
    public void updateImportFileParams(ImportFileActionParamsDto dto) throws ServiceException;
    
    public ImportFileResultDto importSelectedItems(String username, ImportFile importFile, List<Long> selectedItemsList, boolean isAccessGroupsEnabled);
    
    public ImportFileResultDto importAllItems(String username, ImportFile importFile, boolean isAccessGroupsEnabled);
    
    public List<ImportFileItem> extractFailedItems(ImportFile importFile);
    
    public List<KeyChangeDto> getKeyChangeTokensforBulkRef(String bulkRef);
    
    public void stopImportAll(String username, ImportFile importFile);
}
