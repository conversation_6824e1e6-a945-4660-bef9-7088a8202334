<!DOCTYPE ui:UiBinder SYSTEM "http://dl.google.com/gwt/DTD/xhtml.ent">
<ui:UiBinder xmlns:ui="urn:ui:com.google.gwt.uibinder" xmlns:g="urn:import:com.google.gwt.user.client.ui"
	xmlns:ipay="urn:import:za.co.ipay.gwt.common.client.widgets">
	<ui:style>
	
	</ui:style>

	<ui:with field="msg" type="za.co.ipay.metermng.client.i18n.UiMessages" />

	<g:FlowPanel>

		<g:FlowPanel styleName="bordered">
			<g:Label text="{msg.getCustomerSearchInstructions}" styleName="gwt-Label-bold" wordWrap="true" ui:field="instruction" />
			<ipay:IpayListBox debugId="customerSearchTypeBox" visibleItemCount="1" ui:field="lstbxSearchType" styleName="gwt-ListBox-ipay" multipleSelect="false" />
			<g:SuggestBox debugId="suggestBoxCustomerSurname" ui:field="suggestBoxCustomerSurname" animationEnabled="true" autoSelectEnabled="false" styleName="gwt-TextBox topSpaced searchBox" />
			<g:SuggestBox debugId="suggestBoxCustomerIdNumber" ui:field="suggestBoxCustomerIdNumber" animationEnabled="true" autoSelectEnabled="false" visible="false" styleName="gwt-TextBox topSpaced searchBox" />
			<g:SuggestBox debugId="customerAgreementSearchBox" ui:field="suggestBoxCustomerAgreement" animationEnabled="true" autoSelectEnabled="false" visible="false" styleName="gwt-TextBox topSpaced searchBox" />
            <g:SuggestBox debugId="accountNameSearchBox" ui:field="suggestBoxAccountName" animationEnabled="true" autoSelectEnabled="false" visible="false" styleName="gwt-TextBox topSpaced searchBox" />
			<g:Image debugId="customerSearchImage" styleName="gwt-Image-search" ui:field="imgSearch" />
			<ipay:Message debugId="customerSearchMessage" width="450" height="15" type="1" text="message" styleName="error" ui:field="errormessage" />
		</g:FlowPanel>
	</g:FlowPanel>

</ui:UiBinder> 