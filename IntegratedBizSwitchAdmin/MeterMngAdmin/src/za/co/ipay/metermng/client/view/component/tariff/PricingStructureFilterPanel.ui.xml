<!DOCTYPE ui:UiBinder SYSTEM "http://dl.google.com/gwt/DTD/xhtml.ent">
<ui:UiBinder xmlns:ui="urn:ui:com.google.gwt.uibinder"
    xmlns:g="urn:import:com.google.gwt.user.client.ui" xmlns:p1="urn:import:za.co.ipay.gwt.common.client.form"
    xmlns:p2="urn:import:com.google.gwt.user.datepicker.client">

    <ui:style>

    </ui:style>

    <ui:with field="msg" type="za.co.ipay.metermng.client.i18n.UiMessages" />
    <g:HTMLPanel ui:field="filterPanel">
    <g:HTML ui:field="filterPanelTitle" text="" styleName="pageSectionTitle" />
    <g:HorizontalPanel spacing="3">
        <g:Cell verticalAlignment="ALIGN_MIDDLE">
            <g:Label text="{msg.getMeterTxnFilter}:" styleName="gwt-Label-bold" />
        </g:Cell>
        <g:Cell verticalAlignment="ALIGN_MIDDLE">
            <g:ListBox visibleItemCount="1" name="Filter" ui:field="filterDropdown" />
        </g:Cell>
        <g:Cell verticalAlignment="ALIGN_MIDDLE">
            <g:TextBox ui:field="txtbxfilter" />
        </g:Cell>
        <g:Cell verticalAlignment="ALIGN_MIDDLE">
            <g:ListBox visibleItemCount="1" name="Filter" ui:field="statusFilterDropdown" visible="false"/>
        </g:Cell>
        <g:Cell verticalAlignment="ALIGN_MIDDLE">
            <g:ListBox visibleItemCount="1" name="Filter" ui:field="serviceResourceFilterDropdown" visible="false"/>
        </g:Cell>
        <g:Cell verticalAlignment="ALIGN_MIDDLE">
            <g:ListBox visibleItemCount="1" name="Filter" ui:field="meterTypeFilterDropdown" visible="false"/>
        </g:Cell>
        <g:Cell verticalAlignment="ALIGN_MIDDLE">
            <g:ListBox visibleItemCount="1" name="Filter" ui:field="paymentModeFilterDropdown" visible="false"/>
        </g:Cell>
    </g:HorizontalPanel>
    </g:HTMLPanel>
</ui:UiBinder> 