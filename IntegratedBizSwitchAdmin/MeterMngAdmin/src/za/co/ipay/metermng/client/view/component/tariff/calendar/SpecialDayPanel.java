package za.co.ipay.metermng.client.view.component.tariff.calendar;

import java.util.ArrayList;
import java.util.Comparator;
import java.util.Date;
import java.util.logging.Logger;

import com.google.gwt.cell.client.NumberCell;
import com.google.gwt.core.client.GWT;
import com.google.gwt.dom.client.BrowserEvents;
import com.google.gwt.event.dom.client.ClickEvent;
import com.google.gwt.event.dom.client.FocusEvent;
import com.google.gwt.event.dom.client.FocusHandler;
import com.google.gwt.event.logical.shared.SelectionEvent;
import com.google.gwt.event.logical.shared.SelectionHandler;
import com.google.gwt.i18n.client.DateTimeFormat;
import com.google.gwt.uibinder.client.UiBinder;
import com.google.gwt.uibinder.client.UiField;
import com.google.gwt.uibinder.client.UiHandler;
import com.google.gwt.user.cellview.client.CellTable;
import com.google.gwt.user.cellview.client.Column;
import com.google.gwt.user.cellview.client.ColumnSortEvent;
import com.google.gwt.user.cellview.client.ColumnSortEvent.ListHandler;
import com.google.gwt.user.cellview.client.TextColumn;
import com.google.gwt.user.client.ui.Button;
import com.google.gwt.user.client.ui.FlowPanel;
import com.google.gwt.user.client.ui.HorizontalPanel;
import com.google.gwt.user.client.ui.Label;
import com.google.gwt.user.client.ui.SuggestBox;
import com.google.gwt.user.client.ui.SuggestOracle;
import com.google.gwt.user.client.ui.SuggestOracle.Suggestion;
import com.google.gwt.user.client.ui.TextBox;
import com.google.gwt.user.client.ui.Widget;
import com.google.gwt.user.datepicker.client.CalendarModel;
import com.google.gwt.user.datepicker.client.DateBox;
import com.google.gwt.view.client.CellPreviewEvent;
import com.google.gwt.view.client.DefaultSelectionEventManager;
import com.google.gwt.view.client.ListDataProvider;
import com.google.gwt.view.client.SelectionChangeEvent;
import com.google.gwt.view.client.SingleSelectionModel;
import za.co.ipay.gwt.common.client.Dialogs;
import za.co.ipay.gwt.common.client.factory.ResourcesFactoryUtil;
import za.co.ipay.gwt.common.client.form.FormElement;
import za.co.ipay.gwt.common.client.form.Format.FormatUtil;
import za.co.ipay.gwt.common.client.form.HasDirtyData;
import za.co.ipay.gwt.common.client.handler.ConfirmHandler;
import za.co.ipay.gwt.common.client.handler.FormDataChangeHandler;
import za.co.ipay.gwt.common.client.handler.FormDataValueChangeHandler;
import za.co.ipay.gwt.common.client.resource.Messages.MessagesUtil;
import za.co.ipay.gwt.common.client.validation.ClientValidatorUtil;
import za.co.ipay.gwt.common.client.widgets.StrictDateFormat;
import za.co.ipay.gwt.common.client.widgets.TablePager;
import za.co.ipay.gwt.common.client.workspace.SessionCheckCallback;
import za.co.ipay.gwt.common.client.workspace.Workspace;
import za.co.ipay.metermng.client.event.DayProfileUpdatedEvent;
import za.co.ipay.metermng.client.event.DayProfileUpdatedEventHandler;
import za.co.ipay.metermng.client.factory.ClientFactory;
import za.co.ipay.metermng.client.resource.image.MediaResource.MediaResourceUtil;
import za.co.ipay.metermng.client.rpc.callback.ClientCallback;
import za.co.ipay.metermng.client.view.component.BaseComponent;
import za.co.ipay.metermng.shared.DayProfileSuggestion;
import za.co.ipay.metermng.shared.DayProfileSuggestionsOracle;
import za.co.ipay.metermng.shared.TouCalendarData;
import za.co.ipay.metermng.shared.TouSpecialDayData;

public class SpecialDayPanel extends BaseComponent {

    private static final int DEFAULT_PAGE_SIZE = 15;

    @UiField(provided=true) CellTable<TouSpecialDayData> specialdayTable;
    @UiField TablePager specialdayPager;
    @UiField Label formHeading;
    @UiField TextBox nameTextBox;
    @UiField DateBox sdDateBox;
    @UiField(provided=true) SuggestBox dayProfile;
    @UiField FlowPanel formPanel;
    @UiField FormElement nameElement;
    @UiField FormElement dateElement;
    @UiField FormElement dayProfileElement;
    @UiField Label errorMsg;
    @UiField HorizontalPanel buttons;
    @UiField Button btnSave;
    @UiField Button btnCancel;
    @UiField Button btnDelete;

    private ListDataProvider<TouSpecialDayData> dataProvider;
    private SingleSelectionModel<TouSpecialDayData> selectionModel;
    private TouCalendarData calendarData;
    private TouSpecialDayData specialDay;
    private DayProfileSuggestion selectedSuggestion;

    private TextColumn<TouSpecialDayData> specialDayName;
    private TextColumn<TouSpecialDayData> yearColumn;
    private TextColumn<TouSpecialDayData> monthColumn;
    private TextColumn<TouSpecialDayData> dayProfileName;

    private ArrayList<TouSpecialDayData> specialDaysList;
    private CalendarModel calmod;

    protected HasDirtyData hasDirtyData;
    protected boolean readOnly = false;

    private static Logger logger = Logger.getLogger(SpecialDayPanel.class.getName());
    private CalendarContainer parentContainer;
    ListHandler<TouSpecialDayData> columnSortHandler;

    private static SpecialDayPanelUiBinder uiBinder = GWT.create(SpecialDayPanelUiBinder.class);

    interface SpecialDayPanelUiBinder extends UiBinder<Widget, SpecialDayPanel> {
    }

    public SpecialDayPanel(ClientFactory clientFactory, CalendarContainer parentContainer) {
        this.clientFactory = clientFactory;
        this.parentContainer = parentContainer;
        dayProfile = new SuggestBox(clientFactory.getDayProfileSuggestionsOracle());


        dayProfile.addSelectionHandler(new SelectionHandler<SuggestOracle.Suggestion>() {
            @Override
            public void onSelection(SelectionEvent<Suggestion> event) {
                Suggestion selectedItem = event.getSelectedItem();
                //cast returned suggestion
                selectedSuggestion = (DayProfileSuggestion) selectedItem;
                // it seems the ValueChangeEvent not fired for selections
                // same happens in AssignDayProfilesPanel's SuggestBoxes
                hasDirtyData.setDirtyData(true);
            }
        });
        dayProfile.getValueBox().addFocusHandler(new FocusHandler() {

            @Override
            public void onFocus(FocusEvent event) {
                dayProfile.showSuggestionList();
            }
        });
        calmod = new CalendarModel();
        createTable();
        initWidget(uiBinder.createAndBindUi(this));
        this.hasDirtyData = ((Workspace) parentContainer).createAndRegisterHasDirtyData();
        initTable();
        StrictDateFormat strictDateFormat = new StrictDateFormat(DateTimeFormat.getFormat(FormatUtil.getInstance().getDateFormat()));
        sdDateBox.setFormat(strictDateFormat);

        setSpecialDay(null);
        dayProfile.setWidth("3em");

        clientFactory.getEventBus().addHandler(DayProfileUpdatedEvent.TYPE, new DayProfileUpdatedEventHandler() {
            @Override
            public void processDayProfileUpdatedEvent(DayProfileUpdatedEvent event) {
                refreshTable();
            }
        });

        addFieldHandlers();
    }

    protected void createTable() {
        if (ResourcesFactoryUtil.getInstance() != null && ResourcesFactoryUtil.getInstance().getCellTableResources() != null) {
            specialdayTable = new CellTable<TouSpecialDayData>(DEFAULT_PAGE_SIZE, ResourcesFactoryUtil.getInstance().getCellTableResources());
        } else {
            specialdayTable = new CellTable<TouSpecialDayData>(DEFAULT_PAGE_SIZE);
        }
    }

    public void setTouCalendarData(TouCalendarData calendarData) {
        this.calendarData = calendarData;
        if (calendarData == null || calendarData.getId()==null) {
            clearFields();
        } else {
            ((DayProfileSuggestionsOracle)dayProfile.getSuggestOracle()).setCalendarId(calendarData.getId());
            refreshTable();
        }
    }

    protected void initTable() {
        if (dataProvider == null) {
            specialDayName = new TextColumn<TouSpecialDayData>() {
                @Override
                public String getValue(TouSpecialDayData data) {
                    if (data.getName() != null) {
                        return data.getName();
                    }
                    return " ? ";
                }
            };
            specialDayName.setSortable(true);

            yearColumn = new TextColumn<TouSpecialDayData>() {
                @Override
                public String getValue(TouSpecialDayData data) {
                    if (data.getSdYear() != null) {
                        return data.getSdYear() + "";
                    }
                    return " ? ";
                }
            };
            yearColumn.setSortable(true);

            monthColumn = new TextColumn<TouSpecialDayData>() {
                @Override
                public String getValue(TouSpecialDayData data) {
                    if (data.getSdMonth() != null) {
                        return calmod.formatMonth(data.getSdMonth()-1);//we use 1 for jan calmod uses 0
                    }
                    return " ? ";
                }
            };
            monthColumn.setSortable(true);

            Column<TouSpecialDayData, Number> dayColumn = new Column<TouSpecialDayData, Number>(new NumberCell()) {
                @Override
                public Integer getValue(TouSpecialDayData data) {
                    return data.getSdDay();
                }
            };

            dayProfileName = new TextColumn<TouSpecialDayData>() {
                @Override
                public String getValue(TouSpecialDayData data) {
                    if (data.getTouDayProfileId() != null) {
                        return data.getDayProfile().getName()+" ("+data.getDayProfile().getCode()+")";
                    }
                    return " ? ";
                }
            };
            dayProfileName.setSortable(true);

            // Add the columns.
            specialdayTable.addColumn(specialDayName, MessagesUtil.getInstance().getMessage("calendar.specialday.field.name"));
            specialdayTable.addColumn(yearColumn, MessagesUtil.getInstance().getMessage("calendar.specialday.field.year"));
            specialdayTable.addColumn(monthColumn, MessagesUtil.getInstance().getMessage("calendar.specialday.field.month"));
            specialdayTable.addColumn(dayColumn, MessagesUtil.getInstance().getMessage("calendar.specialday.field.day"));
            specialdayTable.addColumn(dayProfileName, MessagesUtil.getInstance().getMessage("calendar.specialday.field.dayprofile"));

            dataProvider = new ListDataProvider<TouSpecialDayData>();
            dataProvider.addDataDisplay(specialdayTable);
            specialdayPager.setDisplay(specialdayTable);
            specialdayTable.setPageSize(getPageSize());


            logger.info("Created Special Day table");
        }

        selectionModel = new SingleSelectionModel<TouSpecialDayData>();
        CellPreviewEvent.Handler<TouSpecialDayData> handler = new CellPreviewEvent.Handler<TouSpecialDayData>() {
            final CellPreviewEvent.Handler<TouSpecialDayData> selectionEventManager = DefaultSelectionEventManager.createDefaultManager();
            @Override
            public void onCellPreview(final CellPreviewEvent<TouSpecialDayData> event) {
                    if (BrowserEvents.CLICK.equals(event.getNativeEvent().getType())) {
                        if (!event.isCanceled()) {
                            if (hasDirtyData.isDirtyData()) {
                                Dialogs.confirmDiscardChanges(new ConfirmHandler() {
                                            @Override
                                            public void confirmed(boolean confirm) {
                                                if (confirm) {
                                                    hasDirtyData.setDirtyData(false);
                                                    event.getDisplay().getSelectionModel().setSelected(event.getValue(), true);
                                                    logger.info("Discarding changes for selection event");
                                                } else {
                                                    event.setCanceled(true);
                                                    logger.info("Cancelled selection event, staying with current selection");
                                                }

                                            }});
                            } else {
                                selectionEventManager.onCellPreview(event);
                            }

                        }
                } else {
                    selectionEventManager.onCellPreview(event);
                }
            }
        };
        specialdayTable.setSelectionModel(selectionModel, handler);
        selectionModel.addSelectionChangeHandler(new SelectionChangeEvent.Handler() {
            public void onSelectionChange(SelectionChangeEvent event) {

                final TouSpecialDayData selected = selectionModel.getSelectedObject();
                if (selected != null) {
                    setSpecialDay(selected);
                    specialdayTable.getSelectionModel().setSelected(selected, true);
                }
            }
        });
    }

    public void clearTableSelection() {
        TouSpecialDayData selected = selectionModel.getSelectedObject();
        if (selected != null) {
            selectionModel.setSelected(selected, false);
        }
    }

    public void refreshTable() {
        clientFactory.getCalendarRpc().getSpecialDays(calendarData.getId(), new ClientCallback<ArrayList<TouSpecialDayData>>() {
            @Override
            public void onSuccess(ArrayList<TouSpecialDayData> result) {
                logger.info("Got special days: " + result.size());
                if (dataProvider != null && dataProvider.getList() != null) {
                    dataProvider.getList().clear();
                    dataProvider.getList().addAll(result);
                    setSpecialDayList(result);
                }
            }
        });
    }

    public void setSpecialDayList(ArrayList<TouSpecialDayData> thedata) {
        logger.info("Displaying Special Days: "+thedata.size());
        specialDaysList = thedata;
        dataProvider.getList().clear();
        dataProvider.getList().addAll(specialDaysList);
        dataProvider.refresh();
        if (columnSortHandler == null || columnSortHandler.getList() == null) {
            columnSortHandler = new ListHandler<TouSpecialDayData>(dataProvider.getList());
            columnSortHandler.setComparator(specialDayName, new Comparator<TouSpecialDayData>() {
                public int compare(TouSpecialDayData o1, TouSpecialDayData o2) {
                    if (o1 == o2) {
                        return 0;
                    }
                    if (o1 != null && o1.getName() != null) {
                        return (o2 != null && o2.getName() != null) ? o1.getName().compareTo(o2.getName()) : 1;
                    }
                    return -1;
                }
            });
            columnSortHandler.setComparator(monthColumn, new Comparator<TouSpecialDayData>() {
                public int compare(TouSpecialDayData o1, TouSpecialDayData o2) {
                    if (o1 == o2) {
                        return 0;
                    }
                    if (o1 != null && o1.getSdMonth() != null) {
                        return (o2 != null && o2.getSdMonth() != null) ? o1.getSdMonth().compareTo(o2.getSdMonth()) : 1;
                    }
                    return -1;
                }
            });
            columnSortHandler.setComparator(dayProfileName, new Comparator<TouSpecialDayData>() {
                public int compare(TouSpecialDayData o1, TouSpecialDayData o2) {
                    if (o1 == o2) {
                        return 0;
                    }
                    if (o1 != null && o1.getTouDayProfileId() != null) {
                        return (o2 != null && o2.getTouDayProfileId() != null) ? o1.getTouDayProfileId().compareTo(o2.getTouDayProfileId()) : 1;
                    }
                    return -1;
                }
            });

            specialdayTable.addColumnSortHandler(columnSortHandler);
            specialdayTable.getColumnSortList().push(specialDayName);
            ColumnSortEvent.fire(specialdayTable, specialdayTable.getColumnSortList());
        } else {
            columnSortHandler.setList(dataProvider.getList());
            ColumnSortEvent.fire(specialdayTable, specialdayTable.getColumnSortList());
        }
        specialdayTable.setPageStart(0);
    }

    @SuppressWarnings("deprecation")
    private void setSpecialDay(TouSpecialDayData thespecialday) {
        clearErrors();
        clearFields();
        if (thespecialday != null) {
            // make a copy so that if validation fails we haven't changed the original
            this.specialDay = new TouSpecialDayData(thespecialday);
            this.specialDay.setDayProfile(thespecialday.getDayProfile());
            btnSave.setText(MessagesUtil.getInstance().getMessage("button.update"));
            formHeading.setText(MessagesUtil.getInstance().getMessage("calendar.specialday.update"));
            nameTextBox.setText(specialDay.getName());
            Date date = new Date();
            date.setYear(specialDay.getSdYear() - 1900);
            date.setMonth(specialDay.getSdMonth() -1);
            date.setDate(specialDay.getSdDay());
            sdDateBox.setValue(date);
            if (specialDay.getDayProfile() != null) {
                dayProfile.setText(specialDay.getDayProfile().getCode());
            }
            btnDelete.setVisible(true);
        } else {
            this.specialDay = new TouSpecialDayData();
            btnSave.setText(MessagesUtil.getInstance().getMessage("button.create"));
            formHeading.setText(MessagesUtil.getInstance().getMessage("calendar.specialday.add"));
            clearTableSelection();
            selectedSuggestion = null;
            btnDelete.setVisible(false);
            hasDirtyData.setDirtyData(false);
        }
    }

    public void clearFields() {
        nameTextBox.setText("");
        sdDateBox.setValue(null);
        dayProfile.setText("");
        selectedSuggestion = null;
    }

    public void clearErrors() {
        nameElement.clearErrorMsg();
        dateElement.clearErrorMsg();
        dayProfileElement.clearErrorMsg();
        errorMsg.setText("");
        errorMsg.setVisible(false);
    }

    @UiHandler("btnSave")
    public void save(ClickEvent e) {
        if (readOnly) {
            Dialogs.displayErrorMessage(MessagesUtil.getInstance().getMessage("calendar.readOnly", new String[] {parentContainer.getPricingStructureNames()}), MediaResourceUtil.getInstance().getErrorIcon(), btnSave.getAbsoluteLeft()+btnSave.getOffsetWidth(), btnSave.getAbsoluteTop()-btnSave.getOffsetHeight(), null);
            return;
        }
        SessionCheckCallback sessionCheckCallback = new SessionCheckCallback() {
            @Override
            public void callback(SessionCheckResolution resolution) {
                if (specialDay.getId() == null) {
                    addSpecialDay();
                } else {
                    updateSpecialDay();
                }
            }
        };
        clientFactory.handleSessionCheckCallback(sessionCheckCallback);
    }

    @UiHandler("btnCancel")
    public void cancel(ClickEvent e) {
        if(hasDirtyData.isDirtyData()) {
            Dialogs.confirmDiscardChanges(new ConfirmHandler() {
                @Override
                public void confirmed(boolean confirm) {
                    if (confirm) {
                        hasDirtyData.setDirtyData(false);
                        setSpecialDay(null);
                    }
                }});
        } else {
            setSpecialDay(null);
        }
    }

    @UiHandler("btnDelete")
    void delete(ClickEvent e) {
        deleteSpecialDay();
    }

    @SuppressWarnings("deprecation")
    private void update() {
        if (specialDay == null) {
            this.specialDay = new TouSpecialDayData();
        }
        specialDay.setName(nameTextBox.getText().isEmpty() ? null : nameTextBox.getText());
        Date date = sdDateBox.getValue();
        specialDay.setSdYear(date.getYear() + 1900);
        specialDay.setSdMonth(date.getMonth() + 1);
        specialDay.setSdDay(date.getDate());
        if (selectedSuggestion != null) {
            specialDay.setTouDayProfileId(selectedSuggestion.getId());
        }
        specialDay.setTouCalendarId(calendarData.getId());
    }

    private boolean isValid() {
        boolean valid = true;
        clearErrors();

        if (!ClientValidatorUtil.getInstance().validateField(specialDay, "name", nameElement)) {
            valid = false;
        }

        for (TouSpecialDayData tsdd : specialDaysList) {
            if (specialDay.getId() == null || !(specialDay.getId().equals(tsdd.getId()))) {
                if (tsdd.getSdYear().equals(specialDay.getSdYear()) && tsdd.getSdMonth().equals(specialDay.getSdMonth())
                        && tsdd.getSdDay().equals(specialDay.getSdDay())) {
                    errorMsg.setText(MessagesUtil.getInstance().getMessage("calendar.specialday.error.date.already.assigned.to")+" "+tsdd.getName());
                    errorMsg.setVisible(true);
                    valid = false;
                    break;
                }
            }
        }

        if (specialDay.getTouDayProfileId() == null) {
            errorMsg.setText(MessagesUtil.getInstance().getMessage("calendar.specialday.field.dayprofile.error"));
            errorMsg.setVisible(true);
            valid = false;
        }

        return valid;
    }

    public void addSpecialDay() {
        update();
        if (isValid()) {
            clientFactory.getCalendarRpc().updateSpecialDay(specialDay, new ClientCallback<TouSpecialDayData>(btnSave.getAbsoluteLeft(), btnSave.getAbsoluteTop()) {
                @Override
                public void onSuccess(TouSpecialDayData result) {
                    if (result != null) {
                        Dialogs.displayInformationMessage(MessagesUtil.getInstance().getSavedMessage(new String[] { result.getName() }), MediaResourceUtil.getInstance().getInformationIcon(), btnSave.getAbsoluteLeft(), btnSave.getAbsoluteTop(), MessagesUtil.getInstance().getMessage("button.close"));
                        specialDaysList.add(result);
                        dataProvider.setList(specialDaysList);
                        dataProvider.refresh();
                        setSpecialDay(null);
                    }
                }
            });
        }
    }

    public void updateSpecialDay() {
        update();
        if (isValid()) {
            clientFactory.getCalendarRpc().updateSpecialDay(specialDay, new ClientCallback<TouSpecialDayData>(btnSave.getAbsoluteLeft(), btnSave.getAbsoluteTop()) {
                @Override
                public void onSuccess(TouSpecialDayData result) {
                    if (result != null) {
                        int index = -1;
                        for (int i = 0; i < specialDaysList.size(); i++) {
                            if (specialDaysList.get(i).getId().equals(result.getId())) {
                                index = i;
                                break;
                            }
                        }
                        if (index > -1) {
                            setSpecialDay(null);
                            Dialogs.displayInformationMessage(MessagesUtil.getInstance().getSavedMessage(new String[] { result.getName() }), MediaResourceUtil.getInstance().getInformationIcon(), btnSave.getAbsoluteLeft(), btnSave.getAbsoluteTop(), MessagesUtil.getInstance().getMessage("button.close"));
                            specialDaysList.set(index, result);
                            dataProvider.setList(specialDaysList);
                            dataProvider.refresh();
                        }
                    }
                }
            });
        }
    }

    public void deleteSpecialDay() {
        //ToDo double check with popup
        final int aleft = btnDelete.getAbsoluteLeft();
        final int atop = btnDelete.getAbsoluteTop();
        Dialogs.confirm(
                ResourcesFactoryUtil.getInstance().getMessages().getMessage("question.delete"),
                ResourcesFactoryUtil.getInstance().getMessages().getMessage("option.positive"),
                ResourcesFactoryUtil.getInstance().getMessages().getMessage("option.negative"),
                ResourcesFactoryUtil.getInstance().getQuestionIcon(),
                new ConfirmHandler() {
                    @Override
                    public void confirmed(boolean confirm) {
                        if (confirm) {
                            SessionCheckCallback sessionCheckCallback = new SessionCheckCallback() {
                                @Override
                                public void callback(SessionCheckResolution resolution) {
                                    hasDirtyData.setDirtyData(false);
                                    clientFactory.getCalendarRpc().deleteSpecialDay(specialDay.getId(), specialDay.getTouCalendarId(), new ClientCallback<Boolean>(aleft, atop) {
                                        @Override
                                        public void onSuccess(Boolean result) {
                                            if (result) {
                                                setSpecialDay(null);
                                                Dialogs.displayInformationMessage(MessagesUtil.getInstance().getMessage("calendar.special.day.deleted"), MediaResourceUtil.getInstance().getInformationIcon(), aleft, atop, MessagesUtil.getInstance().getMessage("button.close"));
                                                refreshTable();
                                            }
                                        }
                                    });
                                }
                            };
                            clientFactory.handleSessionCheckCallback(sessionCheckCallback);
                        }
                    }
                }, aleft,atop
         );
    }

    public void setReadOnly(boolean readOnly) {
        this.readOnly = readOnly;
        formPanel.setVisible(!readOnly);
        buttons.setVisible(!readOnly);
    }

    protected void addFieldHandlers() {
        nameTextBox.addChangeHandler(new FormDataChangeHandler(hasDirtyData));
        sdDateBox.addValueChangeHandler(new FormDataValueChangeHandler<Date>(hasDirtyData));
        dayProfile.addValueChangeHandler(new FormDataValueChangeHandler<String>(hasDirtyData));
    }

}
