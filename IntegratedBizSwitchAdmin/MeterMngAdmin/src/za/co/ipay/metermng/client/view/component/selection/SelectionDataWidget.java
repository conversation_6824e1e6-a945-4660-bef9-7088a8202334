package za.co.ipay.metermng.client.view.component.selection;

import java.util.ArrayList;
import java.util.Collections;
import java.util.Comparator;
import java.util.Iterator;
import java.util.List;
import java.util.logging.Logger;

import com.google.gwt.core.client.GWT;
import com.google.gwt.event.dom.client.ChangeEvent;
import com.google.gwt.event.dom.client.ChangeHandler;
import com.google.gwt.event.dom.client.ClickEvent;
import com.google.gwt.event.dom.client.ClickHandler;
import com.google.gwt.place.shared.Place;
import com.google.gwt.uibinder.client.UiBinder;
import com.google.gwt.uibinder.client.UiField;
import com.google.gwt.user.client.ui.CellPanel;
import com.google.gwt.user.client.ui.HorizontalPanel;
import com.google.gwt.user.client.ui.Image;
import com.google.gwt.user.client.ui.Label;
import com.google.gwt.user.client.ui.PushButton;
import com.google.gwt.user.client.ui.VerticalPanel;
import com.google.gwt.user.client.ui.Widget;

import za.co.ipay.gwt.common.client.Dialogs;
import za.co.ipay.gwt.common.client.form.FormElement;
import za.co.ipay.gwt.common.client.form.HasDirtyData;
import za.co.ipay.gwt.common.client.handler.FormDataChangeHandler;
import za.co.ipay.gwt.common.client.resource.Messages.MessagesUtil;
import za.co.ipay.gwt.common.client.resource.StyleNames;
import za.co.ipay.gwt.common.client.widgets.Message;
import za.co.ipay.gwt.common.client.workspace.SessionCheckCallback;
import za.co.ipay.gwt.common.shared.LookupListItem;
import za.co.ipay.metermng.client.event.UpdateUsagePointGroupsListEvent;
import za.co.ipay.metermng.client.event.UpdateUsagePointGroupsListHandler;
import za.co.ipay.metermng.client.factory.ClientFactory;
import za.co.ipay.metermng.client.history.MetadataUploadPlace;
import za.co.ipay.metermng.client.resource.image.MediaResource.MediaResourceUtil;
import za.co.ipay.metermng.client.rpc.SelectionDataRpcAsync;
import za.co.ipay.metermng.client.rpc.callback.ClientCallback;
import za.co.ipay.metermng.client.view.component.BaseComponent;
import za.co.ipay.metermng.shared.MeterMngStatics;
import za.co.ipay.metermng.shared.dto.SelectionDataItem;
import za.co.ipay.metermng.shared.dto.SelectionDataItem.SelectionDataType;

public class SelectionDataWidget extends BaseComponent {

    private static final boolean AUTOMATIC_SELECTION = true;
    private Place place;

    private SelectionDataRpcAsync service;
    private String accessGroupParentGroupId;
    private SelectionDataItem item;
    private Long groupTypeId;
    private boolean horizontalLayout;
    @UiField VerticalPanel wrapperPanel;
    private VerticalPanel captionPanel;
    private CellPanel mainPanel;
    private Label captionLabel;
    private List<SelectionFormRowPanel> formRowPanels;
    private static Logger logger = Logger.getLogger(SelectionDataWidget.class.getName());
    private static SelectionDataWidgetUiBinder uiBinder = GWT.create(SelectionDataWidgetUiBinder.class);
    private SelectionDataListBox latestListBox;
    private String latestLabel;
    private Long upGenGroupLnkId;
    private ArrayList<Long> selectedGroupPath = null;
    private Long lastSelectedGroupId = null;
    private Long selectedGroupType;
    private Message errorMessage;
    private boolean mustSelectLastlevel = true;
    private String disabledId;
    private boolean foundMatchingGroup;
    //Flag to indicate whether an automatic selection should be made if the item only has 1 child.
    private boolean automaticSelection;
    private boolean reselectIfDataChanges = false;

    private ArrayList<String> lowestLevelIds; // contains SelectionDataItem.id as returned from toIdKey in that class

    private String sdwContext;
    private HasDirtyData hasDirtyData;

    interface SelectionDataWidgetUiBinder extends UiBinder<Widget, SelectionDataWidget> {
    }

    public SelectionDataWidget(ClientFactory clientFactory, SelectionDataRpcAsync service, SelectionDataItem item, boolean horizontalLayout, boolean mustSelectLastlevel, String sdwContext, HasDirtyData hasDirtyData) {
        this(clientFactory, service, item, horizontalLayout, mustSelectLastlevel, null, AUTOMATIC_SELECTION, sdwContext, hasDirtyData, null);
    }

    public SelectionDataWidget(ClientFactory clientFactory,
                               SelectionDataRpcAsync service,
                               SelectionDataItem item,
                               boolean horizontalLayout,
                               boolean mustSelectLastlevel,
                               String disabledId,
                               boolean automaticSelection, HasDirtyData hasDirtyData) {
        this(clientFactory, service, item, horizontalLayout, mustSelectLastlevel, disabledId, automaticSelection, "", hasDirtyData, null);
    }

    public SelectionDataWidget(ClientFactory clientFactory,
            SelectionDataRpcAsync service,
            SelectionDataItem item,
            boolean horizontalLayout,
            boolean mustSelectLastlevel,
            String disabledId,
            boolean automaticSelection, HasDirtyData hasDirtyData, String accessGroupParentGroupId) {
        this(clientFactory, service, item, horizontalLayout, mustSelectLastlevel, disabledId, automaticSelection, "", hasDirtyData, accessGroupParentGroupId);
    }

    public SelectionDataWidget(ClientFactory clientFactory,
                               SelectionDataRpcAsync service,
                               SelectionDataItem item,
                               boolean horizontalLayout,
                               boolean mustSelectLastlevel,
                               String disabledId,
                               boolean automaticSelection,
                               String sdwContext,
                               HasDirtyData hasDirtyData,
                               String accessGroupParentGroupId) {

        this.clientFactory = clientFactory;
        this.service = service;
        this.item = item;
        this.groupTypeId = item.getActualId();
        this.horizontalLayout = horizontalLayout;
        this.errorMessage = new Message();
        this.errorMessage.ensureDebugId("selectionDataError");
        this.formRowPanels = new ArrayList<SelectionFormRowPanel>();
        this.mustSelectLastlevel = mustSelectLastlevel;
        this.disabledId = disabledId;
        this.foundMatchingGroup = false;
        this.automaticSelection = automaticSelection;
        this.lowestLevelIds = new ArrayList<String>();
        this.sdwContext = sdwContext;
        this.hasDirtyData = hasDirtyData;
        this.accessGroupParentGroupId = accessGroupParentGroupId;

        initWidget(uiBinder.createAndBindUi(this));
        initUi();
        clear();
        addUpdatedHandler();
    }

    public void setPlace(Place place) {
        this.place = place;
    }

    private void addUpdatedHandler() {
        clientFactory.getEventBus().addHandler(UpdateUsagePointGroupsListEvent.TYPE, new UpdateUsagePointGroupsListHandler() {

            @Override
            public void updateUsagePointGroupList(final UpdateUsagePointGroupsListEvent event) {
                if (event.getGroupTypeId() == null || event.getGroupTypeId().equals(groupTypeId)) {
                    SessionCheckCallback sessionCheckCallback = new SessionCheckCallback() {
                        @Override
                        public void callback(SessionCheckResolution resolution) {
                            clientFactory.getGroupRpc().getGroupType(groupTypeId, new ClientCallback<ArrayList<SelectionDataItem>>() {

                                @Override
                                public void onSuccess(ArrayList<SelectionDataItem> result) {
                                    for (int i = 0; i < result.size(); i++) {
                                        item = result.get(i);
                                        if (groupTypeId.equals(item.getActualId())) {
                                            break;
                                        }
                                    }
                                    clear();
                                    if (event.getSelected() != null && reselectIfDataChanges) {
                                        setSelectedGroup(event.getSelected());
                                    } else if (lastSelectedGroupId != null) {
                                        setSelectedGroup(lastSelectedGroupId);
                                    }

                                }
                            });
                        }
                    };
                    clientFactory.handleSessionCheckCallback(sessionCheckCallback);
                }
            }
        });

    }

    private void initUi() {
        captionPanel = new VerticalPanel();
        captionLabel = new Label("");
        captionLabel.addStyleName("pseudoCaptionLabel");
        captionPanel.add(captionLabel);
        captionPanel.addStyleName("pseudoCaptionPanel");
        wrapperPanel.add(captionPanel);
        if (horizontalLayout) {
            mainPanel = new HorizontalPanel();
        } else {
            mainPanel = new VerticalPanel();
        }
        captionPanel.add(mainPanel);
        wrapperPanel.add(errorMessage);
        errorMessage.setVisible(false);
    }

    public void clear() {
        mainPanel.clear();
        captionLabel.setText("");
        formRowPanels.clear();
        selectedGroupPath = null;
        if (item != null) {
            captionLabel.setText(item.getName() + ":");
            if (item.getChildren().size() > 0) {
                logger.info("Creating init selection box for item:" + item.getName());
                createSelectionBox(item, false, true);
            }
        }
    }

    private void createSelectionBox(SelectionDataItem item, boolean required) {
        createSelectionBox(item, required, false);
    }

    private void createSelectionBox(SelectionDataItem item, boolean required, boolean initialSetup) {
        if (item == null) {
            return;
        }
        String name = item.getName();
        ArrayList<SelectionDataItem> children = item.getChildren();
        int childCount = (children != null ? children.size() : 0);
        if (childCount== 0) {
            logger.info("Loading children for next selection box: " + name + " " + item.getType() + " " + childCount);
            loadChildren(item);
        } else {
            logger.info("Got children for next selection box: " + name + " " + item.getType() + " " + childCount);
            String rowPanelId = item.getType().name() + item.getId();
            replaceExistingWidget(rowPanelId);
            SelectionFormRowPanel row = new SelectionFormRowPanel(rowPanelId);
            FormElement element = new FormElement();
            element.setRequired(item.isRequired() || required || mustSelectLastlevel);
            element.setHelpMsg(null);
            SelectionDataListBox listBox = new SelectionDataListBox();
            if (hasDirtyData != null) {
                // sometimes this widget is not part of a form that has a dirty status and gets saved, but for example is used to
                // select session group which is not quite the same as a form that can be cleared and cancelled and warn for tab closing
                listBox.addChangeHandler(new FormDataChangeHandler(hasDirtyData));
            }
            listBox.ensureDebugId("selectionDataBox" + captionLabel.getText().replaceAll(" ", "").replaceAll(":", "") + formRowPanels.size());

            listBox.setParentRowPanel(row);
            listBox.addItem("", SelectionDataItem.EMPTY_ITEM_ID_KEY);
            displayChildren(item, listBox, element);
            element.add(listBox);
            row.add(element);
            if (MeterMngStatics.SELECTION_DATA_WIDGET_CONTEXT_USAGE_POINT_GROUP.equals(sdwContext)) {
                FormElement el = new FormElement();
                el.add(addUsagePointGroupDetailsButton(item, listBox));
                row.add(el);
            }
            mainPanel.add(row);
            formRowPanels.add(row);
            latestListBox = listBox;
            setSelectedGroup();
            setDefaultSelection(initialSetup);
        }
    }

    private void replaceExistingWidget(String id) {
        for (int j = 0, count = mainPanel.getWidgetCount(); j < count; j++) {
            Widget widget = mainPanel.getWidget(j);
            if (widget instanceof SelectionFormRowPanel) {
                SelectionFormRowPanel panel = (SelectionFormRowPanel) widget;
                if (panel.getId().equals(id)) {
                    mainPanel.remove(widget);
                    formRowPanels.remove(widget);
                    return;
                }
            }
        }
    }

    private void loadChildren(final SelectionDataItem item) {
        service.getChildrenSelectionData(item, new ClientCallback<ArrayList<SelectionDataItem>>() {
            @Override
            public void onSuccess(ArrayList<SelectionDataItem> result) {
                if (result.size() > 0) {
                    if (accessGroupParentGroupId != null) {
                        // If user has logged in to a group, only use that group tree.
                        for (SelectionDataItem item : result) {
                            if (accessGroupParentGroupId.equals(item.getId())) {
                                result = new ArrayList<>();
                                result.add(item);
                                break;
                            }
                        }
                    }

                    Collections.sort(result, new Comparator<SelectionDataItem>() {
                        @Override
                        public int compare(SelectionDataItem o1, SelectionDataItem o2) {
                            return o1.getName().compareTo(o2.getName());
                        }
                    });
                    item.setChildren(result);
                    createSelectionBox(item, false);
                }
            }
        });
    }

    @SuppressWarnings("deprecation")
    private void displayChildren(final SelectionDataItem item, final SelectionDataListBox listBox, FormElement element) {
        boolean createAnotherLevel = true;
        SelectionDataItem child = null;
        lowestLevelIds.clear();
        logger.fine("Displaying children: " + item.getChildren().size());

        for (int i = 0; i < item.getChildren().size(); i++) {
            child = item.getChildren().get(i);
            listBox.addItem(child.getName(), child.getId());
            if (listBox.getSelectionDataType() == null) {
                listBox.setSelectionDataType(child.getType());
            }
            if (disabledId != null && disabledId.equals(child.getId())) {
                listBox.setEnabled(false);
                listBox.addStyleName("inactive");
                foundMatchingGroup = true;
                logger.fine("Disabled box for matching id: " + disabledId);
            }
            if (child.getId().equals(SelectionDataItem.EMPTY_ITEM_ID_KEY)) {
                listBox.removeItem(0);
                listBox.setItemSelected(i, true);
                listBox.setEnabled(false);
                element.setLabelText(child.getLabel());
                logger.fine("Removed place holder child: " + child.getName() + ", " + child.getLabel());
                break;
            }

            if (child.isLastLevel()) {
                lowestLevelIds.add(child.getId());
            }

            if (i == 0) {
                if (SelectionDataType.GROUP_DATA.equals(child.getType())) {
                    element.setLabelText(child.getLabel());
                } else {
                    element.setLabelText(item.getLabel());
                }

                if (child.isLastLevel()) {
                    logger.fine("Child is lastLevel: " + child.getName());
                    createAnotherLevel = false;
                    listBox.addStyleName(StyleNames.LAST_LEVEL_STYLE_NAME);
                    element.addLabelStyleName(StyleNames.LAST_LEVEL_STYLE_NAME);
                } else {
                    listBox.addStyleName(StyleNames.LEVEL_STYLE_NAME);
                    element.addLabelStyleName(StyleNames.LEVEL_STYLE_NAME);
                }
            }
        }
        if (disabledId != null && !foundMatchingGroup) {
            listBox.setEnabled(false);
            listBox.addStyleName("inactive");
        }

        if (createAnotherLevel) {
            listBox.addChangeHandler(new ChangeHandler() {
                @Override
                public void onChange(ChangeEvent event) {
                    if (selectedGroupPath == null) {
                        selectedGroupPath = new ArrayList<Long>();
                    }
                    selectedGroupPath.add(item.getActualId());
                    lastSelectedGroupId = SelectionDataItem.fromIdKeyToActualId(listBox.getValue(listBox.getSelectedIndex()));
                    clearError();
                    createNextListBox(listBox);
                }
            });
        } else {
            listBox.addChangeHandler(new ChangeHandler() {
                @Override
                public void onChange(ChangeEvent event) {
                    clearError();
                    latestListBox.setSelectedIndex(listBox.getSelectedIndex());
                    lastSelectedGroupId = SelectionDataItem.fromIdKeyToActualId(listBox.getValue(listBox.getSelectedIndex()));
                }
            });
        }

        if (listBox.getSelectedIndex() >= 0) {
            //if lowest level, don't create a next list box
            if (createAnotherLevel) {
                createNextListBox(listBox);
            }
        }
    }

    private PushButton addUsagePointGroupDetailsButton(final SelectionDataItem item, final SelectionDataListBox contextListBox) {
        PushButton usagePointGroupDetailsBtn = new PushButton(new Image(MediaResourceUtil.getInstance().getInfoImage()));
        usagePointGroupDetailsBtn.addClickHandler(new ClickHandler() {
            @Override
            public void onClick(ClickEvent event) {
                int selectedIndex = contextListBox.getSelectedIndex();
                LookupListItem listItem = contextListBox.getItem(selectedIndex);
                if (listItem != null) {
                    SelectionDataItem dataItem = findSelectedItem(item, listItem.getValue());
                    if (dataItem == null) {
                        Dialogs.displayInformationMessage(MessagesUtil.getInstance().getMessage("error.no.selection"),
                                MediaResourceUtil.getInstance().getInformationIcon());
                    } else if (place instanceof MetadataUploadPlace) {
                        GisMetadataUpdateBox box = new GisMetadataUpdateBox(clientFactory, dataItem.getActualId());
                        box.show();
                    } else {
                        new GroupDetailsDialogueBox(clientFactory, dataItem.getActualId());
                    }
                }
            }
        });
        return usagePointGroupDetailsBtn;
    }

    public void createNextListBox(SelectionDataListBox parentListBox) {
        int selectedIndex = parentListBox.getSelectedIndex();
        // Remove any child list boxes before adding a new child list box for the current selection
        removeListBoxes(parentListBox);
        latestListBox = parentListBox;
        if (selectedIndex > 0) {
            SelectionDataItem selectedItem = findSelectedItem(item, parentListBox.getValue(selectedIndex));
			if (selectedItem != null) {
				latestLabel = selectedItem.getLabel();
			}
            createSelectionBox(selectedItem, parentListBox.getSelectionDataType().equals(SelectionDataType.GROUP_TYPE));
        } else {
            for (int p = 0; p < parentListBox.getItemCount(); p++) {
                if (!"".equals(parentListBox.getItem(p).getText())) {
					SelectionDataItem selectedItem = findSelectedItem(item, parentListBox.getValue(p));
					if (selectedItem != null) {
						latestLabel = selectedItem.getLabel();
					}
                    break;
                }
            }
        }
    }

    private void removeListBoxes(SelectionDataListBox listBox) {
        int index = formRowPanels.indexOf(listBox.getParentRowPanel());
        if (index > -1) {
            for (int i = formRowPanels.size() - 1; i > index; i--) {
                SelectionFormRowPanel rowPanel = formRowPanels.remove(i);
                mainPanel.remove(rowPanel);
            }
        }
    }

    private SelectionDataItem findSelectedItem(SelectionDataItem item, String id) {
        if (item.getId().equals(id)) {
            return item;
        }
        List<SelectionDataItem> children = item.getChildren();
        SelectionDataItem child = null;
        for (int i = 0; child == null && i < children.size(); i++) {
            child = findSelectedItem(children.get(i), id);
        }
        return child;
    }

    public synchronized void setSelectedGroup(ArrayList<Long> selectedGroupPath, Long groupType) {
        this.selectedGroupPath = selectedGroupPath;
        this.selectedGroupType = groupType;
        setSelectedGroup();
    }

    private void setSelectedGroup() {
        if (selectedGroupPath != null) {
            latestListBox.clearSelections();
            outer:
            for (int i = 0, count = latestListBox.getItemCount(); i < count; i++) {
               String selectionItem = SelectionDataItem.fromIdKeyToActualIdString(latestListBox.getValue(i));
                for (Long pathValue : selectedGroupPath) {
                    if ((selectedGroupType != null && selectionItem.equals(selectedGroupType.toString()))
                            || selectionItem.equals(pathValue.toString())) {
                        latestListBox.setSelectedIndex(i);
                        lastSelectedGroupId = SelectionDataItem.fromIdKeyToActualId(latestListBox.getValue(latestListBox.getSelectedIndex()));
                        if (!lowestLevelIds.contains(latestListBox.getValue(i))) {
                            createNextListBox(latestListBox);
                            break outer;
                        }
                    }
                }
            }
        }
    }

    public boolean isLowestLevel() {
        return lowestLevelIds.contains(latestListBox.getValue(latestListBox.getSelectedIndex()));
    }

    public SelectionDataListBox getLatestListBox() {
        return latestListBox;
    }

    private void setDefaultSelection(boolean initialSetup) {
        if (!initialSetup) {
            if (latestListBox != null && automaticSelection) {
                //2 options: 1 blank and 1 child option
                if (latestListBox.getItemCount() == 2) {
                    int index = latestListBox.getSelectedIndex();
                    if (index != 1) {
                        latestListBox.setSelectedIndex(1);
                        if (latestListBox.getValue(1).equals(SelectionDataItem.NO_VALUES_ACTUAL_ID.toString())) {
                            latestListBox.setEnabled(false);
                            latestListBox.addStyleName(StyleNames.INACTIVE_STYLE);
                        } else {
                            try {
                                String id = latestListBox.getValue(1);
                                if (!lowestLevelIds.contains(id)) {
                                    latestListBox.setEnabled(true);
                                    createNextListBox(latestListBox);
                                }
                            } catch (NumberFormatException e) {
                                logger.log(java.util.logging.Level.SEVERE, "Invalid lowest level id: " + latestListBox.getValue(1));
                            }
                        }
                    }
                }
            }
        }
    }

    /**
     * Returns actual database id of the group as a string
     */
    public Long getSelectedGroup() {
        if (latestListBox == null) {
            return null;
        }
        String value = null;
        if (mustSelectLastlevel) {
            value = latestListBox.getValue(latestListBox.getSelectedIndex());
        } else {
            value = getAnySelectedGroup(latestListBox, true);
        }
        if (value == null)
            return null;
        return SelectionDataItem.fromIdKeyToActualId(value);
    }

    public void setSelectedGroup(ArrayList<Long> selectedGroupPath) {
        this.selectedGroupPath = selectedGroupPath;
        setSelectedGroup();
    }

    public void setSelectedGroup(Long selectedGenGroupId) {
        clientFactory.getGroupRpc().getPath(selectedGenGroupId, new ClientCallback<ArrayList<Long>>() {

            @Override
            public void onSuccess(ArrayList<Long> result) {
                selectedGroupPath = result;
                setSelectedGroup();
            }
        });
    }

    public String getSelectedGroupName() {
        if (latestListBox == null) {
            return null;
        }
        if (mustSelectLastlevel) {
            return latestListBox.getItemText(latestListBox.getSelectedIndex());
        } else {
            return getAnySelectedGroup(latestListBox, false);
        }
    }

    public SelectionDataType getSelectedGroupDataType() {
        if (latestListBox == null) {
            return null;
        }

        if (mustSelectLastlevel) {
            return latestListBox.getSelectionDataType();
        } else {
            SelectionDataListBox selectedListBox = getAnySelectedListBox(latestListBox, true);
            if (selectedListBox != null) {
                return selectedListBox.getSelectionDataType();
            } else {
                return null;
            }
        }
    }

    public String getLastLevelLabel() {
        return latestLabel;
    }

    public List<Long> getSelectedGroupPath() {
        return selectedGroupPath;
    }

    /*
     * returns SelectionDataItem.id as returned from toIdKey in that class when value is true, otherwise returns group name
     */
    private String getAnySelectedGroup(SelectionDataListBox current, boolean value) {
        SelectionDataListBox box = getAnySelectedListBox(current, value);
        if (box != null) {
            String group = box.getValue(box.getSelectedIndex());
            if (value) {
                return group; //found a valid group
            } else {
                return box.getItemText(box.getSelectedIndex());
            }
        }
        return null;
    }

    private SelectionDataListBox getAnySelectedListBox(SelectionDataListBox current, boolean value) {
        //Iterator up through the selection boxes until we get one with a valid selection - not very pretty
        for (int i = formRowPanels.size() - 1; i >= 0; i--) {
            SelectionFormRowPanel rowPanel = formRowPanels.get(i);
            Iterator<Widget> widgets = rowPanel.iterator();
            while (widgets.hasNext()) {
                Widget w = widgets.next();
                if (w instanceof FormElement) {
                    FormElement fe = (FormElement) w;
                    Iterator<Widget> iterator = fe.iterator();
                    while (iterator.hasNext()) {
                        Widget iw = iterator.next();
                        if (iw instanceof SelectionDataListBox) {
                            SelectionDataListBox box = (SelectionDataListBox) iw;
                            String group = box.getValue(box.getSelectedIndex());
                            if (group != null && !group.equals("") && !group.equals(SelectionDataItem.EMPTY_ITEM_ID_KEY)) {
                                return box;
                            }
                        }
                    }
                }
            }
        }
        logger.info("No selected value");
        return null;
    }

    public Long getUpGenGroupLnkId() {
        return upGenGroupLnkId;
    }

    public void setUpGenGroupLnkId(Long upGenGroupLnkId) {
        this.upGenGroupLnkId = upGenGroupLnkId;
    }

    public ArrayList<Long> getGroupTypeIds() {
        if (item == null) {
            return null;
        }
        ArrayList<Long> groupTypes = new ArrayList<Long>();
        groupTypes.add(item.getActualId());
        if (item.getChildren() != null && !item.getChildren().isEmpty()) {
            for (int i = 0; i < item.getChildren().size(); i++) {
                groupTypes.add(item.getChildren().get(i).getActualId());
            }
        }

        return groupTypes;
    }

    public Long getGroupTypeId() {
        if (item == null) {
            return null;
        }
        return item.getActualId();
    }

    public boolean isRequired() {
        return (item != null && item.isRequired());
    }

    public Integer getLayoutOrder() {
        return item.getLayoutOrder();
    }

    public void setError(String error) {
        errorMessage.setText(error);
        errorMessage.setType(Message.MESSAGE_TYPE_ERROR);
        errorMessage.setVisible(true);
    }

    public void clearError() {
        errorMessage.setText("");
        errorMessage.setVisible(false);
    }

    // If the data changes behind the scenes the reselect according to the UpdateUsagePointGroupsListEvent
    public void setReselectIfDataChanges(boolean reselectIfDataChanges) {
        this.reselectIfDataChanges = reselectIfDataChanges;
    }

    public String getAccessGroupParentGroupId() {
        return accessGroupParentGroupId;
    }

    public String setAccessGroupParentGroupId(String accessGroupParentGroupId) {
        return this.accessGroupParentGroupId = accessGroupParentGroupId;
    }
}
