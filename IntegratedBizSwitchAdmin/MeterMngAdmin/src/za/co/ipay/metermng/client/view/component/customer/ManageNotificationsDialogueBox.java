package za.co.ipay.metermng.client.view.component.customer;

import java.util.Map;
import com.google.gwt.core.client.GWT;
import com.google.gwt.event.dom.client.ClickEvent;
import com.google.gwt.uibinder.client.UiBinder;
import com.google.gwt.uibinder.client.UiFactory;
import com.google.gwt.uibinder.client.UiField;
import com.google.gwt.uibinder.client.UiHandler;
import com.google.gwt.user.client.ui.Button;
import com.google.gwt.user.client.ui.DialogBox;
import com.google.gwt.user.client.ui.ListBox;
import com.google.gwt.user.client.ui.Widget;

import za.co.ipay.gwt.common.client.Dialogs;
import za.co.ipay.gwt.common.client.form.HasDirtyData;
import za.co.ipay.gwt.common.client.form.LocalOnlyHasDirtyData;
import za.co.ipay.gwt.common.client.handler.ConfirmHandler;
import za.co.ipay.gwt.common.client.handler.FormDataChangeHandler;
import za.co.ipay.gwt.common.client.resource.Messages;
import za.co.ipay.gwt.common.client.resource.Messages.MessagesUtil;
import za.co.ipay.gwt.common.client.widgets.Message;
import za.co.ipay.gwt.common.client.workspace.SessionCheckCallback;
import za.co.ipay.metermng.client.event.UsagePointUpdatedEvent;
import za.co.ipay.metermng.client.factory.ClientFactory;
import za.co.ipay.metermng.client.i18n.UiMessages;
import za.co.ipay.metermng.client.i18n.UiMessagesUtil;
import za.co.ipay.metermng.client.resource.image.MediaResource.MediaResourceUtil;
import za.co.ipay.metermng.client.rpc.callback.ClientCallback;
import za.co.ipay.metermng.client.view.workspace.UsagePointWorkspaceView;
import za.co.ipay.metermng.mybatis.generated.model.AppSetting;
import za.co.ipay.metermng.mybatis.generated.model.CustAccNotify;
import za.co.ipay.metermng.shared.appsettings.AppSettings;
import za.co.ipay.metermng.shared.dto.UpGenGroupLinkData;
import za.co.ipay.metermng.shared.dto.UsagePointData;

public class ManageNotificationsDialogueBox extends DialogBox {
    interface ManageNotificationsDialogueBoxUiBinder extends UiBinder<Widget, ManageNotificationsDialogueBox> {
    }
    private static ManageNotificationsDialogueBoxUiBinder uiBinder = GWT.create(ManageNotificationsDialogueBoxUiBinder.class);

    @UiField ListBox auxAdjustmentBox;
    @UiField ListBox newAuxBox;

    @UiField Message feedBack;
    @UiField Button btnSave;
    @UiField Button btnCancel;

    private ClientFactory clientFactory;
    private HasDirtyData hasDirtyDataLocal = new LocalOnlyHasDirtyData();

    private static final String optionYes = "yes";
    private static final String optionNo = "no";
    private static final String optionInherit = "inherit";
    private CustAccNotify notifyAccount;
    private UsagePointData usagePointData;
    UsagePointWorkspaceView usagePointWorkspaceView;

    public ManageNotificationsDialogueBox(ClientFactory clientFactory, UsagePointWorkspaceView usagePointWorkspaceView) {
        this.clientFactory = clientFactory;
        this.usagePointWorkspaceView = usagePointWorkspaceView;
        setWidget(uiBinder.createAndBindUi(this));
        this.ensureDebugId("ManageNotificationsDialogueBox");
    }

    public void setNotificationData() {
        notifyAccount = new CustAccNotify();
        if(usagePointData.getCustomerAgreementData() != null
                && usagePointData.getCustomerAgreementData().getCustomerAccount() != null){
            notifyAccount.setNotifyNewAux(usagePointData.getCustomerAgreementData().getCustomerAccount().getNotifyNewAux());
            notifyAccount.setNotifyAuxAdjustment(usagePointData.getCustomerAgreementData().getCustomerAccount().getNotifyAuxAdjustment());
        }
        getAppSettingsDataForNewAux();
    }

    public void save() {
        final CustAccNotify notificationsToSave = mapFormToData();
        if(notificationsToSave == null)
            return;
        if(!haveValuesChanged(notificationsToSave)) {
            clearSelections();
            displaySaveMessage();
            return;
        }
        if ((notificationsToSave.getNotifyNewAux() == null && notificationsToSave.getNotifyAuxAdjustment() != null)
                || (notificationsToSave.getNotifyAuxAdjustment()  == null && notificationsToSave.getNotifyNewAux() != null)) {
            feedBack.setText(MessagesUtil.getInstance().getMessage("error.notify.selection.null"));
            feedBack.setType(Message.MESSAGE_TYPE_ERROR);
            feedBack.setVisible(true);
            return;
        }
        saveNotifications(notificationsToSave);
    }

    @UiFactory
    public UiMessages getUiMessages() {
        return UiMessagesUtil.getInstance();
    }

    private void saveNotifications(CustAccNotify custAccNotify) {
        usagePointData.getCustomerAgreementData().getCustomerAccount().setNotifyNewAux(custAccNotify.getNotifyNewAux());
        usagePointData.getCustomerAgreementData().getCustomerAccount().setNotifyAuxAdjustment(custAccNotify.getNotifyAuxAdjustment());

        clientFactory.getCustomerRpc().updateNotifyPreferences(
                usagePointData.getCustomerAgreementData().getCustomerAccount(),
                new ClientCallback<Integer>() {
                    @Override
                    public void onSuccess(Integer result) {
                        if(result != 1) {
                            Dialogs.displayErrorMessage(MessagesUtil.getInstance().getMessage("error.general"),
                                    MediaResourceUtil.getInstance().getErrorIcon(),
                                    btnSave.getAbsoluteLeft(), btnSave.getAbsoluteTop() + btnSave.getOffsetHeight(),
                                    null);
                            return;
                        }
                        if(usagePointWorkspaceView.checkDuplicateDataOnOtherTabs(usagePointData)){
                            usagePointData = usagePointWorkspaceView.getUsagePointData();
                            hasDirtyDataLocal.setDirtyData(false);
                            return;
                        }
                        displaySaveMessage();
                        saveUsagePointData();
                    }
                });
    }

    private void displaySaveMessage() {
        notifyAccount.setNotifyNewAux(
                usagePointData.getCustomerAgreementData().getCustomerAccount().getNotifyNewAux());
        notifyAccount.setNotifyAuxAdjustment(
                usagePointData.getCustomerAgreementData().getCustomerAccount().getNotifyAuxAdjustment());
        Dialogs.displayInformationMessage(
                MessagesUtil.getInstance().getSavedMessage(new String[] { MessagesUtil.getInstance().getMessage("customer.notification.types") }),
                MediaResourceUtil.getInstance().getInformationIcon(),
                auxAdjustmentBox.getAbsoluteLeft(), auxAdjustmentBox.getAbsoluteTop(),
                MessagesUtil.getInstance().getMessage("button.close"));
        btnSave.setEnabled(true);
        clearSelections();
        this.hide();
    }

    private CustAccNotify mapFormToData() {
        String newAuxValue = newAuxBox.getSelectedValue();
        String auxAdjustValue = auxAdjustmentBox.getSelectedValue();
        CustAccNotify notificationsToSave = new CustAccNotify();
        notificationsToSave.setId(null);

        if(newAuxValue.equals(optionYes)) {
            notificationsToSave.setNotifyNewAux(true);
        }else if(newAuxValue.equals(optionNo)) {
            notificationsToSave.setNotifyNewAux(false);
        }

        if(auxAdjustValue.equals(optionYes)) {
            notificationsToSave.setNotifyAuxAdjustment(true);
        }else if(auxAdjustValue.equals(optionNo)) {
            notificationsToSave.setNotifyAuxAdjustment(false);
        }
        return notificationsToSave;
    }

    private void clearSelections(){
        if(notifyAccount != null) {
            newAuxBox.setSelectedIndex(convertBooleanToIndex(notifyAccount.getNotifyNewAux()));
            auxAdjustmentBox.setSelectedIndex(convertBooleanToIndex(notifyAccount.getNotifyAuxAdjustment()));
            feedBack.setText("");
            feedBack.setVisible(false);
            btnSave.setEnabled(true);
        }
    }

    @UiHandler("btnSave")
    public void save(ClickEvent e) {
        SessionCheckCallback sessionCheckCallback = new SessionCheckCallback() {
            @Override
            public void callback(SessionCheckResolution resolution) {
                save();
            }
        };
        clientFactory.handleSessionCheckCallback(sessionCheckCallback);
    }

    private void setupListBox(ListBox box, Boolean inheritedOption, boolean newAux) {
        box.clear();
        Messages messages = MessagesUtil.getInstance();
        box.addItem(messages.getMessage("option.positive"), optionYes);
        box.addItem(messages.getMessage("option.negative"), optionNo);
        if (inheritedOption == true) {
            box.addItem(messages.getMessage("notify.selection.inherit",
                    new String[]{messages.getMessage("option.positive")}), optionInherit);
        }else if(inheritedOption == false){
            box.addItem(messages.getMessage("notify.selection.inherit",
                    new String[]{messages.getMessage("option.negative")}), optionInherit);
        }
        box.addChangeHandler(new FormDataChangeHandler(hasDirtyDataLocal));
        if(newAux)
            box.setSelectedIndex(convertBooleanToIndex(notifyAccount.getNotifyNewAux()));
        else
            box.setSelectedIndex(convertBooleanToIndex(notifyAccount.getNotifyAuxAdjustment()));
    }

    private int convertBooleanToIndex(Boolean notify) {
        if(notify == null)
            return 2;
        else if(notify == false)
            return 1;
        else return 0;
    }

    @UiHandler("btnCancel")
    void handleCancelBtn(ClickEvent event) {
        if (hasDirtyDataLocal.isDirtyData()) {
            Dialogs.confirmDiscardChanges(new ConfirmHandler() {
                @Override
                public void confirmed(boolean confirm) {
                    if (confirm) {
                        clearSelections();
                        hasDirtyDataLocal.setDirtyData(false);
                        hide();
                    }
                }
            });
        } else {
            this.hide();
        }
    }

    public void setUsagePointData(UsagePointData usagePointData) {
        this.usagePointData = usagePointData;
    }

    private void getInheritanceData() {
        if(usagePointData.getUpgengroups() != null) {
            Map<Long, UpGenGroupLinkData> genGroups = usagePointData.getUpgengroups();
            for (UpGenGroupLinkData upGenGroupLinkData : genGroups.values()) {
                Long custAccNotifyId = upGenGroupLinkData.getCustAccNotifyId();
                if (custAccNotifyId != null) {
                    clientFactory.getGroupRpc().getGenGroupNotifications(custAccNotifyId, new ClientCallback<CustAccNotify>() {
                        @Override
                        public void onSuccess(CustAccNotify result) {
                            setupListBox(newAuxBox, result.getNotifyNewAux(), true);
                            setupListBox(auxAdjustmentBox, result.getNotifyAuxAdjustment(), false);
                        }
                    });
                }
            }
        }
    }

    private void getAppSettingsDataForNewAux(){
        clientFactory.getAppSettingRpc().getAppSettingByKey(AppSettings.CUSTOMER_ACCOUNT_NOTIFY_NEW_AUX,
                new ClientCallback<AppSetting>() {
            @Override
            public void onSuccess(AppSetting appSetting) {
                setupListBox(newAuxBox, new Boolean(appSetting.getValue()), true);
                getAppSettingsDataForAuxAdjust();
            }
            @Override
            public void onFailure(Throwable caught) {
                getAppSettingsDataForNewAux();
                super.onFailure(caught);
            }
        });
    }

    private void getAppSettingsDataForAuxAdjust(){
        clientFactory.getAppSettingRpc().getAppSettingByKey(AppSettings.CUSTOMER_ACCOUNT_NOTIFY_AUX_ADJUSTMENT,
                new ClientCallback<AppSetting>() {
            @Override
            public void onSuccess(AppSetting appSetting) {
                setupListBox(auxAdjustmentBox, new Boolean(appSetting.getValue()), false);
                getInheritanceData();
            }
            @Override
            public void onFailure(Throwable caught) {
                getAppSettingsDataForAuxAdjust();
                super.onFailure(caught);
            }
        });
    }

    private boolean haveValuesChanged(CustAccNotify notificationsToSave) {
        if(usagePointData.getCustomerAgreementData().getCustomerAccount() != null) {
            if (notificationsToSave.getNotifyNewAux()
                    != usagePointData.getCustomerAgreementData().getCustomerAccount().getNotifyNewAux()
                    || notificationsToSave.getNotifyAuxAdjustment()
                    != usagePointData.getCustomerAgreementData().getCustomerAccount().getNotifyAuxAdjustment()) {
                return true;
            }
        }else {
            Dialogs.displayErrorMessage(MessagesUtil.getInstance().getMessage("customer.account.does.not.exist"),
                    MediaResourceUtil.getInstance().getErrorIcon(),
                    btnSave.getAbsoluteLeft(), btnSave.getAbsoluteTop() + btnSave.getOffsetHeight(),
                    null);
            return false;
        }
        return false;
    }

    public void clearErrorMsg() {
        clearSelections();
    }

    private void saveUsagePointData() {
        UsagePointUpdatedEvent event = new UsagePointUpdatedEvent(usagePointWorkspaceView, usagePointData,
                UsagePointUpdatedEvent.SAVE_NOTIFICATIONS);
        event.setLeft(this.getAbsoluteLeft() + (this.getOffsetWidth() / 2));
        event.setTop(this.getAbsoluteTop());
        clientFactory.getEventBus().fireEvent(event);
        this.hide();
    }
}
