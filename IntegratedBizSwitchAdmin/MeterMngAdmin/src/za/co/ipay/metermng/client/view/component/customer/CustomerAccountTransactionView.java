package za.co.ipay.metermng.client.view.component.customer;

import com.google.gwt.cell.client.DateCell;
import com.google.gwt.core.client.GWT;
import com.google.gwt.event.dom.client.ChangeEvent;
import com.google.gwt.event.dom.client.ClickEvent;
import com.google.gwt.event.dom.client.ClickHandler;
import com.google.gwt.event.dom.client.KeyUpEvent;
import com.google.gwt.i18n.client.DateTimeFormat;
import com.google.gwt.place.shared.Place;
import com.google.gwt.uibinder.client.UiBinder;
import com.google.gwt.uibinder.client.UiField;
import com.google.gwt.uibinder.client.UiHandler;
import com.google.gwt.user.cellview.client.CellTable;
import com.google.gwt.user.cellview.client.Column;
import com.google.gwt.user.cellview.client.ColumnSortEvent;
import com.google.gwt.user.cellview.client.ColumnSortEvent.ListHandler;
import com.google.gwt.user.cellview.client.TextColumn;
import com.google.gwt.user.client.Window;
import com.google.gwt.user.client.rpc.AsyncCallback;
import com.google.gwt.user.client.ui.Button;
import com.google.gwt.user.client.ui.HTML;
import com.google.gwt.user.client.ui.HasHorizontalAlignment;
import com.google.gwt.user.client.ui.Label;
import com.google.gwt.user.client.ui.ListBox;
import com.google.gwt.user.client.ui.TextBox;
import com.google.gwt.user.client.ui.Widget;
import za.co.ipay.gwt.common.client.Dialogs;
import za.co.ipay.gwt.common.client.dataexport.GetRequestBuilder;
import za.co.ipay.gwt.common.client.factory.ResourcesFactoryUtil;
import za.co.ipay.gwt.common.client.form.Format;
import za.co.ipay.gwt.common.client.form.Format.FormatUtil;
import za.co.ipay.gwt.common.client.form.SimpleForm;
import za.co.ipay.gwt.common.client.handler.ConfirmHandler;
import za.co.ipay.gwt.common.client.resource.Messages;
import za.co.ipay.gwt.common.client.resource.Messages.MessagesUtil;
import za.co.ipay.gwt.common.client.validation.ClientValidatorUtil;
import za.co.ipay.gwt.common.client.widgets.TablePager;
import za.co.ipay.gwt.common.client.workspace.WorkspaceNotification;
import za.co.ipay.gwt.common.client.workspace.WorkspaceNotification.NotificationType;
import za.co.ipay.metermng.client.factory.ClientFactory;
import za.co.ipay.metermng.client.resource.image.MediaResource.MediaResourceUtil;
import za.co.ipay.metermng.client.rpc.callback.ClientCallback;
import za.co.ipay.metermng.client.view.component.BaseComponent;
import za.co.ipay.metermng.client.view.component.DateRangeFilterPanel;
import za.co.ipay.metermng.client.view.component.IPayDataProvider;
import za.co.ipay.metermng.client.view.component.IpayDataProviderFilter;
import za.co.ipay.metermng.client.view.workspace.UsagePointWorkspaceView;
import za.co.ipay.metermng.datatypes.AccountTransTypeE;
import za.co.ipay.metermng.mybatis.generated.model.AccountTrans;
import za.co.ipay.metermng.mybatis.generated.model.SpecialActionReasonsLog;
import za.co.ipay.metermng.shared.AccountTransData;
import za.co.ipay.metermng.shared.AuxAccountData;
import za.co.ipay.metermng.shared.CustomerAccountTransData;
import za.co.ipay.metermng.shared.MeterMngStatics;
import za.co.ipay.metermng.shared.dto.CustomerAgreementData;
import za.co.ipay.metermng.shared.dto.HistoryData;
import za.co.ipay.metermng.shared.dto.UsagePointData;
import za.co.ipay.metermng.shared.dto.user.MeterMngUser;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Comparator;
import java.util.Date;

public class CustomerAccountTransactionView extends BaseComponent implements IpayDataProviderFilter<CustomerAccountTransData>{
    private static final int DEFAULT_PAGE_SIZE = 15;
    private static CustomerTransactionWidgetUiBinder uiBinder = GWT.create(CustomerTransactionWidgetUiBinder.class);
    
    interface CustomerTransactionWidgetUiBinder extends UiBinder<Widget, CustomerAccountTransactionView> {
    }

    @UiField(provided=true) CellTable<CustomerAccountTransData> clltblTransactions;
    @UiField TablePager smplpgrTransactions;
    @UiField TextBox txtbxfilter;
    @UiField ListBox filterDropdown;
    @UiField DateRangeFilterPanel dateFilter;
    @UiField HTML dataName;
    @UiField HTML dataDescription;
    @UiField Label debtRefundDescription;
    @UiField SimpleForm form;
    @UiField Button btnExportCsv;
    
    private CustomerAgreementData customerAgreementData;
    private AuxAccountData auxAccountData;
    private CustomerAccountAdjustmentPanel customerAdjustmentPanel;
    private UsagePointWorkspaceView usagePointWorkspaceView;
    
    private Boolean isAuxAccount = false;
    private Boolean isCustAccountTrans = false;
    private Column<CustomerAccountTransData, Date> enteredDateCol;
    private IPayDataProvider<CustomerAccountTransData> dataProvider = new IPayDataProvider<CustomerAccountTransData>(this);
    private ArrayList<CustomerAccountTransData> theTransactiondata;
    ListHandler<CustomerAccountTransData> columnSortHandler;


    public CustomerAccountTransactionView(UsagePointWorkspaceView usagePointWorkspaceView, CustomerAgreementData customerAgreementData, ClientFactory clientFactory, Boolean isAuxAccount) {
        constructClass(usagePointWorkspaceView, customerAgreementData, clientFactory, isAuxAccount);
    }
 
    public CustomerAccountTransactionView(UsagePointWorkspaceView usagePointWorkspaceView, CustomerAgreementData customerAgreementData, ClientFactory clientFactory) {
        constructClass(usagePointWorkspaceView, customerAgreementData, clientFactory, false);
        UsagePointWorkspaceView upv =usagePointWorkspaceView;
        final UsagePointData usagePointData = upv.getUsagePointData();
        setLatestCustAccTransId(usagePointData);
    }
    private void constructClass(UsagePointWorkspaceView usagePointWorkspaceView, CustomerAgreementData customerAgreementData, ClientFactory clientFactory, Boolean isAuxAccount) {
        this.usagePointWorkspaceView = usagePointWorkspaceView;
        this.customerAgreementData = customerAgreementData;
        this.clientFactory = clientFactory;
        this.isAuxAccount = isAuxAccount;
        
        createTable();
        initWidget(uiBinder.createAndBindUi(this));
        initUi();
    }
    
    private void initUi() {
        initView();
        initTable();
        if (checkPermissions()) {
            initForm();
        }  else {
            form.removeFromParent();
        }
    }
    
    private void initView() {
        filterDropdown.addItem(MessagesUtil.getInstance().getMessage("customer.txn.trans.date"));
        filterDropdown.addItem(MessagesUtil.getInstance().getMessage("customer.txn.trans.type"));
        filterDropdown.addItem(MessagesUtil.getInstance().getMessage("customer.txn.our.ref"));
        if (!isAuxAccount) {
            dataName.setText(MessagesUtil.getInstance().getMessage("customer.txn.history"));
            dataDescription.setText(MessagesUtil.getInstance().getMessage("customer.txn.description"));
        } else {
            dataDescription.setText(MessagesUtil.getInstance().getMessage("customer.auxaccount.txn.description"));
            debtRefundDescription.setVisible(true);
        }
    }
    
    protected void createTable() {
    	 clltblTransactions = new CellTable<CustomerAccountTransData>(DEFAULT_PAGE_SIZE, ResourcesFactoryUtil.getInstance().getCellTableResources());
    	 clltblTransactions.ensureDebugId("accTransTable");
    }
    
    private void initTable() {
        //Date entered
        DateCell dateCell = new DateCell(DateTimeFormat.getFormat(FormatUtil.getInstance().getDateTimeFormat()));
        enteredDateCol = new Column<CustomerAccountTransData, Date>(dateCell) {
            @Override
            public Date getValue(CustomerAccountTransData data) {
                Format format = FormatUtil.getInstance();
                return format.parseDateTime(format.formatDateTime(data.getDateEntered()));
            }
        };
        enteredDateCol.setSortable(true);
        enteredDateCol.setDefaultSortAscending(false);

        //user entered
        TextColumn<CustomerAccountTransData> userEnteredCol = new TextColumn<CustomerAccountTransData>() {
            @Override
            public String getValue(CustomerAccountTransData data) {
                return data.getUserRecEntered();
            }
        };
        
        // Transaction Type
        TextColumn<CustomerAccountTransData> tranTypeCol = new TextColumn<CustomerAccountTransData>() {
            @Override
            public String getValue(CustomerAccountTransData data) {
                return data.getTransType();    
            }
        };
        
        //Transaction Date
        Column<CustomerAccountTransData, Date> transDateCol = new Column<CustomerAccountTransData, Date>(dateCell) {
            @Override
            public Date getValue(CustomerAccountTransData data) {
                return data.getTransDate();
            }
        };
        transDateCol.setSortable(true);
        
        //Comment
        TextColumn<CustomerAccountTransData> commentCol = new TextColumn<CustomerAccountTransData>() {
            @Override
            public String getValue(CustomerAccountTransData data) {
                return data.getComment();
            }
        };

        TextColumn<CustomerAccountTransData> reasonCol = new TextColumn<CustomerAccountTransData>() {
            @Override
            public String getValue(CustomerAccountTransData data) {
                String result = "";
                SpecialActionReasonsLog reasonsLog = data.getSpecialActionReasonsLog();
                if (reasonsLog != null) {
                    result = reasonsLog.getReasonText();
                }
                return result;
            }
        };

        //Account Ref
        TextColumn<CustomerAccountTransData> ourRefCol = new TextColumn<CustomerAccountTransData>() {
            @Override
            public String getValue(CustomerAccountTransData data) {
                return data.getOurRef();
            }
        };
        
        //Amount incl tax
        TextColumn<CustomerAccountTransData> amtCol = new TextColumn<CustomerAccountTransData>() {
            @Override
            public String getValue(CustomerAccountTransData data) {
                return FormatUtil.getInstance().formatCurrency(data.getAmtInclTax(), true);
            }
        };
        amtCol.setHorizontalAlignment(HasHorizontalAlignment.ALIGN_RIGHT);
        
        //Tax
        TextColumn<CustomerAccountTransData> taxCol = new TextColumn<CustomerAccountTransData>() {
            @Override
            public String getValue(CustomerAccountTransData data) {
                return FormatUtil.getInstance().formatCurrency(data.getAmtTax(), true);
            }
        };
        taxCol.setHorizontalAlignment(HasHorizontalAlignment.ALIGN_RIGHT);
        
        //Balance
        TextColumn<CustomerAccountTransData> balCol = new TextColumn<CustomerAccountTransData>() {
            @Override
            public String getValue(CustomerAccountTransData data) {
                return FormatUtil.getInstance().formatCurrency(data.getResultantBalance(), true);
            }
        };
        balCol.setHorizontalAlignment(HasHorizontalAlignment.ALIGN_RIGHT);

        clltblTransactions.addColumn(enteredDateCol, MessagesUtil.getInstance().getMessage("customer.txn.ent.date"));
        clltblTransactions.addColumn(userEnteredCol, MessagesUtil.getInstance().getMessage("customer.txn.user"));
        clltblTransactions.addColumn(tranTypeCol, MessagesUtil.getInstance().getMessage("customer.txn.trans.type"));
        clltblTransactions.addColumn(transDateCol, MessagesUtil.getInstance().getMessage("customer.txn.trans.date"));
        clltblTransactions.addColumn(commentCol, MessagesUtil.getInstance().getMessage("customer.txn.comment"));
        clltblTransactions.addColumn(reasonCol, MessagesUtil.getInstance().getMessage("customer.txn.reason"));
        clltblTransactions.addColumn(ourRefCol, MessagesUtil.getInstance().getMessage("customer.txn.our.ref"));
        clltblTransactions.addColumn(amtCol, MessagesUtil.getInstance().getMessage("customer.txn.amt"));
        clltblTransactions.addColumn(taxCol, MessagesUtil.getInstance().getMessage("customer.txn.tax"));
        clltblTransactions.addColumn(balCol, MessagesUtil.getInstance().getMessage("customer.txn.bal"));
        
        dataProvider.addDataDisplay(clltblTransactions);
        smplpgrTransactions.setDisplay(clltblTransactions);
        
        columnSortHandler = new ListHandler<CustomerAccountTransData>(dataProvider.getList());
        columnSortHandler.setComparator(enteredDateCol, new Comparator<CustomerAccountTransData>() {
            public int compare(CustomerAccountTransData o1, CustomerAccountTransData o2) {
                if (o1 == o2) {

                    return 0;
                }
                if (o1 != null) {
                    return (o2 != null) ? o1.getDateEntered().compareTo(o2.getDateEntered()) : 1;
                }
                return -1;
            }
        });
        clltblTransactions.addColumnSortHandler(columnSortHandler);
        clltblTransactions.getColumnSortList().push(enteredDateCol);
        ColumnSortEvent.fire(clltblTransactions, clltblTransactions.getColumnSortList());
        dateFilter.setDataProvider(dataProvider);
        handleFilterDropdownSelection(null);
    }
    
    public void clearTransactionTable() {
        dataProvider.setList(new ArrayList<CustomerAccountTransData>());
        dataProvider.refresh();
    }

    
    //---------------------------------------Customer Account Transactions --------------------------------------------
    public void getCustomerAccTransList(CustomerAgreementData customerAgreementData) {
        clientFactory.getCustomerRpc().fetchCustomerAccountTransactions(customerAgreementData, new ClientCallback<ArrayList<CustomerAccountTransData>>() {
            @Override
            public void onSuccess(ArrayList<CustomerAccountTransData> result) {
                if (result != null) {
                    setCustomerAccTransList(result);
                }
            }
        });
    }
    
    public void setCustomerAccTransList(ArrayList<CustomerAccountTransData> thedata) {
        theTransactiondata = thedata;
        dataProvider.setList(theTransactiondata);
        columnSortHandler.setList(dataProvider.getList());
        ColumnSortEvent.fire(clltblTransactions, clltblTransactions.getColumnSortList());
        customerAdjustmentPanel.setValues(null, null);
        clltblTransactions.setPageStart(0);
    }
    
    //---------------------------------------Aux Account Transactions --------------------------------------------
    public void displayAuxAccountData(CustomerAgreementData customerAgreementData, AuxAccountData auxAccountData){
        this.customerAgreementData = customerAgreementData;
        this.auxAccountData = auxAccountData;
        String headerStr = MessagesUtil.getInstance().getMessage("customer.auxaccount.txn.history", new String[]{auxAccountData.getAccountName()});
        if (auxAccountData.getSuspendUntil() != null && auxAccountData.getSuspendUntil().after(new Date())) {
            headerStr += " " + MessagesUtil.getInstance().getMessage("customer.auxaccount.txn.history.suspend.until",
                    new String[] { FormatUtil.getInstance().formatDateTime(auxAccountData.getSuspendUntil()) });
        }
        dataName.setText(headerStr);
        getAuxAccTransList();
        UsagePointWorkspaceView upv = usagePointWorkspaceView;
        final UsagePointData usagePointData = upv.getUsagePointData();
        setLatestAuxAdjustmentId(usagePointData);

    }
    
    public void getAuxAccTransList() {
        clientFactory.getCustomerRpc().fetchAuxiliaryAccountTransactions(auxAccountData.getId(), new ClientCallback<ArrayList<CustomerAccountTransData>>() {
            @Override
            public void onSuccess(ArrayList<CustomerAccountTransData> result) {
                if (result != null) {
                    setAuxAccTransList(result);
                }
            }
        });
    }
    
    public void setAuxAccTransList(ArrayList<CustomerAccountTransData> thedata) {
        theTransactiondata = thedata;
        dataProvider.setList(theTransactiondata);
        columnSortHandler.setList(dataProvider.getList());
        ColumnSortEvent.fire(clltblTransactions, clltblTransactions.getColumnSortList());
        customerAdjustmentPanel.setValues(auxAccountData.getBalance(), auxAccountData.getAccountName());
        handleFormVisibility();
        clltblTransactions.setPageStart(0);
    }

    //------------------------------------------------------------------------------------------------------------
    @UiHandler("txtbxfilter")
    void handleFilterChange(KeyUpEvent e) {
        if (txtbxfilter.getText().trim().isEmpty()) {
            dataProvider.resetFilter();
        } else {
            dataProvider.setFilter(txtbxfilter.getText());
        }
        dataProvider.setList(theTransactiondata);
        columnSortHandler.setList(dataProvider.getList());
        smplpgrTransactions.firstPage();
        clltblTransactions.setPageStart(0);
    }

    @Override
    public boolean isValid(CustomerAccountTransData value, String filter) {
        String selectedFilter = filterDropdown.getSelectedValue();
        Messages messagesInstance = MessagesUtil.getInstance();
        if (selectedFilter.equals(messagesInstance.getMessage("customer.txn.trans.date"))) {
            return dateFilter.isValid(value.getTransDate(), filter);
        }
        if (selectedFilter.equals(messagesInstance.getMessage("customer.txn.trans.type"))) {
            return (AccountTransTypeE.fromId(value.getAccountTransTypeId()).toString()).toLowerCase()
                    .contains(filter.toLowerCase());
        }
        if (selectedFilter.equals(messagesInstance.getMessage("customer.txn.our.ref"))) {
            return value.getOurRef().toLowerCase().contains(filter.toLowerCase());
        } else {
            return true;
        }
    }
    
    @UiHandler("filterDropdown")
    void handleFilterDropdownSelection(ChangeEvent changeEvent) {
        dataProvider.resetFilter();
        txtbxfilter.setText("");
        boolean isDateSelected = filterDropdown.getSelectedItemText()
                .equals(MessagesUtil.getInstance().getMessage("customer.txn.trans.date"));
        dateFilter.changeFilter(isDateSelected);
        txtbxfilter.setVisible(!isDateSelected);
    }
    
    //**************************************** Input Adjustment Form **************************************************************
    public void setCustomerAgreementData(CustomerAgreementData customerAgreementData){
        this.customerAgreementData = customerAgreementData;
        if (!checkPermissions()) {
            form.removeFromParent();
        }
    }
    
    private void initForm() {
        customerAdjustmentPanel = new CustomerAccountAdjustmentPanel(form, clientFactory);
        form.setHasDirtyDataManager(usagePointWorkspaceView);
        form.getFormFields().add(customerAdjustmentPanel);
        form.ensureDebugId("custAdjPanel");
        if (!isAuxAccount) {
            form.getFormPanel().setHeadingText(MessagesUtil.getInstance().getMessage("customer.txn.input"));
        } else {
            form.getFormPanel().setHeadingText(MessagesUtil.getInstance().getMessage("customer.auxaccount.adjust"));
        }
        
        form.getSaveBtn().setText(MessagesUtil.getInstance().getMessage("button.update"));
        form.getSaveBtn().ensureDebugId("saveButton");
        form.getSaveBtn().addClickHandler(new ClickHandler() {
            @Override
            public void onClick(ClickEvent event) {
                onSaveButtonClick();
            }
        });
        form.getOtherBtn().setText(MessagesUtil.getInstance().getMessage("button.cancel"));
        form.getOtherBtn().ensureDebugId("cancelButton");
        form.getOtherBtn().addClickHandler(new ClickHandler() {
            @Override
            public void onClick(ClickEvent event) {
                form.checkDirtyData(new ConfirmHandler() {
                    @Override
                    public void confirmed(boolean confirm) {
                        if (confirm) {
                            form.setDirtyData(false);
                            customerAdjustmentPanel.clearFields();
                            customerAdjustmentPanel.clearErrors();
                        }
                    }
                });
            }
        });
    }

    private void setLatestCustAccTransId(final UsagePointData usagePointData){
            if(usagePointData != null) {
                clientFactory.getSearchRpc().setLatestCustAccTransId(usagePointData, new AsyncCallback<HistoryData>() {
                    @Override
                    public void onSuccess(HistoryData historyData) {
                        usagePointData.setHistoryData(historyData);
                    }

                    @Override
                    public void onFailure(Throwable caught) {
                    }
                });
            }

    }
    private void setLatestAuxAdjustmentId(final UsagePointData usagePointData){
        if(usagePointData != null) {
            clientFactory.getSearchRpc().setLatestAuxAdjustmentId(usagePointData, new AsyncCallback<HistoryData>() {
                @Override
                public void onSuccess(HistoryData historyData) {
                    usagePointData.setHistoryData(historyData);
                }

                @Override
                public void onFailure(Throwable caught) {
                }
            });
        }

    }

    private void onSaveButtonClick() {
        if (customerAgreementData == null || customerAgreementData.getId() == null) {
            Dialogs.displayInformationMessage(
                    MessagesUtil.getInstance().getSavedMessage(
                            new String[] { MessagesUtil.getInstance().getMessage("customer.txn.no.agreement") }),
                    MediaResourceUtil.getInstance().getInformationIcon(), form.getSaveBtn().getAbsoluteLeft(),
                    form.getSaveBtn().getAbsoluteTop(), MessagesUtil.getInstance().getMessage("button.close"));
            return;
        }

        final Button btnSave = form.getSaveBtn();
        final UsagePointWorkspaceView upv =usagePointWorkspaceView;
        UsagePointData upd = upv.getUsagePointData();

        clientFactory.getSearchRpc().checkUsagePointDataIntegrity(upd, new AsyncCallback<Boolean>() {
            @Override
            public void onSuccess(Boolean result) {
                if (result) {
                    AccountTransData accountTrans = mapFormToData();
                    if (isValid(accountTrans)) {
                        btnSave.setEnabled(false);
                        form.getOtherBtn().setVisible(false);
                        if (!isAuxAccount) {
                            saveCustomerAccountAdjustment(accountTrans);
                        } else {
                            boolean isRefund = auxAccountData.getBalance().compareTo(BigDecimal.ZERO) == 1;
                            boolean isIncrease = customerAdjustmentPanel.radioIncrease.getValue();
                            if ((!isRefund && isIncrease) || (isRefund && !isIncrease)) {
                                accountTrans.setAmtInclTax(accountTrans.getAmtInclTax().negate());
                                accountTrans.setAmtTax(accountTrans.getAmtTax().negate());
                            }
                            saveAuxiliaryAccountAdjustment(accountTrans);
                        }
                    }
                } else {
                     Place place = upv.getPlace();
                     upv.processInvalidState(place);
                }
            }

            @Override
            public void onFailure(Throwable caught) {
                resetButtons();
            }
        });
    }


    private void saveCustomerAccountAdjustment(AccountTransData accountTrans) {
        clientFactory.getCustomerRpc().inputAccountAdjustment(accountTrans, new ClientCallback<BigDecimal>(form.getSaveBtn().getAbsoluteLeft(), form.getSaveBtn().getAbsoluteTop()) {
            @Override
            public void onSuccess(BigDecimal result) {
                if (result != null) {
                    successfulAdjustment(result);
                }
            }

            @Override
            public void onFailureClient() {
                resetButtons();
            }
        });
    }

    private void saveAuxiliaryAccountAdjustment(AccountTransData accountTrans) {
        clientFactory.getCustomerRpc().inputAuxAccountAdjustment(accountTrans, new ClientCallback<BigDecimal>(form.getSaveBtn().getAbsoluteLeft(), form.getSaveBtn().getAbsoluteTop()) {
            @Override
            public void onSuccess(BigDecimal result) {
                if (result != null) {
                    successfulAdjustment(result);
                }
            }

            @Override
            public void onFailureClient() {
                resetButtons();
            }
        });
    }
    private void successfulAdjustment(BigDecimal result) {
        customerAdjustmentPanel.clearFields();
        resetButtons();
        //update table
        if (!isAuxAccount) {
            getCustomerAccTransList(customerAgreementData);
        } else {
            getAuxAccTransList();
        }
        UsagePointWorkspaceView upv = usagePointWorkspaceView;
        final UsagePointData usagePointData = upv.getUsagePointData();
        if(isAuxAccount){
            setLatestAuxAdjustmentId(usagePointData);
        }else{
            setLatestCustAccTransId(usagePointData);
        }
        dataProvider.refresh();
        Dialogs.displayInformationMessage(MessagesUtil.getInstance().getSavedMessage(new String[]
                { MessagesUtil.getInstance().getMessage("customer.txn.successful.adjustment") }), 
                MediaResourceUtil.getInstance().getInformationIcon(), 
                form.getSaveBtn().getAbsoluteLeft(), 
                form.getSaveBtn().getAbsoluteTop(), 
                MessagesUtil.getInstance().getMessage("button.close"));
        sendNotification(result);
    }
    private void resetButtons() {
        form.getSaveBtn().setEnabled(true);
        form.getOtherBtn().setVisible(true);
    }
    
 
    private AccountTransData mapFormToData() {
        AccountTransData accTrans = new AccountTransData();
        if (!isAuxAccount) {
            accTrans.setCustomerAccountId(customerAgreementData.getCustomerAccountId());
        } else {
            accTrans.setAuxAccountId(auxAccountData.getId());
        }
        accTrans.setCustomerAgreementId(customerAgreementData.getId());
        accTrans.setAccountTransTypeId(AccountTransTypeE.ADJUSTMENT.getId());
        Date dte = new Date();
        accTrans.setTransDate(dte);
        accTrans.setDateEntered(dte);
        accTrans.setComment(customerAdjustmentPanel.getComment());
        accTrans.setAccountRef(customerAdjustmentPanel.getAccRef());
        accTrans.setOurRef(customerAdjustmentPanel.getOurRef());
        accTrans.setAmtInclTax(customerAdjustmentPanel.getAmount());
        accTrans.setAmtTax(customerAdjustmentPanel.getTax());
        accTrans.setUserRecEntered(clientFactory.getUser().getUserName());
        accTrans.setSpecialActionReasonsLog(customerAdjustmentPanel.specialactionreasons.getLogEntry());

        return accTrans;
    }
    
    private boolean isValid(AccountTrans accountTrans) {
        boolean valid = true;

        customerAdjustmentPanel.clearErrors();

        if (!ClientValidatorUtil.getInstance().validateField(accountTrans, "accountRef",
                customerAdjustmentPanel.accRefElement)) {
            valid = false;
        }

        if (!ClientValidatorUtil.getInstance().validateField(accountTrans, "ourRef",
                customerAdjustmentPanel.ourRefElement)) {
            valid = false;
        }
        
        if(accountTrans.getOurRef().trim().isEmpty()){        
                customerAdjustmentPanel.ourRefElement 
                .setErrorMsg(MessagesUtil.getInstance().getMessage("error.field.ourref.null"));
                valid = false;
        }

        if (!ClientValidatorUtil.getInstance().validateField(accountTrans, "amtInclTax",
                customerAdjustmentPanel.amtElement)) {
            valid = false;
        }

        BigDecimal amount = accountTrans.getAmtInclTax();
        if (amount == null) {
            amount = BigDecimal.ZERO;
        }
        int amountComparison = amount.compareTo(BigDecimal.ZERO);
        int taxComparison = 0;
        BigDecimal tax = accountTrans.getAmtTax();
        if (tax != null) {
            taxComparison = tax.compareTo(BigDecimal.ZERO);
        }
        if (valid) {
        	
        	if (amountComparison == 0) {
                customerAdjustmentPanel
                        .amtVsTaxError(MessagesUtil.getInstance().getMessage("customer.txn.error.amount.incl.tax.is.zero"));
                valid = false;
            } 
        	
        	if(tax.compareTo(BigDecimal.ZERO) != 0 && amount.compareTo(BigDecimal.ZERO) != 0) {
	        	if(amount.signum() != tax.signum()) {
	            		customerAdjustmentPanel.amtVsTaxError(
	                            MessagesUtil.getInstance().getMessage("customer.txn.error.tax.and.amount.different.sign"));
	                    valid = false;
	            }
        	}
        	
            
            // check if amount is negative
            if(amount.compareTo(BigDecimal.ZERO) == -1) { 
            	
            	if(amount.compareTo(tax) == 1) {
            		customerAdjustmentPanel.amtVsTaxError(
                            MessagesUtil.getInstance().getMessage("customer.txn.error.tax.less.than.amount"));
                    valid = false;
            	}
           
            } else if (amount.compareTo(BigDecimal.ZERO) != 0 && amount.compareTo(tax) == -1) {
                customerAdjustmentPanel.amtVsTaxError(
                        MessagesUtil.getInstance().getMessage("customer.txn.error.tax.more.than.amount"));
                valid = false;
            } 
        }
        if (!ClientValidatorUtil.getInstance().validateField(accountTrans, "comment",
                customerAdjustmentPanel.commentElement)) {
            valid = false;
        }

        if (!valid || !customerAdjustmentPanel.specialactionreasons.validate()) {
            valid = false;
        }

        if (isAuxAccount) {
            boolean isDecrease = customerAdjustmentPanel.radioDecrease.getValue();
            if (!isDecrease && !customerAdjustmentPanel.radioIncrease.getValue()) {
                customerAdjustmentPanel.adjustmentElement
                        .showErrorMsg(MessagesUtil.getInstance().getMessage("customer.auxaccount.error.adjustment"));
                valid = false;
            }
            if (amountComparison == -1) {
                customerAdjustmentPanel.amtElement.showErrorMsg(
                        MessagesUtil.getInstance().getMessage("customer.auxaccount.error.amount.negative"));
                valid = false;
            }
            if (taxComparison == -1) {
                customerAdjustmentPanel.taxElement
                        .showErrorMsg(MessagesUtil.getInstance().getMessage("customer.auxaccount.error.tax.negative"));
                valid = false;
            }
            if (isDecrease) {
                BigDecimal currentBalance = auxAccountData.getBalance();
                if (currentBalance.compareTo(BigDecimal.ZERO) == 1) {
                    BigDecimal newBalance = currentBalance.subtract(amount);
                    if (newBalance.compareTo(BigDecimal.ZERO) < 0) {
                        Dialogs.displayErrorMessage(
                                MessagesUtil.getInstance().getMessage("customer.auxaccount.error.debt",
                                        new String[] { FormatUtil.getInstance().formatCurrency(newBalance, true) }),
                                MediaResourceUtil.getInstance().getErrorIcon(), null);
                        valid = false;
                    }
                } else {
                    BigDecimal newBalance = currentBalance.add(amount);
                    if (newBalance.compareTo(BigDecimal.ZERO) == 1) {
                        Dialogs.displayErrorMessage(
                                MessagesUtil.getInstance().getMessage("customer.auxaccount.error.refund",
                                        new String[] { FormatUtil.getInstance().formatCurrency(newBalance, true) }),
                                MediaResourceUtil.getInstance().getErrorIcon(), null);
                        valid = false;
                    }
                }
            }
        }

        return valid;
    }

    public boolean checkPermissions() {
        boolean allow = clientFactory.getUser().hasPermission(MeterMngStatics.ACCESS_PERMISSION_MM_CUST_ACC_ADJ);
        boolean groupHasGlobal = UsagePointWorkspaceView.hasGlobalContractElement(usagePointWorkspaceView.getUsagePoint(), clientFactory.isGroupGroupUser());
        if (clientFactory.isEnableAccessGroups() && (groupHasGlobal || clientFactory.isGroupGlobalUser())) {
            allow = false;
        }
        return allow;
    }
    
    public void refreshCustomerTransactionTable() {
        clearTransactionTable();
        getCustomerAccTransList(customerAgreementData);
        dataProvider.refresh();
    }

    private void handleFormVisibility() {
        MeterMngUser user = clientFactory.getUser();
        boolean hasRights = false;
        //Perms below handle if adjustment form is shown
        if (auxAccountData.getBalance().compareTo(BigDecimal.ZERO) == 1) {
            hasRights = user.hasPermission(MeterMngStatics.ADMIN_PERMISSION_MM_AUX_ADJUST_REFUND);
        } else {
            hasRights = user.hasPermission(MeterMngStatics.ADMIN_PERMISSION_MM_AUX_ADJUST_DEBT);
        }

        form.setVisible(hasRights);
    }
    
    private void sendNotification(BigDecimal customerBalance) {
        //Notify any affected tabs                    
        if (!isAuxAccount) {
            clientFactory.getWorkspaceContainer().notifyWorkspaces(
                    new WorkspaceNotification(NotificationType.DATA_UPDATED, 
                            MeterMngStatics.CUSTOMER_ACCOUNT_BALANCE_ADJUSTED, 
                            customerBalance));
        } else {
            clientFactory.getWorkspaceContainer().notifyWorkspaces(
                    new WorkspaceNotification(NotificationType.DATA_UPDATED, 
                            MeterMngStatics.AUX_ACCOUNT_BALANCE_ADJUSTED));
        }
    }

    @UiHandler("btnExportCsv")
    void handleExportCsvButton(ClickEvent clickEvent) {
        if (!theTransactiondata.isEmpty()) {
            String filterValue = dataProvider.getFilter();
            if (filterValue == null) {
                filterValue = "";
            }
            String exportType = MeterMngStatics.CUSTOMER_CUSTOMER_TRANS_EXPORT;
            String accountId = customerAgreementData.getCustomerAccountId().toString();
            if(isAuxAccount) {
            	exportType = MeterMngStatics.AUX_CUSTOMER_CUSTOMER_TRANS_EXPORT;
            	accountId = auxAccountData.getId().toString();
            }
        	String encodedUrl = new GetRequestBuilder().withBaseUrl(GWT.getHostPageBaseURL())
                    .withTargetUrl("customertransexport").addParam(MeterMngStatics.EXPORT_TYPE, exportType)
                    .addParam(MeterMngStatics.EXPORT_FILE_NAME_PREFIX, customerAgreementData.getAgreementRef())
                    .addParam(exportType, accountId)
                    .addParam(MeterMngStatics.EXPORT_FILTER_ELEMENT, filterDropdown.getItemText(filterDropdown.getSelectedIndex()))
                    .addParam(MeterMngStatics.EXPORT_FILTER_VALUE, filterValue).toEncodedUrl();
            if (encodedUrl != null) {
                Window.open(encodedUrl, "_blank", ""); //opens in same window
            }
        } else {
            Dialogs.displayErrorMessage(MessagesUtil.getInstance().getMessage("export.error.nodata"),
                    MediaResourceUtil.getInstance().getErrorIcon());
        }
    }
}
