<!DOCTYPE ui:UiBinder SYSTEM "http://dl.google.com/gwt/DTD/xhtml.ent">
<ui:UiBinder xmlns:ui="urn:ui:com.google.gwt.uibinder" 
                xmlns:g="urn:import:com.google.gwt.user.client.ui"
                xmlns:p3="urn:import:za.co.ipay.gwt.common.client.form"
                xmlns:entity="urn:import:za.co.ipay.metermng.client.view.component.group.entity"
                xmlns:ndp="urn:import:za.co.ipay.metermng.client.view.component.group.entity.ndp">
  <ui:style>    
  </ui:style>
  
  <ui:with field="msg" type="za.co.ipay.metermng.client.i18n.UiMessages" />
  
        <g:FlowPanel>
             <g:DisclosurePanel open="false" styleName="gwt-DisclosurePanel-component" ui:field="contactDisclosurePanel" width="100%" debugId="contactDisclosurePanel">
                <g:customHeader>
                    <g:FlowPanel styleName="gwt-DisclosurePanel-component .header" width="100%">
                        <g:HorizontalPanel verticalAlignment="ALIGN_MIDDLE">
                            <g:Image styleName="horizontalFlow" ui:field="contactOpenorclosearrow"/>
                            <g:Image ui:field="contactImage" />
                            <g:Label styleName="gwt-DisclosurePanel-component .header" text="{msg.getEntityTitle}" horizontalAlignment="ALIGN_LEFT" />
                        </g:HorizontalPanel>
                    </g:FlowPanel>
                </g:customHeader>
                <g:FlowPanel width="100%">
                    <entity:EntityContactPanel ui:field="contactPanel" width="100%"></entity:EntityContactPanel>
                </g:FlowPanel>
             </g:DisclosurePanel>
             
             <g:DisclosurePanel open="false" styleName="gwt-DisclosurePanel-component" ui:field="thresholdDisclosurePanel" width="100%" debugId="thresholdDisclosurePanel">
                <g:customHeader>
                    <g:FlowPanel styleName="gwt-DisclosurePanel-component .header" width="100%">
                        <g:HorizontalPanel verticalAlignment="ALIGN_MIDDLE">
                            <g:Image styleName="horizontalFlow" ui:field="thresholdOpenorclosearrow"/>
                            <g:Image ui:field="thresholdImage" />
                            <g:Label styleName="gwt-DisclosurePanel-component .header" text="{msg.getThresholdTitle}" horizontalAlignment="ALIGN_LEFT" />
                        </g:HorizontalPanel>
                    </g:FlowPanel>
                </g:customHeader>
                <g:FlowPanel width="100%">
                    <entity:ThresholdsPanel ui:field="thresholdsPanel" width="100%"></entity:ThresholdsPanel>
                </g:FlowPanel>
             </g:DisclosurePanel>
             
             <g:DisclosurePanel open="false" styleName="gwt-DisclosurePanel-component" ui:field="ndpDisclosurePanel" width="100%" debugId="ndpDisclosurePanel">
                <g:customHeader>
                    <g:FlowPanel styleName="gwt-DisclosurePanel-component .header" width="100%">
                        <g:HorizontalPanel verticalAlignment="ALIGN_MIDDLE">
                            <g:Image styleName="horizontalFlow" ui:field="ndpOpenorclosearrow"/>
                            <g:Image ui:field="ndpImage" />
                            <g:Label styleName="gwt-DisclosurePanel-component .header" ui:field="ndpDisclosureComponentLabel" text="{msg.getNdpDisclosureTitle}" horizontalAlignment="ALIGN_LEFT" />
                        </g:HorizontalPanel>
                    </g:FlowPanel>
                </g:customHeader>
                <g:VerticalPanel width="100%">
                    <ndp:NonDisconnectPeriodPanel ui:field="ndpPanel" width="100%"></ndp:NonDisconnectPeriodPanel>
                </g:VerticalPanel>
             </g:DisclosurePanel>    
                     <g:DisclosurePanel open="false" styleName="gwt-DisclosurePanel-component" ui:field="notificationDisclosurePanel" width="100%" debugId="notificationDisclosurePanel">
                <g:customHeader>
                    <g:FlowPanel styleName="gwt-DisclosurePanel-component .header" width="100%">
                        <g:HorizontalPanel verticalAlignment="ALIGN_MIDDLE">
                            <g:Image styleName="horizontalFlow" ui:field="notificationOpenorclosearrow"/>
                            <g:Image ui:field="notificationImage" />
                            <g:Label styleName="gwt-DisclosurePanel-component .header" text="{msg.getCustomerManageNotificationTypes}" horizontalAlignment="ALIGN_LEFT" />
                        </g:HorizontalPanel>
                    </g:FlowPanel>
                </g:customHeader>
                <g:FlowPanel width="100%">
                    <entity:NotificationsPanel ui:field="notificationsPanel" width="100%"></entity:NotificationsPanel>
                </g:FlowPanel>
         </g:DisclosurePanel>         
        </g:FlowPanel>
	</ui:UiBinder> 