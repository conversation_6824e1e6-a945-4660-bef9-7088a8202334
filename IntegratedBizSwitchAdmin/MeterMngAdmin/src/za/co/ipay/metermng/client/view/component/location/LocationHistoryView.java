package za.co.ipay.metermng.client.view.component.location;

import java.util.ArrayList;
import java.util.Comparator;
import java.util.Date;

import za.co.ipay.gwt.common.client.factory.ResourcesFactoryUtil;
import za.co.ipay.gwt.common.client.form.Format;
import za.co.ipay.gwt.common.client.form.Format.FormatUtil;
import za.co.ipay.gwt.common.client.resource.Messages.MessagesUtil;
import za.co.ipay.gwt.common.client.widgets.TablePager;
import za.co.ipay.metermng.client.view.component.BaseComponent;
import za.co.ipay.metermng.client.view.component.DateRangeFilterPanel;
import za.co.ipay.metermng.client.view.component.IPayDataProvider;
import za.co.ipay.metermng.client.view.component.IpayDataProviderFilter;
import za.co.ipay.metermng.client.view.component.TranslateDbTerms;
import za.co.ipay.metermng.client.widget.StatusTableColumn;
import za.co.ipay.metermng.shared.LocationHistData;

import com.google.gwt.cell.client.Cell.Context;
import com.google.gwt.cell.client.DateCell;
import com.google.gwt.core.client.GWT;
import com.google.gwt.event.dom.client.ChangeEvent;
import com.google.gwt.event.dom.client.KeyUpEvent;
import com.google.gwt.i18n.client.DateTimeFormat;
import com.google.gwt.uibinder.client.UiBinder;
import com.google.gwt.uibinder.client.UiField;
import com.google.gwt.uibinder.client.UiHandler;
import com.google.gwt.user.cellview.client.CellTable;
import com.google.gwt.user.cellview.client.Column;
import com.google.gwt.user.cellview.client.ColumnSortEvent;
import com.google.gwt.user.cellview.client.ColumnSortEvent.ListHandler;
import com.google.gwt.user.cellview.client.TextColumn;
import com.google.gwt.user.client.ui.HTML;
import com.google.gwt.user.client.ui.ListBox;
import com.google.gwt.user.client.ui.TextBox;
import com.google.gwt.user.client.ui.Widget;

public class LocationHistoryView extends BaseComponent implements IpayDataProviderFilter<LocationHistData>{

    private static final int DEFAULT_PAGE_SIZE = 15;
     
    interface LocationHistoryWidgetUiBinder extends UiBinder<Widget, LocationHistoryView> {
    }

    private static LocationHistoryWidgetUiBinder uiBinder = GWT.create(LocationHistoryWidgetUiBinder.class);

    @UiField(provided=true) CellTable<LocationHistData>  locationtblHistory;
    @UiField TablePager smplpgrHistory;
    @UiField TextBox txtbxfilter;
    @UiField ListBox filterDropdown;
    @UiField DateRangeFilterPanel dateFilter;
    @UiField HTML dataName;
    @UiField HTML dataDescription;
        
    private IPayDataProvider<LocationHistData> dataProvider;
    private ArrayList<LocationHistData> thehistorydata;
 
    private Column<LocationHistData, Date> dateModifiedColumn;
    private Column<LocationHistData, String> modifiedbyColumn;
    private ListHandler<LocationHistData> columnSortHandler;
    private boolean viewConstructed = false;
    
    public LocationHistoryView()  {
        createTable();
        initWidget(uiBinder.createAndBindUi(this));
        initUi();
    }
    
    private void initUi() {
        initView();
        initTable();
        
        viewConstructed = true;
    }

    public boolean isViewConstructed() {
        return viewConstructed;
    }
    
    private void initView() {
        filterDropdown.addItem(MessagesUtil.getInstance().getMessage("location.user"));
        filterDropdown.addItem(MessagesUtil.getInstance().getMessage("location.date"));
        dataName.setText(MessagesUtil.getInstance().getMessage("location.history"));
    	dataDescription.setText(MessagesUtil.getInstance().getMessage("location.history.description"));
    }
    
    protected void createTable() {
    	 locationtblHistory = new CellTable<LocationHistData>(DEFAULT_PAGE_SIZE, ResourcesFactoryUtil.getInstance().getCellTableResources());
    }
    
    private void initTable() {
    	
    	 if (dataProvider == null) {
             dataProvider = new IPayDataProvider<LocationHistData>(this);
        
            DateCell dateCell = new DateCell(DateTimeFormat.getFormat(FormatUtil.getInstance().getDateTimeFormat()));
	        dateModifiedColumn = new Column<LocationHistData, Date>(dateCell) {
	            @Override
	            public Date getValue(LocationHistData data) {
                    Format format = FormatUtil.getInstance();
                    return format.parseDateTime(format.formatDateTime(data.getDateRecModified()));
	            }
	        };
	        dateModifiedColumn.setSortable(true);
	        dateModifiedColumn.setDefaultSortAscending(false);
	        
	        modifiedbyColumn = new TextColumn<LocationHistData>() {
	            @Override
	            public String getValue(LocationHistData data) {
	                return data.getUserRecEntered();
	            }
	        };
	        modifiedbyColumn.setSortable(true);
	        
	        TextColumn<LocationHistData> actionColumn = new TextColumn<LocationHistData>() {
	            @Override
	            public String getValue(LocationHistData data) {
	                return TranslateDbTerms.translateDbAction(data.getUserAction());
	            }
	        };
	
	        TextColumn<LocationHistData> statusColumn = new StatusTableColumn<LocationHistData>() {
				@Override
		        public String getCellStyleNames(Context context, LocationHistData data) {
		            String style = super.getCellStyleNames(context, data);
		            if (data.isRecordStatusChanged()) {
		                style += " cellChanged ";
		            }
		            return style;
		        }
			};
			
			TextColumn<LocationHistData> address1Column = new TextColumn<LocationHistData>() {
                @Override
                public String getValue(LocationHistData data) {
                    return data.getAddressLine1();
                }
                
                @Override
                public String getCellStyleNames(Context context, LocationHistData data) {
                    return getCellStyleName(data.isAddressLine1Changed());
                }
            };
            
            TextColumn<LocationHistData> address2Column = new TextColumn<LocationHistData>() {
                @Override
                public String getValue(LocationHistData data) {
                    return data.getAddressLine2();
                }
                
                @Override
                public String getCellStyleNames(Context context, LocationHistData data) {
                    return getCellStyleName(data.isAddressLine2Changed());
                }
            };
            
            TextColumn<LocationHistData> address3Column = new TextColumn<LocationHistData>() {
                @Override
                public String getValue(LocationHistData data) {
                    return data.getAddressLine3();
                }
                
                @Override
                public String getCellStyleNames(Context context, LocationHistData data) {
                    return getCellStyleName(data.isAddressLine3Changed());
                }
            };
	         
            TextColumn<LocationHistData> erfColumn = new TextColumn<LocationHistData>() {
                @Override
                public String getValue(LocationHistData data) {
                    return data.getErfNumber();
                }
                
                @Override
                public String getCellStyleNames(Context context, LocationHistData data) {
                    return getCellStyleName(data.isErfNumberChanged());
                }
            };
            
            TextColumn<LocationHistData> latitudeColumn = new TextColumn<LocationHistData>() {
                @Override
                public String getValue(LocationHistData data) {
                    if (data.getLatitude() != null) {
                        return String.valueOf(data.getLatitude());
                    } else {
                        return "";
                    }
                }
                
                @Override
                public String getCellStyleNames(Context context, LocationHistData data) {
                    return getCellStyleName(data.isLatitudeChanged());
                }
            };
            
            TextColumn<LocationHistData> longitudeColumn = new TextColumn<LocationHistData>() {
                @Override
                public String getValue(LocationHistData data) {
                    if (data.getLongitude() != null) {
                        return String.valueOf(data.getLongitude());
                    } else {
                        return "";
                    }
                }
                
                @Override
                public String getCellStyleNames(Context context, LocationHistData data) {
                    return getCellStyleName(data.isLongitudeChanged());
                }
            };
            
            TextColumn<LocationHistData> locationGroupColumn = new TextColumn<LocationHistData>() {
                @Override
                public String getValue(LocationHistData data) {
                    return data.getLocationGroup();
                }
                
                @Override
                public String getCellStyleNames(Context context, LocationHistData data) {
                    return getCellStyleName(data.isLocGroupIdChanged());
                }
            };
           
            TextColumn<LocationHistData> streetnumColumn = new TextColumn<LocationHistData>() {
                @Override
                public String getValue(LocationHistData data) {
                    return data.getStreetNum();
                }
                
                @Override
                public String getCellStyleNames(Context context, LocationHistData data) {
                    return getCellStyleName(data.isStreetNumChanged());
                }
            };
            
            TextColumn<LocationHistData> buildingNameColumn = new TextColumn<LocationHistData>() {
                @Override
                public String getValue(LocationHistData data) {
                    return data.getBuildingName();
                }
                
                @Override
                public String getCellStyleNames(Context context, LocationHistData data) {
                    return getCellStyleName(data.isBuildingNameChanged());
                }
            };
            
            TextColumn<LocationHistData> suiteColumn = new TextColumn<LocationHistData>() {
                @Override
                public String getValue(LocationHistData data) {
                    return data.getSuiteNum();
                }
                
                @Override
                public String getCellStyleNames(Context context, LocationHistData data) {
                    return getCellStyleName(data.isSuiteNumChanged());
                }
            };
            
	        locationtblHistory.addColumn(dateModifiedColumn, MessagesUtil.getInstance().getMessage("location.history.date.mod.column"));
	        locationtblHistory.addColumn(modifiedbyColumn, MessagesUtil.getInstance().getMessage("location.history.user.by.column"));
	        locationtblHistory.addColumn(actionColumn, MessagesUtil.getInstance().getMessage("location.history.action.column"));
	        locationtblHistory.addColumn(statusColumn, MessagesUtil.getInstance().getMessage("location.history.status.column"));
	        locationtblHistory.addColumn(address1Column, MessagesUtil.getInstance().getMessage("location.history.address1.column"));
	        locationtblHistory.addColumn(address2Column, MessagesUtil.getInstance().getMessage("location.history.address2.column"));
	        locationtblHistory.addColumn(address3Column, MessagesUtil.getInstance().getMessage("location.history.address3.column"));
	        locationtblHistory.addColumn(erfColumn, MessagesUtil.getInstance().getMessage("location.history.erfnumber.column"));
	        locationtblHistory.addColumn(latitudeColumn, MessagesUtil.getInstance().getMessage("location.history.latitude.column"));
	        locationtblHistory.addColumn(longitudeColumn, MessagesUtil.getInstance().getMessage("location.history.longitude.column"));
	        locationtblHistory.addColumn(locationGroupColumn, MessagesUtil.getInstance().getMessage("location.history.group.column"));
	        locationtblHistory.addColumn(streetnumColumn, MessagesUtil.getInstance().getMessage("location.history.streetnum.column"));
            locationtblHistory.addColumn(buildingNameColumn, MessagesUtil.getInstance().getMessage("location.history.buildingname.column"));
            locationtblHistory.addColumn(suiteColumn, MessagesUtil.getInstance().getMessage("location.history.suitenum.column"));
            
	       
	        dataProvider.addDataDisplay(locationtblHistory);
	        smplpgrHistory.setDisplay(locationtblHistory);
    	 }
         dateFilter.setDataProvider(dataProvider);
    }
    
    private String getCellStyleName(boolean changed) {
        String style = "";
        if (changed) {
            style = "cellChanged ";
        }
        return style;
    }

    public void setLocationHistoryList(ArrayList<LocationHistData> thedata) {
        thehistorydata = thedata;
        dataProvider.setList(thehistorydata);
        if (columnSortHandler == null || columnSortHandler.getList() == null) {
            columnSortHandler = new ListHandler<LocationHistData>(dataProvider.getList());

            columnSortHandler.setComparator(dateModifiedColumn, new Comparator<LocationHistData>() {
                public int compare(LocationHistData o1, LocationHistData o2) {
                    if (o1 == o2) {
                        return 0;
                    }
                    if (o1 != null) {
                        return (o2 != null) ? o1.getDateRecModified().compareTo(o2.getDateRecModified()) : 1;
                    }
                    return -1;
                }
            });

            columnSortHandler.setComparator(modifiedbyColumn, new Comparator<LocationHistData>() {
                public int compare(LocationHistData o1, LocationHistData o2) {
                    if (o1 == o2) {
                        return 0;
                    }

                    // Compare the name columns.
                    if (o1 != null) {
                        return (o2 != null) ? o1.getUserRecEntered().compareTo(o2.getUserRecEntered()) : 1;
                    }
                    return -1;
                }
            });

            locationtblHistory.addColumnSortHandler(columnSortHandler);
            locationtblHistory.getColumnSortList().push(dateModifiedColumn);
            ColumnSortEvent.fire(locationtblHistory, locationtblHistory.getColumnSortList());
        } else {
            columnSortHandler.setList(dataProvider.getList());
            ColumnSortEvent.fire(locationtblHistory, locationtblHistory.getColumnSortList());
        }
    }

    @Override
    public boolean isValid(LocationHistData value, String filter) {
        boolean valid = false;
		if (filterDropdown.getValue(filterDropdown.getSelectedIndex()).equals(MessagesUtil.getInstance().getMessage("customer.date"))) {
            return dateFilter.isValid(value.getDateRecModified(), filter);
		} else if (filterDropdown.getValue(filterDropdown.getSelectedIndex()).equals(MessagesUtil.getInstance().getMessage("customer.user"))) {
    		valid = value.getUserRecEntered().toLowerCase().contains(filter);
    	} else {
    		valid = true;
    	} 
        return valid;
    }

    @UiHandler("txtbxfilter")
    void handleFilterChange(KeyUpEvent e) {
        if (txtbxfilter.getText().trim().isEmpty()) {
            dataProvider.resetFilter();
        } else {
            dataProvider.setFilter(txtbxfilter.getText());
        }
        dataProvider.setList(dataProvider.getList());
        columnSortHandler.setList(dataProvider.getList());
        smplpgrHistory.firstPage();
    }
    
    @UiHandler("filterDropdown")
    void handleFilterDropdownSelection(ChangeEvent changeEvent) {
        dataProvider.resetFilter();
        boolean isDateSelected = filterDropdown.getSelectedItemText()
                .equals(MessagesUtil.getInstance().getMessage("meter.date"));
        dateFilter.changeFilter(isDateSelected);
        txtbxfilter.setVisible(!isDateSelected);
    }
    
    public void setHeadingText(String headingText) {
        this.dataName.setText(headingText);
    }

    public void setHeadingDescription(String description) {
        this.dataDescription.setText(description);
    }
    
    
}
