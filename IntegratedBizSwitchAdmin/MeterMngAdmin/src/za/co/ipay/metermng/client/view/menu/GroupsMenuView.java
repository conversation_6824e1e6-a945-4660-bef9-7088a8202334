package za.co.ipay.metermng.client.view.menu;

import com.google.gwt.core.client.GWT;
import com.google.gwt.uibinder.client.UiBinder;
import com.google.gwt.uibinder.client.UiField;
import com.google.gwt.user.client.ui.FlowPanel;
import com.google.gwt.user.client.ui.Widget;

import za.co.ipay.metermng.client.factory.ClientFactory;
import za.co.ipay.metermng.client.rpc.callback.ClientCallback;
import za.co.ipay.metermng.client.view.component.BaseComponent;
import za.co.ipay.metermng.shared.MeterMngStatics;
import za.co.ipay.metermng.shared.dto.user.MeterMngUser;

public class GroupsMenuView extends BaseComponent {

    private static GroupsMenuViewUiBinder uiBinder = GWT.create(GroupsMenuViewUiBinder.class);
    @UiField FlowPanel usagepointGroupsLnk;
    @UiField FlowPanel groupTypeLnk;
    @UiField FlowPanel accessGroupLnk;
    @UiField FlowPanel userGroupLnk;
    @UiField FlowPanel locationGroups;
    @UiField FlowPanel metadataUploadLnk;

    @UiField FlowPanel groupsMenu;
    
    interface GroupsMenuViewUiBinder extends UiBinder<Widget, GroupsMenuView> {
    }

    public GroupsMenuView(ClientFactory clientFactory) {
        this.clientFactory = clientFactory;
        initWidget(uiBinder.createAndBindUi(this));
    }

    public void checkPermissions(MeterMngUser user) {
        // Group Types and Hierarchies
        if (!user.hasPermission(MeterMngStatics.ACCESS_PERMISSION_MM_GROUP_TYPE_ADMIN)) {
            groupTypeLnk.removeFromParent();
        }
        // Usage Point Groups
        if (!user.hasPermission(MeterMngStatics.ACCESS_PERMISSION_MM_GROUP_ADMIN)) {
            usagepointGroupsLnk.removeFromParent();
        } else {
            // If they use access-groups, Only global has access.
            if (clientFactory.isGroupGroupUser()) {
                usagepointGroupsLnk.removeFromParent();
            }
        }
        // Location Groups
        if (!user.hasPermission(MeterMngStatics.ACCESS_PERMISSION_MM_LOCATION_GROUP_ADMIN)) {
            locationGroups.removeFromParent();
        } else {
            // If they use access-groups, Only global has access.
            if (clientFactory.isGroupGroupUser()) {
                locationGroups.removeFromParent();
            }
        }
        // Access Groups
        if (clientFactory.isEnableAccessGroups() || !user.hasPermission(MeterMngStatics.ACCESS_PERMISSION_MM_ACCESS_GROUP_ADMIN)) {
            accessGroupLnk.removeFromParent();
        }
        // Access metadata upload
        if (!user.hasPermission(MeterMngStatics.ACCESS_PERMISSION_MM_METADATA_UPLOAD)) {
            metadataUploadLnk.removeFromParent();
        }
        // User's Access Group
        //RC.....this change for later when remove the clientfactory user ...!!
        if (clientFactory.isEnableAccessGroups() || !user.hasPermission(MeterMngStatics.ACCESS_PERMISSION_MM_USER_GROUP_ADMIN)) {
            userGroupLnk.removeFromParent();
        } else {
            clientFactory.getUserRpc().getCurrentUser(new ClientCallback<MeterMngUser>() {
                @Override
                public void onSuccess(MeterMngUser result) {
                    if (result.getAssignedGroup().getParentId() != null) {
                        userGroupLnk.removeFromParent();
                    }
                }
            });
        }
    }

    public boolean isGroupsMenuEmpty() {
        if (groupsMenu.getWidgetCount() < 1) {
            return true;   //IS empty
        } else {
            return false;  //has elements
        }
    }
}