package za.co.ipay.metermng.client.view.component.meter;

import java.util.ArrayList;
import java.util.logging.Logger;

import com.google.gwt.core.client.GWT;
import com.google.gwt.event.dom.client.ChangeEvent;
import com.google.gwt.event.dom.client.ClickEvent;
import com.google.gwt.uibinder.client.UiBinder;
import com.google.gwt.uibinder.client.UiField;
import com.google.gwt.uibinder.client.UiHandler;
import com.google.gwt.user.client.Window;
import com.google.gwt.user.client.Window.Location;
import com.google.gwt.user.client.ui.Button;
import com.google.gwt.user.client.ui.FlowPanel;
import com.google.gwt.user.client.ui.HTMLPanel;
import com.google.gwt.user.client.ui.Label;
import com.google.gwt.user.client.ui.ListBox;
import com.google.gwt.user.client.ui.Widget;

import za.co.ipay.gwt.common.client.dataexport.GetRequestBuilder;
import za.co.ipay.gwt.common.client.resource.Messages;
import za.co.ipay.gwt.common.client.resource.Messages.MessagesUtil;
import za.co.ipay.gwt.common.client.workspace.SessionCheckCallback;
import za.co.ipay.metermng.client.event.EngineeringTokenIssuedEvent;
import za.co.ipay.metermng.client.event.EngineeringTokenIssuedEventHandler;
import za.co.ipay.metermng.client.factory.ClientFactory;
import za.co.ipay.metermng.client.rpc.callback.ClientCallback;
import za.co.ipay.metermng.client.view.component.BaseComponent;
import za.co.ipay.metermng.client.view.workspace.UsagePointWorkspaceView;
import za.co.ipay.metermng.datatypes.ServiceResourceE;
import za.co.ipay.metermng.datatypes.StsEngineeringTokenTypeE;
import za.co.ipay.metermng.mybatis.generated.model.AppSetting;
import za.co.ipay.metermng.mybatis.generated.model.StsMeter;
import za.co.ipay.metermng.shared.StsEngineeringTokenData;
import za.co.ipay.metermng.shared.TokenData;
import za.co.ipay.metermng.shared.dto.MeterData;
import za.co.ipay.metermng.shared.dto.UsagePointData;
import za.co.ipay.metermng.shared.dto.user.MeterMngUser;

public class MeterInfoEngTokens extends BaseComponent {

    private static MeterInfoEngTokenTransUiBinder uiBinder = GWT.create(MeterInfoEngTokenTransUiBinder.class);

    interface MeterInfoEngTokenTransUiBinder extends UiBinder<Widget, MeterInfoEngTokens> {
    }

    @UiField(provided = true) EngineeringTokenTransactionsView engineeringTokenTransactions;

    @UiField Label formHeadingLabel;
    @UiField FlowPanel flwPanelNewEngineeringToken;
    @UiField ListBox tokenIssueListbox;
    @UiField Label engineeringheader;

    @UiField(provided=true) IssueNewEngineeringTokenPanel issueNewEngineeringTokenPanel;
    @UiField Button btnPrint;
    @UiField Button btnSaveToPdf;
    @UiField HTMLPanel pnlButtons;

    private ClientFactory clientFactory;
    private MeterData meterData;
    private Long usagepointid = null;
    private ArrayList<StsEngineeringTokenData> engineeringTokenTransactionList;

    private boolean viewConstructed = false;

    private final Logger logger = Logger.getLogger("MeterInfoEngTokens");

    public MeterInfoEngTokens(ClientFactory clientFactory, final UsagePointWorkspaceView usagePointWorkspaceView) {
        this.clientFactory = clientFactory;
        engineeringTokenTransactions = new EngineeringTokenTransactionsView(clientFactory);
        issueNewEngineeringTokenPanel = new IssueNewEngineeringTokenPanel(clientFactory);
        initWidget(uiBinder.createAndBindUi(this));

        //get AppSetting once for all the token panels
        clientFactory.getAppSettingRpc().getAppSettingByKey("engineering.token.issue.user.reference.status", new ClientCallback<AppSetting>() {
            @Override
            public void onSuccess(AppSetting result) {
                init(result, usagePointWorkspaceView);
            }
         });
    }

    protected void init(AppSetting tokenIssueRefStatusAppSetting, UsagePointWorkspaceView usagePointWorkspaceView) {

        checkUserPermissions(tokenIssueRefStatusAppSetting, usagePointWorkspaceView);

        clientFactory.getEventBus().addHandler(EngineeringTokenIssuedEvent.TYPE,
                new EngineeringTokenIssuedEventHandler() {
                    @Override
                    public void handleEngineeringTokenIssuedEvent(EngineeringTokenIssuedEvent event) {
                        if (event.getMeter() != null && event.getMeter().getId() != null && meterData != null
                                && meterData.getId() != null && event.getMeter().getId().equals(meterData.getId())) {
                            SessionCheckCallback sessionCheckCallback = new SessionCheckCallback() {
                                @Override
                                public void callback(SessionCheckResolution resolution) {
                                    populateEngineeringTokenTransactions();
                                    pnlButtons.setVisible(true);
                                }
                            };
                            clientFactory.handleSessionCheckCallback(sessionCheckCallback);
                        }
                    }
        });
        viewConstructed = true;
    }

    private void checkUserPermissions(AppSetting tokenIssueRefStatusAppSetting, UsagePointWorkspaceView upwView) {

        flwPanelNewEngineeringToken.setVisible(true);
        UsagePointData usagePointData = null;
        if (upwView != null) {
            usagePointData = upwView.getUsagePoint();
        }
        boolean groupHasGlobal = UsagePointWorkspaceView.hasGlobalContractElement(usagePointData, clientFactory.isGroupGroupUser());
        if (clientFactory.isEnableAccessGroups()) {
            if (groupHasGlobal) {
                flwPanelNewEngineeringToken.setVisible(false);
            }
            if (clientFactory.isGroupGlobalUser()) {
                flwPanelNewEngineeringToken.removeFromParent();
            }
        }

        if (clientFactory.getUser().hasPermission("mm_gen_sts_meter_spec")) {
            issueNewEngineeringTokenPanel.initEngineeringTokenUserRefPanel(tokenIssueRefStatusAppSetting);
            issueNewEngineeringTokenPanel.setVisible(false);
        } else {
            flwPanelNewEngineeringToken.removeFromParent();
        }
        // TODO RC planio 6237 & 6955 - comment out EMERGENCY VEND for now. Has to do a
        // pseudoVEnd and not the full vend process first!
        //        if (clientFactory.getUser().hasPermission("mm_gen_sts_credit_token")) {
        //            tokenIssueListbox.addItem(MessagesUtil.getInstance().getMessage("meter.freeissue.currency"));
        //        }
    }

    public boolean isViewConstructed() {
        return viewConstructed;
    }

    @UiHandler("tokenIssueListbox")
    void handleTokenIssueTypeSelection(ChangeEvent event) {
        clearFields();
        String selected = tokenIssueListbox.getValue(tokenIssueListbox.getSelectedIndex());
        engineeringheader.setText(selected);
        boolean hasSelection = !selected.trim().equals("");
        engineeringheader.setVisible(hasSelection);
        issueNewEngineeringTokenPanel.setVisible(hasSelection);
        Messages messagesInstance = MessagesUtil.getInstance();
        if (selected.equals(messagesInstance.getMessage("meter.cleartamper"))) {
            issueNewEngineeringTokenPanel.setTokenType(StsEngineeringTokenTypeE.CLEAR_TAMPER);
        } else if (selected.equals(messagesInstance.getMessage("meter.clearcredit"))) {
            issueNewEngineeringTokenPanel.setTokenType(StsEngineeringTokenTypeE.CLEAR_CREDIT);
        } else if (selected.equals(messagesInstance.getMessage("meter.clearreverseflag"))) {
            issueNewEngineeringTokenPanel.setTokenType(StsEngineeringTokenTypeE.CLEAR_REVERSE_FLAG);
        } else if (selected.equals(messagesInstance.getMessage("meter.disabletriplimit"))) {
            issueNewEngineeringTokenPanel.setTokenType(StsEngineeringTokenTypeE.DISABLE_TRIP_LIMIT);
        } else if (selected.equals(messagesInstance.getMessage("meter.setphase"))) {
            issueNewEngineeringTokenPanel.setTokenType(StsEngineeringTokenTypeE.SET_PHASE);
        } else if (selected.equals(messagesInstance.getMessage("meter.changekey"))) {
            issueNewEngineeringTokenPanel.setTokenType(StsEngineeringTokenTypeE.KEY_CHANGE);
        } else if (selected.equals(messagesInstance.getMessage("meter.freeissue.units"))) {
            issueNewEngineeringTokenPanel.setTokenType(StsEngineeringTokenTypeE.FREE_ISSUE);
            issueNewEngineeringTokenPanel.setFreeIssueType(TokenData.TOKEN_TYPE_FREE_ISSUE_UNITS);
            issueNewEngineeringTokenPanel.updateUnitTypes(meterData.getMeterModelData().getServiceResourceId());
        } else if (selected.equals(messagesInstance.getMessage("meter.powerlimit")) || selected.equals(messagesInstance.getMessage("meter.setcurrentlimit"))) {
            issueNewEngineeringTokenPanel.setTokenType(StsEngineeringTokenTypeE.POWER_LIMIT);
        }
    }

    protected void populateEngineeringTokenTransactions() {
        if (meterData != null) {
            engineeringTokenTransactions.setVisible(true);
            SessionCheckCallback sessionCheckCallback = new SessionCheckCallback() {
                @Override
                public void callback(SessionCheckResolution resolution) {
                    clientFactory.getMeterRpc().getEngineeringTokenTransactions(meterData.getId(), clientFactory.isEnableAccessGroups(),
                            new ClientCallback<ArrayList<StsEngineeringTokenData>>() {
                                @Override
                                public void onSuccess(ArrayList<StsEngineeringTokenData> result) {
                                    if (result != null) {
                                        engineeringTokenTransactionList = result;
                                        engineeringTokenTransactions.setMeterTransactionList(engineeringTokenTransactionList);
                                    }
                                }
                            });
                }
            };
            clientFactory.handleSessionCheckCallback(sessionCheckCallback);
        } else {
            engineeringTokenTransactions.setVisible(false);
        }
    }


    public void setMeterInfo(MeterData meterData, Long usagePointId) {
        this.usagepointid = usagePointId;
        if (meterData != null) {
            this.meterData = meterData;
            MeterMngUser meterMngUser = clientFactory.getUser();
            boolean isElectricityMeter = ServiceResourceE.ELEC.getId() == meterData.getMeterModelData().getServiceResourceId();
            StsMeter stsMeter = meterData.getStsMeter();
            String selectedToken = tokenIssueListbox.getSelectedValue();
            if (meterMngUser.hasPermission("mm_gen_sts_meter_spec")) {
                tokenIssueListbox.clear();
                tokenIssueListbox.addItem("");
                Messages messagesInstance = MessagesUtil.getInstance();
                boolean isProprietaryMeter = false;
                if (stsMeter != null && stsMeter.getStsAlgorithmCode().equals("89")) {
                    isProprietaryMeter = true;
                }
                if (meterMngUser.hasPermission("mm_aux_free_issue") && !isProprietaryMeter) {
                    tokenIssueListbox.addItem(messagesInstance.getMessage("meter.freeissue.units"));
                }
                if (meterMngUser.hasPermission("mm_gen_clear_tamper")) {
                    tokenIssueListbox.addItem(messagesInstance.getMessage("meter.cleartamper"));
                }
                tokenIssueListbox.addItem(messagesInstance.getMessage("meter.clearcredit"));
                if (isProprietaryMeter) {
                    tokenIssueListbox.addItem(messagesInstance.getMessage("meter.clearreverseflag"));
                    if (isElectricityMeter) {
                        tokenIssueListbox.addItem(messagesInstance.getMessage("meter.setcurrentlimit"));
                    }
                    tokenIssueListbox.addItem(messagesInstance.getMessage("meter.disabletriplimit"));
                } else {
                    tokenIssueListbox.addItem(messagesInstance.getMessage("meter.changekey"));
                    if (isElectricityMeter) {
                        if (meterMngUser.hasPermission("mm_gen_power_limit")) {
                            tokenIssueListbox.addItem(messagesInstance.getMessage("meter.powerlimit"));
                        }
                        if (meterMngUser.hasPermission("mm_gen_set_max_phase")) {
                            tokenIssueListbox.addItem(messagesInstance.getMessage("meter.setphase"));
                        }
                    }
                }

                if (clientFactory.isEnableSTS() && stsMeter != null) {
                    issueNewEngineeringTokenPanel.setStsMeter(stsMeter);
                    issueNewEngineeringTokenPanel.setUsagePointId(usagePointId);
                    if (usagepointid == null) {
                        for (int i = 0; i < tokenIssueListbox.getItemCount(); i++) {
                            if (tokenIssueListbox.getItemText(i)
                                    .equals(messagesInstance.getMessage("meter.freeissue.currency"))) {
                                tokenIssueListbox.removeItem(i);
                                break;
                            }
                        }
                    }
                }
            }
            if (selectedToken != null) {
                for (int i = 0; i < tokenIssueListbox.getItemCount(); i++) {
                    if (tokenIssueListbox.getValue(i).equals(selectedToken)) {
                        tokenIssueListbox.setSelectedIndex(i);
                        break;
                    }
                }
            }
            if (clientFactory.isEnableSTS() && stsMeter != null) {
                if (!isElectricityMeter) {
                    engineeringTokenTransactions.removePowerLimitColumn();
                }
            } else {
                engineeringTokenTransactions.setVisible(false);
            }
            populateEngineeringTokenTransactions();
        }
    }

    public void setFreeIssueAuxAccountId(Long freeIssueAuxAccountId) {
        issueNewEngineeringTokenPanel.setFreeIssueAuxAccountId(freeIssueAuxAccountId);
    }

    public void setCustomerAgreementId(Long custAgreementId) {
        issueNewEngineeringTokenPanel.setCustomerAgreementId(custAgreementId);
    }

    private void clearFields() {
        issueNewEngineeringTokenPanel.clearFields();
        pnlButtons.setVisible(false);
    }

    public void updateEngineeringTokenUserRefStatusExNotify() {
      //get AppSetting once for all the token panels
        clientFactory.getAppSettingRpc().getAppSettingByKey("engineering.token.issue.user.reference.status", new ClientCallback<AppSetting>() {
            @Override
            public void onSuccess(AppSetting result) {
                issueNewEngineeringTokenPanel.getEngineeringTokenUserRefPanel().updateEngineeringTokenUserRefStatus(result);
                engineeringTokenTransactions.updateUserReferenceColumnVisibility();
            }
         });
    }

    @UiHandler("btnPrint")
    void handlePrintReceipt(ClickEvent e) {
        generatePdf("print");
    }

    @UiHandler("btnSaveToPdf")
    void handleSaveReceiptPdf(ClickEvent e) {
        generatePdf("save");
    }

    private void generatePdf(String action) {
        GetRequestBuilder url = new GetRequestBuilder().withBaseUrl(GWT.getHostPageBaseURL())
                .withTargetUrl("reprintreceiptpdf")
                .addParam("action", action)
                .addParam("locale", clientFactory.getLocaleName())
                .addParam("stsengineeringtokenid", engineeringTokenTransactionList.get(0).getId().toString())
                .addParam("meterid", meterData.getId().toString())
                .addParam("usingaccessgroups", "" + clientFactory.isEnableAccessGroups());
        if ("print".equals(action)) {
            Window.open(url.toEncodedUrl(), "_blank", "");
        } else {
            Location.assign(url.toEncodedUrl());
        }
    }
}
