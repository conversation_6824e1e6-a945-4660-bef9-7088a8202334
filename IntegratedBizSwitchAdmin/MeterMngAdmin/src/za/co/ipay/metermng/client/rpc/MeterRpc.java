package za.co.ipay.metermng.client.rpc;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;

import com.google.gwt.user.client.rpc.RemoteService;
import com.google.gwt.user.client.rpc.RemoteServiceRelativePath;

import za.co.ipay.gwt.common.shared.exception.ServiceException;
import za.co.ipay.gwt.common.shared.exception.ValidationException;
import za.co.ipay.metermng.mybatis.custom.model.CustomerTransAlphaData;
import za.co.ipay.metermng.mybatis.custom.model.CustomerTransAlphaDataWithTotals;
import za.co.ipay.metermng.mybatis.custom.model.MeterDto;
import za.co.ipay.metermng.mybatis.custom.model.RegisterReadingExt;
import za.co.ipay.metermng.mybatis.generated.model.CustomerTrans;
import za.co.ipay.metermng.mybatis.generated.model.CustomerTransExtra;
import za.co.ipay.metermng.mybatis.generated.model.CustomerTransItem;
import za.co.ipay.metermng.mybatis.generated.model.Meter;
import za.co.ipay.metermng.mybatis.generated.model.MeterReadingType;
import za.co.ipay.metermng.mybatis.generated.model.SpecialActionReasonsLog;
import za.co.ipay.metermng.mybatis.generated.model.StsEngineeringToken;
import za.co.ipay.metermng.mybatis.generated.model.StsMeter;
import za.co.ipay.metermng.shared.CustomerTransItemData;
import za.co.ipay.metermng.shared.IpayResponseData;
import za.co.ipay.metermng.shared.MdcTransData;
import za.co.ipay.metermng.shared.MeterHistData;
import za.co.ipay.metermng.shared.STSMeterHistData;
import za.co.ipay.metermng.shared.StsEngineeringTokenData;
import za.co.ipay.metermng.shared.dto.MeterData;
import za.co.ipay.metermng.shared.dto.StsMeterData;
import za.co.ipay.metermng.shared.dto.dashboard.MeterCountDto;
import za.co.ipay.metermng.shared.dto.meter.EnergyBalancingDto;
import za.co.ipay.metermng.shared.dto.meter.MeterReadingDto;
import za.co.ipay.metermng.shared.dto.meter.MeterReadingVariation;
import za.co.ipay.metermng.shared.dto.meter.MeterReadingsDto;
import za.co.ipay.metermng.shared.dto.meter.SuperSubMeterDto;
import za.co.ipay.metermng.shared.dto.meter.VerifyTokenDto;
import za.co.ipay.metermng.shared.dto.schedule.EnergyBalancingExportScreenData;
import za.co.ipay.metermng.shared.dto.usagepoint.MeterUpMdcChannelInfo;

@RemoteServiceRelativePath("secure/meter.do")
public interface MeterRpc extends RemoteService {

    MeterData updateMeter(MeterData meterData) throws ValidationException, ServiceException;

    MeterData getMeter(Long meterId) throws ServiceException;

    MeterDto getMeterByMeterNumber(String meterNumber) throws ServiceException;

    ArrayList<STSMeterHistData> getStsMeterHistory(Long meterId, boolean usingAccessGroup) throws ServiceException;

    ArrayList<MeterHistData> getMeterHistory(Long meterId, boolean usingAccessGroup) throws ServiceException;

    ArrayList<CustomerTransAlphaData> getTransactionHistory(Long meterId) throws ServiceException;

    ArrayList<CustomerTransAlphaDataWithTotals> getTransactionHistoryWithTotals(Long meterId)throws ServiceException;

    ArrayList<CustomerTransAlphaData> getVendTransactionHistory(Long meterId) throws ServiceException;
    
    List<CustomerTransItemData> getTransactionCustomerTransItem(CustomerTransAlphaDataWithTotals customerTransData) throws ServiceException;

    List<CustomerTransItemData> getTransactionCustomerTransItem(Long customerTransId) throws ServiceException;

    int getTransactionCount(Long meterId) throws ServiceException;

    ArrayList<StsEngineeringTokenData> getEngineeringTokenTransactions(Long meterId, boolean usingAccessGroup) throws ServiceException;

    ArrayList<MeterData> getMetersByEndDeviceStore(Long endDeviceStoreId) throws ServiceException;

    ArrayList<MeterReadingType> getMeterReadingTypes() throws ServiceException;

    ArrayList<MeterDto> getSuperMeters() throws ServiceException;

    SuperSubMeterDto getSubMeters(Long superMeterId) throws ServiceException;

    Void saveSuperSubMeters(Long superMeterId, ArrayList<Long> subMeterIds) throws ValidationException,
            ServiceException;
    
    Void deleteSuperMeter(Long superMeterId) throws ValidationException, ServiceException;

    MeterReadingsDto getMeterReadings(Long meterId, Long meterReadingTypeId, Date start, Date end)
            throws ServiceException;

    MeterReadingsDto getMeterBalancingReadings(Long balancingMeterId, Long meterReadingTypeId, Date start, Date end)
            throws ServiceException;

    ArrayList<EnergyBalancingDto> checkEnergyBalancingMeters(Long meterReadingTypeId, Date startDate, Date endDate,
            double percentVariation) throws ServiceException;

    IpayResponseData addMeterReadings(MeterDto meterDto, Date start, Date end, int intervalMinutes,
            ArrayList<Long> readingTypeIds, int deleteExistingReadings, boolean doTariffCalc, Date zeroStart,
            Date zeroEnd, int zeroInstances, Date missingStart, Date missingEnd, int missingInstances,
            ArrayList<Long> mdcChannelIds) throws ValidationException, ServiceException;

    public void addSuperMeterReadings(Long superMeterId, Date start, Date end, int intervalMinutes, Long readingTypeId,
            boolean deleteExistingSuperMeterReadings, boolean regenerateSubMeterReadings,
            List<MeterReadingVariation> variations) throws ValidationException, ServiceException;

    EnergyBalancingExportScreenData getEnergyBalancingExportScreenData() throws ValidationException, ServiceException;

    String getNewMrid();

    ArrayList<MeterCountDto> getMeterCountByModel() throws ValidationException, ServiceException;
    
    ArrayList<MdcTransData> getMdcTransByMeterId(Long meterId) throws ServiceException;
    
    List<RegisterReadingExt> getRegisterReadingsByMeterId(Long meterId, Date fromDate, Date toDate) throws ServiceException;
    
    List<RegisterReadingExt> getRegisterReadingsByUsagePointId(Long usagePointId, Date fromDate, Date toDate) throws ServiceException;

    IpayResponseData sendConnectDisconnectMsg(MeterData meterData, String messageType, String overrideType, String relayId) throws Exception;
    
    IpayResponseData sendPowerLimitMsg(MeterData meterData, String overrideType, String powerLimit) throws Exception;
    
    Long selectLastCustAgrTransId(Long customerAgreementId) throws Exception;
    
    IpayResponseData sendVendReversalMsg(CustomerTrans customerTrans, boolean allowOlderReversals, String userName,
            SpecialActionReasonsLog specialActionReasonsLog, String comment) throws Exception;
    
    Void updateCustTransLastReprintDate(Long customerTransId, Date reprintDate, String userName) throws Exception;
    
    Boolean getCustTransIsReversed(Long customerTransId) throws Exception;
    
    List<CustomerTransExtra> getCustTransExtraListOrderByReprintDateAsc(Long customerTransId) throws Exception;
    
    List<CustomerTransItem> getCustomerTransItemsForMeterId(Long meterId) throws Exception;
    
    MeterReadingDto getReadingsDateRangeForMeter(MeterDto meterDto, ArrayList<Long> readingTypeIds,
            boolean intervalReadings);

    Void updateMeterPowerLimit(Meter meter, StsMeter stsMeter, BigDecimal powerLimit, String powerLimitLabel);
    
    Void saveInitRegReadings(MeterUpMdcChannelInfo meterUpMdcChannelInfo) throws Exception;

    ArrayList<StsEngineeringTokenData> getStdBsstEngineeringTokens(Long customerTransId, String MeterNumber) throws ServiceException;

    VerifyTokenDto verifyToken(String token, String meterNumber, StsMeterData stsMeterData) throws ServiceException;

    StsEngineeringToken getKeyChangeTokensForCustomerTrans(long customerTransId);

    StsEngineeringToken getFirstEngineeringTokenByCustomerTrans(long customerTransId);
    
	Void updateCustTransLastReprintDateBulk(List<Long> customerTransIdsToUpdate, Date now, String userName);
}
