package za.co.ipay.metermng.client.rpc;

import java.util.ArrayList;
import java.util.Date;

import com.google.gwt.user.client.rpc.AsyncCallback;

import za.co.ipay.gwt.common.shared.LookupListItem;
import za.co.ipay.gwt.common.shared.dto.IdNameDto;
import za.co.ipay.metermng.shared.dto.PtrScreenDataDto;
import za.co.ipay.metermng.shared.dto.meter.AddMeterReadingScreenDataDto;
import za.co.ipay.metermng.shared.dto.meter.AddSuperMeterReadingScreenDataDto;
import za.co.ipay.metermng.shared.dto.usagepoint.MeterUpMdcChannelInfo;

public interface LookupRpcAsync {

    void getPricingStructureLookupList(Long serviceResourceId, Long meterTypeId, ArrayList<Long> paymentModeIds, boolean hasTariffCurrentlyRunning, AsyncCallback<ArrayList<LookupListItem>> callback);

    void getAvailablePricingStructuresByMeterModelId(Long meterModelId, boolean hasTariffCurrentlyRunning, AsyncCallback<ArrayList<LookupListItem>> callback);
    
    void getPricingStructureLookupList(boolean hasTariffCurrentlyRunning, AsyncCallback<ArrayList<LookupListItem>> callback);

    void getAlgCodeLookupList(AsyncCallback<ArrayList<LookupListItem>> callback);

    void getTtCodeLookupList(AsyncCallback<ArrayList<LookupListItem>> callback);

    void getSgKrnLookupList(AsyncCallback<ArrayList<LookupListItem>> callback);
    void getSgKrnLookupList(boolean excludeExpired, AsyncCallback<ArrayList<LookupListItem>> callback);

    void getAuxTypeLookupList(AsyncCallback<ArrayList<LookupListItem>> callback);

    void getAuxChargeScheduleLookupList(AsyncCallback<ArrayList<LookupListItem>> callback);

    void getAuxAccountsLookupList(Long customerId, AsyncCallback<ArrayList<LookupListItem>> callback);

    void getEndDeviceStoresLookupList(AsyncCallback<ArrayList<LookupListItem>> callback);

    void getMeterTypeLookupList(AsyncCallback<ArrayList<LookupListItem>> lookupSvcAsyncCallback);

    void getTouSeasonLookupList(AsyncCallback<ArrayList<LookupListItem>> callback);

    void getPtrScreenData(AsyncCallback<PtrScreenDataDto> callback);

    void getMeterModelLookupList(AsyncCallback<ArrayList<LookupListItem>> callback);

    void getMeterModelByPricingStructureIdLookupList(Long pricingStructureId, AsyncCallback<ArrayList<LookupListItem>> lookupSvcAsyncCallback);

    void getAddMeterReadingScreenData(AsyncCallback<AddMeterReadingScreenDataDto> callback);

    void getAddSuperMeterReadingScreenData(AsyncCallback<AddSuperMeterReadingScreenDataDto> callback);
    
    void getPaymentModes(AsyncCallback<ArrayList<IdNameDto>> callback);
    
    void getMeterUpMdcChannelInfo(MeterUpMdcChannelInfo meterUpMdcChannelInfo, AsyncCallback<MeterUpMdcChannelInfo> callback);
    
    void getPricingStructureLookupListFromMeterModel(Long meterModelId, boolean hasTariffCurrentlyRunning, AsyncCallback<ArrayList<LookupListItem>> lookupSvcAsyncCallback);
    
    void getBlockingTypeLookupList(AsyncCallback<ArrayList<LookupListItem>> callback);

    void getPricingStructureLookupListForStartDate(Date startDate, AsyncCallback<ArrayList<LookupListItem>> callback);
    
    void getDeviceStoreLookupListForAccessGroup(Long accessGroupId, AsyncCallback<ArrayList<LookupListItem>> callback);
}
