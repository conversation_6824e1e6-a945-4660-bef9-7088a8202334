package za.co.ipay.metermng.client.view.component.meter;

import java.util.HashMap;

import za.co.ipay.metermng.client.rpc.callback.ClientCallback;

import com.google.gwt.user.client.ui.MultiWordSuggestOracle;
import com.google.gwt.user.client.ui.SuggestOracle;

public class PowerLimitSuggestionsOracle extends MultiWordSuggestOracle {

    private HashMap<String, String> limitslist = new HashMap<String, String>();
    
    public PowerLimitSuggestionsOracle() {
        super();
    }

    public boolean isDisplayStringHTML() {
        return true; 
    }

    public void add(String displayString, String replaceString) {
        limitslist.put(displayString, replaceString);
        super.add(displayString);
    }
    
    public String getReplaceString(String displayString) {
        return limitslist.get(displayString);
    }
    
    @Override
    public void requestSuggestions(Request request, Callback callback) {
        super.requestDefaultSuggestions(request, callback);
    }
    
    public class ItemRequestCallback extends ClientCallback<Response>{
        private SuggestOracle.Request req; 
        private SuggestOracle.Callback callback; 

        public void onFailure(Throwable caught) {
            callback.onSuggestionsReady(req,new SuggestOracle.Response()); caught.printStackTrace();
        }

        public void onSuccess(Response result) {
            callback.onSuggestionsReady(req, result);
        }

        public ItemRequestCallback(SuggestOracle.Request _req,SuggestOracle.Callback _callback) {
            this.req=_req; this.callback=_callback;
        }
    }
    
}
