package za.co.ipay.metermng.client.rpc;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;

import com.google.gwt.user.client.rpc.AsyncCallback;

import za.co.ipay.gwt.common.shared.exception.ServiceException;
import za.co.ipay.metermng.mybatis.custom.model.CustomerTransAlphaData;
import za.co.ipay.metermng.mybatis.custom.model.CustomerTransAlphaDataWithTotals;
import za.co.ipay.metermng.mybatis.generated.model.SpecialActionReasonsLog;
import za.co.ipay.metermng.mybatis.generated.model.UnitsTrans;
import za.co.ipay.metermng.mybatis.generated.model.UsagePoint;
import za.co.ipay.metermng.shared.CustomerTransItemOutstandCharges;
import za.co.ipay.metermng.shared.CustomerUsagePointMiscInfo;
import za.co.ipay.metermng.shared.IpayResponseData;
import za.co.ipay.metermng.shared.MdcTransData;
import za.co.ipay.metermng.shared.UsagePointHistData;
import za.co.ipay.metermng.shared.dto.LocationData;
import za.co.ipay.metermng.shared.dto.MdcChannelMatchDto;
import za.co.ipay.metermng.shared.dto.MdcChannelReadingsDto;
import za.co.ipay.metermng.shared.dto.MeterData;
import za.co.ipay.metermng.shared.dto.UpMeterInstallHistData;
import za.co.ipay.metermng.shared.dto.UpPricingStructureData;
import za.co.ipay.metermng.shared.dto.UpPricingStructureHistData;
import za.co.ipay.metermng.shared.dto.UsagePointData;
import za.co.ipay.metermng.shared.dto.meter.MdcChannelDto;

public interface UsagePointRpcAsync {

	void updateUsagePointComponent(UsagePointData usagePointData, LocationData serviceLocation, LocationData usagePointLocation, 
	                                List<MdcChannelReadingsDto> channelReadingsList, AsyncCallback<UsagePointData> usagePointSvcAsyncCallback);

    void updateUsagePointComponent(UsagePointData usagePointData, LocationData serviceLocation, LocationData usagePointLocation, 
                                    AsyncCallback<UsagePointData> usagePointSvcAsyncCallback);
	
	void removeCustomerFromUsagePoint(UsagePointData usagePointData, AsyncCallback<Void> callback);

	void assignCustomerToUsagePoint(Long usagePointId,Long customerAgreementId, AsyncCallback<UsagePoint> callback);
	
	void validateInstallationDate(Long usagePointId, Long oldMeterId, String meterNum, Date newInstallDate, AsyncCallback<ServiceException> callback);
	
	void reAssignMeterToUsagePoint(UsagePointData usagePoint, MeterData meter, List<MdcChannelReadingsDto> channelReadingsList, AsyncCallback<UsagePoint> callback);
	
	void removeMeterFromUsagePoint(UsagePointData usagePointData, AsyncCallback<Void> callback);

	void fetchUsagePointHistory(Long usagePointId,AsyncCallback<ArrayList<UsagePointHistData>> callback);

	void fetchTransactionHistory(Long usagePointId, AsyncCallback<ArrayList<CustomerTransAlphaData>> callback);

    void getAutoGeneratedRef(AsyncCallback<String> callback);
    
    void getMdcTransByUsagePoint(Long usagePointId, AsyncCallback<ArrayList<MdcTransData>> callback);
    
    void checkIfInUse(Long pricingStructureId, AsyncCallback<Boolean> callback);
    
    void isValidAccessGroupUpdate(Long usagePointId, Long customerAgreementId, Long accessGroupId, AsyncCallback<Boolean> callback);
    
    void fetchUsagePointsByCustomerAgreementId(Long customerAgreementId, AsyncCallback<ArrayList<UsagePoint> > callback);
    
    void getUsagePointByName(String usagePointName, AsyncCallback<UsagePoint> callback);
    
    void getCustomerTransItemFromCyclicCharges(UsagePointData usagePointData, Date date, AsyncCallback<CustomerTransItemOutstandCharges> callback);
    
    void writeoffOutstandingCyclicCharges(Long usagePointId, Date upLastCyclicChargeDate, Date upLastBillingCyclicChargeDate, Date upNewCyclicChargeDate, String userRecEntered, CustomerTransItemOutstandCharges outstandCharges, SpecialActionReasonsLog logEntry, AsyncCallback<Void> callback);
    
    void sendMeterInspectionRequestMsg(UsagePointData usagePointData, String comment, AsyncCallback<IpayResponseData> callback);

    void fetchTransactionHistoryWithTotals(Long usagepointId, AsyncCallback<ArrayList<CustomerTransAlphaDataWithTotals>> callback);
    
    void countUpWithMdcUsingBillingDetId(Long billingDetId, String userName, AsyncCallback<List<Integer>> callback);
    
    void countUpWithMdc(Long mdcId, String userName, AsyncCallback<MdcChannelMatchDto> callback);
    
    void getMdcChannelCompatibility(Long mdcId, String userName, AsyncCallback<MdcChannelMatchDto> callback);
    
    void getMdcChannelUpdateCompatibility(Long mdcId, MdcChannelDto updatedMdcChannel, String userName, AsyncCallback<MdcChannelMatchDto> callback);
    
    void getMeterModelChannelCompatibility(Long meterModelId, Long newMdcId, String userName, AsyncCallback<MdcChannelMatchDto> callback);

    void getUnitsAccountTransactions(Long unitsAccountId, AsyncCallback<ArrayList<UnitsTrans>> callback);

    void inputUnitsAccountAdjustment(UnitsTrans unitsTrans, AsyncCallback<BigDecimal> callback) throws ServiceException;
    
    void sendSyncUnitsAccountBalance(MeterData meterData, Long unitsAccountId,AsyncCallback<IpayResponseData> callback);
    
    void getUnitsTransactionFromCustomerTransId(Long customerTransId, AsyncCallback<UnitsTrans> callback);
    
    void getAllUPPricingStructures(Long usagePointId, boolean usingAccessGroup, AsyncCallback<List<UpPricingStructureData>> callback);

    void deleteUpPricingStructure(Long upPricingStructureId, Long usagePointId, AsyncCallback<Boolean> callback) throws ServiceException;
    
    void fetchUpMeterInstallHistory(Long usagePointId, Long meterId, boolean usingAccessGroup,
            AsyncCallback<ArrayList<UpMeterInstallHistData>> callback);
    
    void fetchUpPricingStructureHistory(Long usagePointId, boolean usingAccessGroup, AsyncCallback<List<UpPricingStructureHistData>> callback);

    void getLatestCustomerUsagePointMiscInfoByUsagePointId(long usagePointId, Date date,
            AsyncCallback<CustomerUsagePointMiscInfo> callback);
}
