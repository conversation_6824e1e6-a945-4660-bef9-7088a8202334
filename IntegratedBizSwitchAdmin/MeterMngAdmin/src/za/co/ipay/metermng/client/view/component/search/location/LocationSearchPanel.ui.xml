<!DOCTYPE ui:UiBinder SYSTEM "http://dl.google.com/gwt/DTD/xhtml.ent">
<ui:UiBinder xmlns:ui="urn:ui:com.google.gwt.uibinder" xmlns:g="urn:import:com.google.gwt.user.client.ui"
    xmlns:p3="urn:import:za.co.ipay.gwt.common.client.form" xmlns:t="urn:import:za.co.ipay.metermng.client.view.component">
    <ui:style>

    </ui:style>

    <ui:with field="msg" type="za.co.ipay.metermng.client.i18n.UiMessages" />

    <g:FlowPanel>

        <p3:FormRowPanel>
            <p3:FormElement ui:field="searchLocationTypeElement" labelText="{msg.getLocationSearchTypeHeader}:">
                <g:RadioButton ui:field="customerCheckBox" name="custUpLocationSearchTypeBox" value="true" enabled="true" text="{msg.getLocationSearchTypeCustomer}" debugId="customerCheckBox"/>
                <g:RadioButton ui:field="upCheckBox" name="custUpLocationSearchTypeBox" value="false" enabled="true" text="{msg.getLocationSearchTypeUsagePoint}" debugId="upCheckBox"/>
            </p3:FormElement>
        </p3:FormRowPanel>

        <p3:FormRowPanel>
            <p3:FormElement ui:field="erfNumberElement" labelText="{msg.getSearchLocationErfNumber}:">
                <g:TextBox ui:field="erfNumberBox" maxLength="255" visibleLength="25" debugId="erfNumberBox"/>
            </p3:FormElement>
        </p3:FormRowPanel>

        <p3:FormRowPanel>
            <p3:FormElement ui:field="buildingNameElement" labelText="{msg.getSearchLocationBuildingName}:">
                <g:TextBox ui:field="buildingNameBox" maxLength="255" visibleLength="25" debugId="buildingNameBox"/>
            </p3:FormElement>
        </p3:FormRowPanel>

        <p3:FormRowPanel>
            <p3:FormElement ui:field="suiteNumberElement" labelText="{msg.getSearchLocationSuitNumber}:">
                <g:TextBox ui:field="suiteNumberBox" maxLength="255" visibleLength="25" debugId="suiteNumberBox"/>
            </p3:FormElement>
        </p3:FormRowPanel>

        <p3:FormRowPanel>
            <p3:FormElement ui:field="address1Element" labelText="{msg.getSearchLocationAddress1}:">
                <g:TextBox ui:field="address1Box" maxLength="255" visibleLength="25" debugId="address1Box"/>
            </p3:FormElement>
        </p3:FormRowPanel>

        <p3:FormRowPanel>
            <p3:FormElement ui:field="address2Element" labelText="{msg.getSearchLocationAddress2}:">
                <g:TextBox ui:field="address2Box" maxLength="255" visibleLength="25" debugId="address2Box"/>
            </p3:FormElement>
        </p3:FormRowPanel>

        <p3:FormRowPanel>
            <p3:FormElement ui:field="address3Element" labelText="{msg.getSearchLocationAddress3}:">
                <g:TextBox ui:field="address3Box" maxLength="255" visibleLength="25" debugId="address3Box"/>
            </p3:FormElement>
        </p3:FormRowPanel>

        <p3:FormRowPanel>
        	<p3:FormElement ui:field="locationGroupElement">
    			<t:LocationGroupComponent ui:field="locationGroupComponent"/>
			</p3:FormElement>
        </p3:FormRowPanel>

        <p3:FormRowPanel>
          <p3:FormElement ui:field="searchTypeElement" labelText="{msg.getLocationSearchType}:">
                 <g:RadioButton ui:field="startWithBox" name="locationSearchTypeBox" value="true" enabled="true" text="{msg.getSearchStartsWith}" debugId="locationStartWithBox"/>
                 <g:RadioButton ui:field="containsBox" name="locationSearchTypeBox" value="false" enabled="true" text="{msg.getSearchContains}" debugId="locationContainsBox"/>
            </p3:FormElement>
        </p3:FormRowPanel>

    </g:FlowPanel>

</ui:UiBinder> 