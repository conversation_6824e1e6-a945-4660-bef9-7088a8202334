package za.co.ipay.metermng.client.view.component.tariff;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.logging.Level;
import java.util.logging.Logger;

import com.google.gwt.event.dom.client.ClickEvent;
import com.google.gwt.event.dom.client.ClickHandler;
import com.google.gwt.i18n.client.DateTimeFormat;
import com.google.gwt.place.shared.Place;
import com.google.gwt.user.cellview.client.Column;
import com.google.gwt.user.cellview.client.ColumnSortEvent;
import com.google.gwt.user.cellview.client.ColumnSortEvent.AsyncHandler;
import com.google.gwt.user.cellview.client.ColumnSortList;
import com.google.gwt.user.cellview.client.TextColumn;
import com.google.gwt.user.client.ui.Button;
import com.google.gwt.user.client.ui.HasHorizontalAlignment;
import com.google.gwt.view.client.AsyncDataProvider;
import com.google.gwt.view.client.HasData;
import com.google.gwt.view.client.Range;
import za.co.ipay.gwt.common.client.Dialogs;
import za.co.ipay.gwt.common.client.form.FormManager;
import za.co.ipay.gwt.common.client.handler.ConfirmHandler;
import za.co.ipay.gwt.common.client.resource.Messages.MessagesUtil;
import za.co.ipay.gwt.common.client.validation.ClientValidatorUtil;
import za.co.ipay.gwt.common.client.workspace.SessionCheckCallback;
import za.co.ipay.gwt.common.client.workspace.SimpleTableView;
import za.co.ipay.gwt.common.shared.dto.IdNameDto;
import za.co.ipay.gwt.common.shared.exception.AccessControlException;
import za.co.ipay.metermng.client.event.PricingStructureUpdateEvent;
import za.co.ipay.metermng.client.factory.ClientFactory;
import za.co.ipay.metermng.client.history.PricingStructurePlace;
import za.co.ipay.metermng.client.resource.image.MediaResource.MediaResourceUtil;
import za.co.ipay.metermng.client.rpc.callback.ClientCallback;
import za.co.ipay.metermng.client.view.component.BaseComponent;
import za.co.ipay.metermng.client.view.workspace.pricing.PricingStructureWorkspaceView;
import za.co.ipay.metermng.client.widget.StatusTableColumn;
import za.co.ipay.metermng.datatypes.RecordStatus;
import za.co.ipay.metermng.mybatis.generated.model.PricingStructure;
import za.co.ipay.metermng.mybatis.generated.model.Tariff;
import za.co.ipay.metermng.shared.MeterMngStatics;
import za.co.ipay.metermng.shared.dto.PtrScreenDataDto;
import za.co.ipay.metermng.shared.dto.pricing.PricingStructureDto;

public class PricingStructureView extends BaseComponent implements FormManager<PricingStructureDto> {

    private PricingStructureWorkspaceView parentWorkspace;
    private PricingStructureDto pricingStructure = new PricingStructureDto();
    private AsyncDataProvider<PricingStructureDto> dataProvider;

	private SimpleTableView<PricingStructureDto> view;
	private PricingStructureFilterPanel filterPanel;

	private Button exportAllBtn;

    private PricingStructurePanel panel;
    private Button showTariffsBtn;

    private PricingStructureDto lastAdded;
    private Boolean isFindingLastAdded = false;

    private static Logger logger = Logger.getLogger(PricingStructureView.class.getName());

    private List<Long> pricingStructureIds;

    public PricingStructureView(PricingStructureWorkspaceView parentWorkspace,
                                 ClientFactory clientFactory,
                                 SimpleTableView<PricingStructureDto> view) {
        this.parentWorkspace = parentWorkspace;
        this.clientFactory = clientFactory;
        this.view = view;
        initView();
        initExportAll();
        initForm();
        initTableFilter();
        createTable();
        loadInitData();
        actionPermissions();
    }

    private void initView() {
        view.setFormManager(this);
    }

    private void initExportAll() {
        exportAllBtn = new Button(MessagesUtil.getInstance().getMessage("button.export"));
        exportAllBtn.setTitle(MessagesUtil.getInstance().getMessage("button.export.ps.title"));
        exportAllBtn.addClickHandler(new ClickHandler() {
            @Override
            public void onClick(ClickEvent event) {
                SessionCheckCallback sessionCheckCallback = new SessionCheckCallback() {
                    @Override
                    public void callback(SessionCheckResolution resolution) {
                        disableWidgets();
                        clientFactory.getPricingStructureRpc().getAllPricingStructuresWithCurrentTariff(
                                clientFactory.getUser().getCurrentGroupId(), pricingStructureIds, new ClientCallback<String>() {
                                    @Override
                                    public void onSuccess(String tariffPsStringOfJson) {
                                        if (tariffPsStringOfJson != null && !tariffPsStringOfJson.isEmpty()) {
                                            String fileDate = DateTimeFormat.getFormat("yyyyMMddTHHmmss").format(new Date());
                                            String jsonFileName = "CurrentTariffPS_" + fileDate + ".json";
                                            try {
                                                genJsonDownload(tariffPsStringOfJson, jsonFileName);
                                            } catch (Exception e) {
                                                logger.log(Level.SEVERE,"ERROR: export PS with current tariffs failed EXCEPTION: " + e + " : " + e.getMessage());
                                                Dialogs.centreErrorMessage(MessagesUtil.getInstance().getMessage("export.ps.failed.exception"),
                                                        MediaResourceUtil.getInstance().getErrorIcon(),
                                                        MessagesUtil.getInstance().getMessage("button.close"));
                                            }

                                        } else {
                                            Dialogs.centreErrorMessage(MessagesUtil.getInstance().getMessage("export.ps.failed.non"),
                                                    MediaResourceUtil.getInstance().getErrorIcon(),
                                                    MessagesUtil.getInstance().getMessage("button.close"));
                                        }

                                        enableWidgets();
                                    }
                                    @Override
                                    public void onFailure(Throwable caught) {
                                        enableWidgets();
                                        super.onFailure(caught);
                                    }
                                });
                    }
                };
                clientFactory.handleSessionCheckCallback(sessionCheckCallback);
            }
        });
        exportAllBtn.ensureDebugId("exportAllBtn");
        exportAllBtn.setVisible(true);
        view.getBelowTablePanel().add(exportAllBtn);
    }

    public void initForm() {
        panel = new PricingStructurePanel(view.getForm());
        view.getForm().setHasDirtyDataManager(parentWorkspace);
        view.getForm().getFormFields().add(panel);

        view.getForm().getFormPanel().setHeadingText(MessagesUtil.getInstance().getMessage("pricingstructure.title.new"));

        view.getForm().getSaveBtn().setText(MessagesUtil.getInstance().getMessage("button.create"));
        view.getForm().getSaveBtn().addClickHandler(new ClickHandler() {
            @Override
            public void onClick(ClickEvent event) {
                onSave();
            }
        });
        view.getForm().getSaveBtn().ensureDebugId("saveBtn");

        view.getForm().getOtherBtn().setText(MessagesUtil.getInstance().getMessage("button.cancel"));
        view.getForm().getOtherBtn().addClickHandler(new ClickHandler() {
            @Override
            public void onClick(ClickEvent event) {
                view.getForm().checkDirtyData(new ConfirmHandler() {
                    @Override
                    public void confirmed(boolean confirm) {
                        if (confirm) {
                            if (view.getForm().isDirtyData()) {
                                view.getForm().setDirtyData(false);
                                clear();
                            }else{
                                clear();
                            }
                        }
                    }
                });
            }
        });
        view.getForm().getOtherBtn().ensureDebugId("cancelBtn");

        showTariffsBtn = new Button(MessagesUtil.getInstance().getMessage("button.viewtariffs"));
        showTariffsBtn.addClickHandler(new ClickHandler() {
            @Override
            public void onClick(ClickEvent event) {
                final SessionCheckCallback callback = new SessionCheckCallback() {
                    @Override
                    public void callback(SessionCheckResolution resolution) {
                        view.getForm().checkDirtyData(new ConfirmHandler() {
                            @Override
                            public void confirmed(final boolean confirm) {
                                if (confirm) {
                                    if (view.getForm().isDirtyData()) {
                                        view.getForm().setDirtyData(false);
                                        setPricingStructure(pricingStructure);
                                    }
                                }
                                parentWorkspace.goToTariffs(pricingStructure.getPricingStructure());
                            }
                        });
                    }
                };
                clientFactory.handleSessionCheckCallback(callback);
            }
        });
        showTariffsBtn.ensureDebugId("showTariffsBtn");
        showTariffsBtn.setVisible(false);
        view.getForm().getSecondaryButtons().add(showTariffsBtn);
        view.getForm().getSecondaryButtons().setVisible(true);
    }

    private void initTableFilter() {
    	this.filterPanel = new PricingStructureFilterPanel(clientFactory, this);
    	view.getFiltersPanel().add(filterPanel);
    }

    private void createTable() {
        TextColumn<PricingStructureDto> nameColumn = new TextColumn<PricingStructureDto>() {
          @Override
          public String getValue(PricingStructureDto ps) {
            return ps.getPricingStructure().getName();
          }
        };
        nameColumn.setSortable(true);

        TextColumn<PricingStructureDto> descColumn = new TextColumn<PricingStructureDto>() {
          @Override
          public String getValue(PricingStructureDto ps) {
            return ps.getPricingStructure().getDescription();
          }
        };
        descColumn.setSortable(true);

        TextColumn<PricingStructureDto> activeColumn = new StatusTableColumn<PricingStructureDto>();
        activeColumn.setSortable(true);

        TextColumn<PricingStructureDto> tariffsColumn = new TextColumn<PricingStructureDto>() {
            @Override
            public String getValue(PricingStructureDto ps) {
              return Integer.toString(ps.getTariffCount());
            }
          };

        TextColumn<PricingStructureDto> serviceResourceColumn = new TextColumn<PricingStructureDto>() {
            @Override
            public String getValue(PricingStructureDto ps) {
                Long id = ps.getPricingStructure().getServiceResourceId();
                for (int i = 0; i < panel.serviceResourceBox.getItemCount(); i++) {
                	if (panel.serviceResourceBox.getValue(i).equals(id.toString())) {
                		return panel.serviceResourceBox.getItemText(i);
                	}
                }
                return "";
            }
        };
        serviceResourceColumn.setSortable(true);

        TextColumn<PricingStructureDto> meterTypeColumn = new TextColumn<PricingStructureDto>() {
            @Override
            public String getValue(PricingStructureDto ps) {
                Long id = ps.getPricingStructure().getMeterTypeId();
                for (int i = 0; i < panel.meterTypeBox.getItemCount(); i++) {
                	if (panel.meterTypeBox.getValue(i).equals(id.toString())) {
                		return panel.meterTypeBox.getItemText(i);
                	}
                }
                return "";
            }
        };
        meterTypeColumn.setSortable(true);

        TextColumn<PricingStructureDto> paymentModeColumn = new TextColumn<PricingStructureDto>() {
            @Override
            public String getValue(PricingStructureDto ps) {
                Long id = ps.getPricingStructure().getPaymentModeId();
                for (int i = 0; i < panel.paymentModeBox.getItemCount(); i++) {
                	if (panel.paymentModeBox.getValue(i).equals(id.toString())) {
                		return panel.paymentModeBox.getItemText(i);
                	}
                }
                return "";
            }
        };
        paymentModeColumn.setSortable(true);

        view.getTable().addColumn(nameColumn, MessagesUtil.getInstance().getMessage("pricingstructure.field.name"));
        view.getTable().addColumn(descColumn, MessagesUtil.getInstance().getMessage("pricingstructure.field.description"));
        view.getTable().addColumn(activeColumn, MessagesUtil.getInstance().getMessage("pricingstructure.field.status"));
        view.getTable().addColumn(tariffsColumn, MessagesUtil.getInstance().getMessage("pricingstructure.field.tariffs"));
        view.getTable().addColumn(serviceResourceColumn, MessagesUtil.getInstance().getMessage("ptr.serviceresource"));
        view.getTable().addColumn(meterTypeColumn, MessagesUtil.getInstance().getMessage("ptr.metertype"));
        view.getTable().addColumn(paymentModeColumn, MessagesUtil.getInstance().getMessage("ptr.paymentmode"));

        view.getTable().ensureDebugId("pricingStructureTable");
        view.getTable().getColumn(3).setHorizontalAlignment(HasHorizontalAlignment.ALIGN_RIGHT);

        // Set the range to display
        view.getTable().setVisibleRange(0, getPageSize());

        // Set the data provider for the table
        dataProvider = new AsyncDataProvider<PricingStructureDto>() {
            @Override
            protected void onRangeChanged(HasData<PricingStructureDto> display) {
            	if (isFindingLastAdded) {
            		isFindingLastAdded = false;
            		return;
            	}
                final int start = display.getVisibleRange().getStart();
                String sortColumn = null;

                int filterTableColIndx = filterPanel.getFilterTableColIndx();
                String filterString = filterPanel.getFilterString();

                boolean isAscending = true;
                ColumnSortList sortList = view.getTable().getColumnSortList();
                if (sortList != null && sortList.size() != 0) {
                    @SuppressWarnings("unchecked")
                    Column<PricingStructureDto, ?> sColumn = (Column<PricingStructureDto, ?>) sortList.get(0).getColumn();
                    Integer columnIndex = view.getTable().getColumnIndex(sColumn);
                    sortColumn = getColumnName(columnIndex);
                    isAscending = sortList.get(0).isAscending();
                }
                clientFactory.getPricingStructureRpc().getPricingStructures(start, getPageSize(), sortColumn, filterTableColIndx, filterString, isAscending, lastAdded,
                        new ClientCallback<ArrayList<PricingStructureDto>>() {
                            @Override
                            public void onSuccess(ArrayList<PricingStructureDto> result) {
                                if (!result.isEmpty()) {
                                    int returnedDataStart = result.get(0).getPricingStructurePagerIndex();
                                    // When locating newly added record and changing page range do not run through
                                    // the onRangeChange method again!
                                    if (lastAdded != null) {

                                        if (returnedDataStart != start) {
                                            isFindingLastAdded = true;
                                        }
                                    }

                                    // This will trigger the onRangeChange event to happen again if
                                    // returnedDataStart is not equal to start.
                                    view.getPager().setPageStart(returnedDataStart);
                                    dataProvider.updateRowData(returnedDataStart, result);

                                    // Select and highlight newly added or updated record.
                                    if (lastAdded != null) {
                                        String name = lastAdded.getPricingStructure().getName();
                                        List<PricingStructureDto> myList = view.getTable().getVisibleItems();

                                        for (PricingStructureDto psDto : myList) {

                                            if (psDto.getPricingStructure().getName().equals(name)) {
                                                if (!view.getTable().getSelectionModel().isSelected(psDto)) {
                                                    view.setSelectedTableData(psDto);
                                                }
                                                break;
                                            }
                                        }
                                    }
                                    lastAdded = null;
                                }
                            }
                        });
            }
        };
        dataProvider.addDataDisplay(view.getTable());

        // Create the table's pager
        view.getPager().setDisplay(view.getTable());

        // Set the table's column sorter handler
        AsyncHandler columnSortHandler = new AsyncHandler(view.getTable()) {
            @Override
            public void onColumnSort(ColumnSortEvent event) {
                final int start = view.getTable().getVisibleRange().getStart();
                @SuppressWarnings("unchecked")
                int sortIndex = view.getTable().getColumnIndex((Column<PricingStructureDto, ?>) event.getColumn());
                String sortColumn = getColumnName(sortIndex);

                int filterTableColIndx = filterPanel.getFilterTableColIndx();
                String filterString = filterPanel.getFilterString();

                boolean isAscending = event.isSortAscending();
                clientFactory.getPricingStructureRpc().getPricingStructures(start, getPageSize(), sortColumn, filterTableColIndx, filterString, isAscending, null,
                        new ClientCallback<ArrayList<PricingStructureDto>>() {
                            @Override
                            public void onSuccess(ArrayList<PricingStructureDto> result) {
                                dataProvider.updateRowData(start, result);
                            }
                        });
            }
        };
        view.getTable().addColumnSortHandler(columnSortHandler);
        view.getTable().getColumnSortList().push(nameColumn);
    }

    //needs package level visibility to be used by the PricingStructureFilterPanel
    String getColumnName(int index) {
        if (index == 0) {
            return "name";
        } else if (index == 1) {
            return "description";
        } else if (index == 2) {
            return "status";
        } else if (index == 4) {
            return "serviceResource";
        } else if (index == 5) {
            return "meterType";
        } else  {
            return "paymentMode";
        }
    }

    private void loadInitData() {
        clientFactory.getLookupRpc().getPtrScreenData(new ClientCallback<PtrScreenDataDto>() {
            @Override
            public void onSuccess(PtrScreenDataDto screenData) {
                setInitData(screenData);
            }
        });
    }

    private void setInitData(PtrScreenDataDto screenData) {
        panel.serviceResourceBox.clear();
        panel.serviceResourceBox.addItem("", "");

        panel.meterTypeBox.clear();
        panel.meterTypeBox.addItem("", "");

        panel.paymentModeBox.clear();
        panel.paymentModeBox.addItem("", "");

        filterPanel.filterDropdown.addItem("");
        filterPanel.filterDropdown.addItem(MessagesUtil.getInstance().getMessage("pricingstructure.field.name"));
        filterPanel.filterDropdown.addItem(MessagesUtil.getInstance().getMessage("pricingstructure.field.description"));
        filterPanel.filterDropdown.addItem(MessagesUtil.getInstance().getMessage("pricingstructure.field.status"));
        filterPanel.filterDropdown.addItem(MessagesUtil.getInstance().getMessage("ptr.serviceresource"));
        filterPanel.filterDropdown.addItem(MessagesUtil.getInstance().getMessage("ptr.metertype"));
        filterPanel.filterDropdown.addItem(MessagesUtil.getInstance().getMessage("ptr.paymentmode"));

        filterPanel.statusFilterDropdown.addItem("");
        filterPanel.statusFilterDropdown.addItem(MessagesUtil.getInstance().getMessage("status.active"));
        filterPanel.statusFilterDropdown.addItem(MessagesUtil.getInstance().getMessage("status.inactive"));

        filterPanel.serviceResourceFilterDropdown.addItem("");
        ArrayList<IdNameDto> resources = screenData.getServiceResources();
        if (resources != null) {
            for (IdNameDto dto : resources) {
                panel.serviceResourceBox.addItem(dto.getName(), dto.getId().toString());
                filterPanel.serviceResourceFilterDropdown.addItem(dto.getName(), dto.getId().toString());
            }
        }

        filterPanel.meterTypeFilterDropdown.addItem("");
        ArrayList<IdNameDto> types = screenData.getMeterTypes();
        if (types != null) {
            for (IdNameDto dto : types) {
                panel.meterTypeBox.addItem(dto.getName(), dto.getId().toString());
                filterPanel.meterTypeFilterDropdown.addItem(dto.getName(), dto.getId().toString());
            }
        }

        filterPanel.paymentModeFilterDropdown.addItem("");
        ArrayList<IdNameDto> modes = screenData.getPaymentModes();
        if (modes != null) {
            for (IdNameDto dto : modes) {
                panel.paymentModeBox.addItem(dto.getName(), dto.getId().toString());
                filterPanel.paymentModeFilterDropdown.addItem(dto.getName(), dto.getId().toString());
            }
        }
        panel.mridComponent.initMrid(clientFactory);
    }

    private void onSave() {
        SessionCheckCallback sessionCheckCallback = new SessionCheckCallback() {
            @Override
            public void callback(SessionCheckResolution resolution) {
                disableWidgets();

                if (panel.activeCheckBox.getValue()) {   //marked as active
                    if (pricingStructure.getPricingStructure().getId() != null) {    //ps always saved BEFORE go add tariffs, if no id, no tariffs!!
                        clientFactory.getPricingStructureRpc().getTariffsForPricingStructure(pricingStructure.getPricingStructure().getId(),
                                new ClientCallback<List<Tariff>>() {
                                    @Override
                                    public void onSuccess(List<Tariff> result) {
                                        logger.info("PricingStructure: " + pricingStructure.getPricingStructure().getName() + " : Got latest tariffs: "+result.size());
                                        save(result, false);
                                    }
                                });
                    } else {
                        save(null, false);
                    }
                } else { //marked as inactive
                    if (pricingStructure.getPricingStructure().getId() != null
                            && pricingStructure.getRecordStatus().equals(RecordStatus.ACT)) {
                        //if saved record is still Active, the checkbox has been clicked to inactive - check if any Usage Points use this ps
                        clientFactory.getUsagePointRpc().checkIfInUse(pricingStructure.getPricingStructure().getId(),
                                new ClientCallback<Boolean>() {
                                    @Override
                                    public void onSuccess(Boolean result) {
                                        logger.info("PricingStructure: " + pricingStructure.getPricingStructure().getName() + " : is in use = " + result);
                                        save(null, result);
                                    }
                                });
                    } else {
                        save(null, false);
                    }

                }
            }
        };
        clientFactory.handleSessionCheckCallback(sessionCheckCallback);
    }

    private void save(final List<Tariff> currentTariffs, boolean attemptDeactivateButHasUsagePoints) {
        if (isValid(currentTariffs, attemptDeactivateButHasUsagePoints)) {
            update();
            final Long id = pricingStructure.getPricingStructure().getId();
            clientFactory.getPricingStructureRpc().savePricingStructure(
                    pricingStructure.getPricingStructure(),
                    new ClientCallback<PricingStructure>(view.getForm().getSaveBtn().getAbsoluteLeft(), view.getForm().getSaveBtn().getAbsoluteTop()) {
                        @Override
                        public void onSuccess(PricingStructure ps) {
                        	filterPanel.clearFilter();
                            view.getForm().setDirtyData(false);
                            boolean refresh = false;
                            if (id == null) {
                                refresh = true;
                            }

                            pricingStructure.setPricingStructure(ps);
                            lastAdded = pricingStructure;
                            refreshTable(refresh);
                            Dialogs.displayInformationMessage(
                                    MessagesUtil.getInstance()
                                            .getSavedMessage(
                                                    new String[] { MessagesUtil.getInstance().getMessage("pricingstructure.title") }), MediaResourceUtil.getInstance()
                                            .getInformationIcon(), view.getForm().getSaveBtn().getAbsoluteLeft(), view
                                            .getForm().getSaveBtn().getAbsoluteTop(), MessagesUtil.getInstance()
                                            .getMessage("button.close"));
                            if (ps.getRecordStatus() == RecordStatus.ACT) {
                                firePricingStructureEvent(currentTariffs);
                            }
                            clear();
                            enableWidgets();
                        }
                        @Override
                        public void onFailure(Throwable caught) {
                            if (caught instanceof AccessControlException) {
                                clientFactory.getWorkspaceContainer().closeWorkspaceNow(PricingStructurePlace.ALL_PRICING_STRUCTURES_PLACE);
                            }
                            super.onFailure(caught);
                            enableWidgets();
                        }
                    });
        } else {
            enableWidgets();
        }
    }

    private void firePricingStructureEvent(List<Tariff> currentTariffs) {
        clientFactory.getEventBus().fireEvent(new PricingStructureUpdateEvent(true));
        if (currentTariffs != null && !currentTariffs.isEmpty()) {
            long now = System.currentTimeMillis();
            long start;
            Date soonestFutureTariff = null;
            for (Tariff tariff : currentTariffs) {
                start = tariff.getStartDate().getTime();
                if (start > now && (soonestFutureTariff == null || start < soonestFutureTariff.getTime())) {
                    soonestFutureTariff = tariff.getStartDate();
                }
            }
            if (soonestFutureTariff != null) {
                clientFactory.getScheduledEventDispatcher().schedule(new PricingStructureUpdateEvent(true),
                        soonestFutureTariff);
            }
        }
    }

    private void enableWidgets() {
        panel.activeCheckBox.setEnabled(true);
        view.getForm().getSaveBtn().setEnabled(true);
        view.getForm().getOtherBtn().setEnabled(true);
        if (exportAllBtn != null) {
            exportAllBtn.setEnabled(true);
        }
    }

    private void disableWidgets() {
        panel.activeCheckBox.setEnabled(false);
        view.getForm().getSaveBtn().setEnabled(false);
        view.getForm().getOtherBtn().setEnabled(false);
        if (exportAllBtn != null) {
            exportAllBtn.setEnabled(false);
        }
    }

    public void onArrival(Place place) {
        getPricingStructuresCount(false);
    }

    public void getPricingStructuresCount(final boolean refreshTable) {
        clientFactory.getPricingStructureRpc().getPricingStructuresIdsForFilters(filterPanel.getFilterTableColIndx(),
                filterPanel.getFilterString(), new ClientCallback<List<Long>>() {
            @Override
            public void onFailureClient() {
                view.getTable().setRowCount(0, true);
                dataProvider.updateRowCount(0, true);
            }

                    @Override
                    public void onSuccess(List<Long> result) {
                        int amount = result.size();
                        logger.info("Got count: " + amount);
                        view.getTable().setRowCount(amount, true);
                        dataProvider.updateRowCount(amount, true);
                        // Force a table update - reloads data from the back end
                        if (refreshTable) {
                            refreshTable(false);
                        }
                        pricingStructureIds = result;
                        exportAllBtn.setEnabled(amount > 0);
                    }
        });
    }

    //Method to force the table to refresh its current page. A new row could of been added or just the data should be
    //reloaded due to other changes like disabled user.
    private void refreshTable(boolean insertedNew) {
        logger.info("Refreshing table insertedNew:"+insertedNew);
        getPricingStructuresCount(false);
        Range range = view.getTable().getVisibleRange();
        view.getTable().setVisibleRangeAndClearData(range, true);
    }

    public void clear() {
        pricingStructure = new PricingStructureDto();
        panel.clearFields();
        panel.clearErrors();
        panel.mridComponent.initMrid(clientFactory);
        showTariffsBtn.setVisible(false);
        view.clearTableSelection();
        view.getForm().getFormPanel().setHeadingText(MessagesUtil.getInstance().getMessage("pricingstructure.title.new"));
        view.getForm().getSaveBtn().setVisible(true);
        view.getForm().getSaveBtn().setText(MessagesUtil.getInstance().getMessage("button.create"));
    }

    private boolean isValid(List<Tariff> currentTariffs, boolean attemptDeactivateButHasUsagePoints) {
        boolean valid = true;
        panel.clearErrors();

        PricingStructureDto ps = new PricingStructureDto();
        ps.getPricingStructure().setName(panel.nameTextBox.getText());
        ps.getPricingStructure().setDescription(panel.descTextBox.getText());
        ps.getPricingStructure().setMrid(panel.mridComponent.getMrid());
        ps.getPricingStructure().setMridExternal(panel.mridComponent.isExternal());
        int index = panel.serviceResourceBox.getSelectedIndex();
        if (index > 0) {
            ps.getPricingStructure().setServiceResourceId(Long.valueOf(panel.serviceResourceBox.getValue(index)));
        } else {
            ps.getPricingStructure().setServiceResourceId(null);
        }
        index = panel.meterTypeBox.getSelectedIndex();
        if (index > 0) {
            ps.getPricingStructure().setMeterTypeId(Long.valueOf(panel.meterTypeBox.getValue(index)));
        } else {
            ps.getPricingStructure().setMeterTypeId(null);
        }
        index = panel.paymentModeBox.getSelectedIndex();
        if (index > 0) {
            ps.getPricingStructure().setPaymentModeId(Long.valueOf(panel.paymentModeBox.getValue(index)));
        } else {
            ps.getPricingStructure().setPaymentModeId(null);
        }

        if (!ClientValidatorUtil.getInstance().validateField(ps.getPricingStructure(), "name", panel.nameElement)) {
            valid = false;
        }
        if (!ClientValidatorUtil.getInstance().validateField(ps.getPricingStructure(), "description", panel.descElement)) {
            valid = false;
        }
        if (!ClientValidatorUtil.getInstance().validateField(ps.getPricingStructure(), "serviceResourceId", panel.serviceResourceElement)) {
            valid = false;
        }
        if (!ClientValidatorUtil.getInstance().validateField(ps.getPricingStructure(), "meterTypeId", panel.meterTypeElement)) {
            valid = false;
        }
        if (!ClientValidatorUtil.getInstance().validateField(ps.getPricingStructure(), "paymentModeId", panel.paymentModeElement)) {
            valid = false;
        }
        if (!ClientValidatorUtil.getInstance().validateField(ps.getPricingStructure(), "mrid", panel.mridComponent.getTxtbxMridElement())) {
            valid = false;
        }
        if (!panel.mridComponent.validate()) {
            valid = false;
        }

        if (panel.activeCheckBox.getValue()) {
            if (currentTariffs == null || currentTariffs.isEmpty()) {
                valid = false;
                panel.activeCheckBox.setEnabled(true);
                panel.activeCheckBox.setValue(false);
                //panel.activeElement.setErrorMsg(MessagesUtil.getInstance().getMessage("pricingstructure.error.active"));
                Dialogs.displayErrorMessage(MessagesUtil.getInstance().getMessage("pricingstructure.error.active"),
                        MediaResourceUtil.getInstance().getErrorIcon(),
                        panel.activeCheckBox.getAbsoluteLeft() + panel.activeCheckBox.getOffsetWidth(),
                        panel.activeCheckBox.getAbsoluteTop(),
                        MessagesUtil.getInstance().getMessage("button.close"));
            }
        } else if (attemptDeactivateButHasUsagePoints) {
            valid = false;
            panel.activeCheckBox.setEnabled(true);
            panel.activeCheckBox.setValue(true);
            Dialogs.displayErrorMessage(MessagesUtil.getInstance().getMessage("pricingstructure.error.deactivate"),
                    MediaResourceUtil.getInstance().getErrorIcon(),
                    panel.activeCheckBox.getAbsoluteLeft() + panel.activeCheckBox.getOffsetWidth(),
                    panel.activeCheckBox.getAbsoluteTop(),
                    MessagesUtil.getInstance().getMessage("button.close"));
        }

        return valid;
    }

    public void update() {
        pricingStructure.getPricingStructure().setName(panel.nameTextBox.getText());
        pricingStructure.getPricingStructure().setDescription(panel.descTextBox.getText());
        pricingStructure.getPricingStructure().setRecordStatus(panel.activeCheckBox.getValue() ? RecordStatus.ACT : RecordStatus.DAC);
        pricingStructure.getPricingStructure().setMrid(panel.mridComponent.getMrid());
        pricingStructure.getPricingStructure().setMridExternal(panel.mridComponent.isExternal());

        int index = panel.serviceResourceBox.getSelectedIndex();
        if (index > 0) {
            pricingStructure.getPricingStructure().setServiceResourceId(Long.valueOf(panel.serviceResourceBox.getValue(index)));
        } else {
            pricingStructure.getPricingStructure().setServiceResourceId(null);
        }

        index = panel.meterTypeBox.getSelectedIndex();
        if (index > 0) {
            pricingStructure.getPricingStructure().setMeterTypeId(Long.valueOf(panel.meterTypeBox.getValue(index)));
        } else {
            pricingStructure.getPricingStructure().setMeterTypeId(null);
        }

        index = panel.paymentModeBox.getSelectedIndex();
        if (index > 0) {
            pricingStructure.getPricingStructure().setPaymentModeId(Long.valueOf(panel.paymentModeBox.getValue(index)));
        } else {
            pricingStructure.getPricingStructure().setPaymentModeId(null);
        }
    }

    public void setPricingStructure(PricingStructureDto ps) {
        panel.clearFields();
        panel.clearErrors();

        this.pricingStructure = ps;
        if (pricingStructure == null) {
            this.pricingStructure = new PricingStructureDto();
        }
        if (pricingStructure.getPricingStructure().getId() != null) {
            view.getForm().getFormPanel().setHeadingText(MessagesUtil.getInstance().getMessage("pricingstructure.title.edit"));
            view.getForm().getSaveBtn().setText(MessagesUtil.getInstance().getMessage("button.update"));
            showTariffsBtn.setVisible(true);
        } else {
            view.getForm().getFormPanel().setHeadingText(MessagesUtil.getInstance().getMessage("pricingstructure.title.new"));
            view.getForm().getSaveBtn().setText(MessagesUtil.getInstance().getMessage("button.create"));
            showTariffsBtn.setVisible(false);
            view.clearTableSelection();
        }

        panel.nameTextBox.setText(pricingStructure.getPricingStructure().getName());
        panel.descTextBox.setText(pricingStructure.getPricingStructure().getDescription());
        panel.activeCheckBox.setValue(RecordStatus.ACT.equals(pricingStructure.getRecordStatus()));
        panel.mridComponent.setMrid(pricingStructure.getPricingStructure().getMrid());
        panel.mridComponent.setIsExternal(pricingStructure.getPricingStructure().isMridExternal());
        if (pricingStructure.getPricingStructure().getServiceResourceId() != null) {
            String id = pricingStructure.getPricingStructure().getServiceResourceId().toString();
            for(int i=0;i<panel.serviceResourceBox.getItemCount();i++) {
                if (panel.serviceResourceBox.getValue(i).equals(id)) {
                    panel.serviceResourceBox.setSelectedIndex(i);
                    break;
                }
            }
        }

        if (pricingStructure.getPricingStructure().getMeterTypeId() != null) {
            String id = pricingStructure.getPricingStructure().getMeterTypeId().toString();
            for(int i=0;i<panel.meterTypeBox.getItemCount();i++) {
                if (panel.meterTypeBox.getValue(i).equals(id)) {
                    panel.meterTypeBox.setSelectedIndex(i);
                    break;
                }
            }
        }

        if (pricingStructure.getPricingStructure().getPaymentModeId() != null) {
            String id = pricingStructure.getPricingStructure().getPaymentModeId().toString();
            for(int i=0;i<panel.paymentModeBox.getItemCount();i++) {
                if (panel.paymentModeBox.getValue(i).equals(id)) {
                    panel.paymentModeBox.setSelectedIndex(i);
                    break;
                }
            }
        }

        if (pricingStructure.getTariffCount() > 0) {
            panel.disablePrtFields();
        }

        // For Access Groups confirm edit ability
        view.getForm().getSaveBtn().setVisible(true);
        if (pricingStructure.getPricingStructure().getId() != null &&
                !clientFactory.hasAccessGroupsEditPermissions(pricingStructure.getPricingStructure().getAccessGroupId())) {
            view.getForm().getSaveBtn().setVisible(false);
        }
    }

    @Override
    public void displaySelected(PricingStructureDto selected) {
        setPricingStructure(selected);
    }

    public void updatePricingStructure(Long pricingStructureId, int numberOfTariffs) {
        if (numberOfTariffs > 0) {
            if (pricingStructure != null && pricingStructure.getPricingStructure().getId().equals(pricingStructureId)) {
                pricingStructure.setTariffCount(numberOfTariffs);
                if (numberOfTariffs > 0) {
                    panel.disablePrtFields();
                }
            }
            refreshTable(false);
        }
    }

    private void actionPermissions() {
        if (!clientFactory.getUser().hasPermission(MeterMngStatics.EXPORT_PERMISSION_MM_PRICING_STRUCT)) {
            exportAllBtn.removeFromParent();
        }
        if (!clientFactory.getUser().hasPermission(MeterMngStatics.EDIT_PERMISSION_MM_PRICING_STRUCT)) {
            view.getForm().getButtons().removeFromParent();
        }
    }

    //*******************************************************************************************************

    public static native void genJsonDownload(String jsonContent, String fileName) /*-{

        var download = function (content, fileName, mimeType) {
            var a = document.createElement('a');
            mimeType = mimeType || 'application/json';

            if (navigator.msSaveBlob) { // IE10
                navigator.msSaveBlob(new Blob([content], {
                    type: mimeType
                }), fileName);
            } else if (URL && 'download' in a) { //html5 A[download]
                a.href = URL.createObjectURL(new Blob([content], {
                    type: mimeType
                }));
                a.setAttribute('download', fileName);
                document.body.appendChild(a);
                a.click();
                document.body.removeChild(a);
            } else {
                location.href = 'data:application/json,' + encodeURIComponent(content); // only this mime type is supported
            }
        }

        download(jsonContent, fileName, 'text/json;encoding:utf-8');
    }-*/;

    //*******************************************************************************************************

    /**
	 * @return the view
	 */
	public SimpleTableView<PricingStructureDto> getView() {
		return this.view;
	}

    /**
	 * @return the dataProvider
	 */
	public AsyncDataProvider<PricingStructureDto> getDataProvider() {
		return this.dataProvider;
	}
}
