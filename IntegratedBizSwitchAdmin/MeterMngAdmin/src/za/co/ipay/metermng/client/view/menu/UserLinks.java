package za.co.ipay.metermng.client.view.menu;

import com.google.gwt.core.client.GWT;
import com.google.gwt.core.client.Scheduler.ScheduledCommand;
import com.google.gwt.event.dom.client.ClickEvent;
import com.google.gwt.event.dom.client.ClickHandler;
import com.google.gwt.resources.client.ImageResource;
import com.google.gwt.safehtml.client.SafeHtmlTemplates;
import com.google.gwt.safehtml.shared.SafeHtml;
import com.google.gwt.safehtml.shared.SafeHtmlUtils;
import com.google.gwt.safehtml.shared.SafeUri;
import com.google.gwt.uibinder.client.UiBinder;
import com.google.gwt.uibinder.client.UiField;
import com.google.gwt.user.client.Window;
import com.google.gwt.user.client.ui.Image;
import com.google.gwt.user.client.ui.MenuBar;
import com.google.gwt.user.client.ui.MenuItem;
import com.google.gwt.user.client.ui.PopupPanel;
import com.google.gwt.user.client.ui.PushButton;
import com.google.gwt.user.client.ui.Widget;

import za.co.ipay.gwt.common.client.Dialogs;
import za.co.ipay.gwt.common.client.form.Format.FormatUtil;
import za.co.ipay.gwt.common.client.handler.ConfirmHandler;
import za.co.ipay.gwt.common.client.resource.Messages.MessagesUtil;
import za.co.ipay.gwt.common.client.workspace.TabLayoutWorkspaceContainer;
import za.co.ipay.gwt.common.client.workspace.WaitingDialog.WaitingDialogUtil;
import za.co.ipay.gwt.common.shared.validation.ValidateUtil;
import za.co.ipay.metermng.client.event.OpenAdminDashboardEvent;
import za.co.ipay.metermng.client.event.OpenDashboardEvent;
import za.co.ipay.metermng.client.factory.ClientFactory;
import za.co.ipay.metermng.client.history.SelectAccessGroupPlace;
import za.co.ipay.metermng.client.resource.image.MediaResource.MediaResourceUtil;
import za.co.ipay.metermng.client.rpc.callback.ClientCallback;
import za.co.ipay.metermng.client.view.component.BaseComponent;
import za.co.ipay.metermng.client.view.component.group.ChangeGroupView;
import za.co.ipay.metermng.client.view.component.password.ChangePasswordView;
import za.co.ipay.metermng.shared.MeterMngStatics;
import za.co.ipay.metermng.shared.dto.user.MeterMngUser;

public class UserLinks extends BaseComponent {

    public interface MenuTemplate extends SafeHtmlTemplates {
        @Template("<img src=\"{0}\" class=\"menuImage\"/><span>{1}</span>")
        SafeHtml createItem(SafeUri uri, SafeHtml message);
    }
    
    public interface MenuResources extends MenuBar.Resources {
        @Source("empty-1_10.png")
        ImageResource menuBarSubMenuIcon();
      }
    
    private MenuTemplate menuTemplate;
    
    @UiField(provided=true) PushButton menuButton;
    
    private MenuBar menuBar;
    private MenuItem usernameItem;
    private MenuItem changeGroupItem;
    private MenuItem dashboardItem;
    private MenuItem adminDashboardItem;
    private MenuItem timeZoneItem;
    
    private PopupPanel menuPopupPanel;
    private PopupPanel popupPanel;
    private PopupPanel passwordPopupPanel;
    private ChangeGroupView changeGroupView;
    private ChangePasswordView changePasswordView;
    private PopupPanel aboutPopupPanel;
    private PopupPanel timeZonePopupPanel;
    
    private String currentUsername = "";
    private String currentGroup = "";
    private String currentRole = "";
    
    private static UserLinksUiBinder uiBinder = GWT.create(UserLinksUiBinder.class);

    interface UserLinksUiBinder extends UiBinder<Widget, UserLinks> {
    }

    public UserLinks(ClientFactory clientFactory) {
        this.clientFactory = clientFactory;
        menuTemplate = GWT.create(MenuTemplate.class);
        menuButton = new PushButton(new Image(MediaResourceUtil.getInstance().getGearImage()));
        menuButton.addStyleName("menuButton");
        initWidget(uiBinder.createAndBindUi(this));
        initUi();
    }
    
    private void initUi() {
        setupMenu();
    }
    
    private void setupMenu() {
        menuBar = new MenuBar(true, (MenuBar.Resources) GWT.create(MenuResources.class));
        menuBar.ensureDebugId("linksMenu");
                
        usernameItem  = new MenuItem(buildUserNameItem(), menuBar);
        usernameItem.addStyleName("usernameMenuItem");
        usernameItem.setScheduledCommand(new ScheduledCommand() {            
            @Override
            public void execute() {
                menuPopupPanel.hide();
            }
        });
        menuBar.addItem(usernameItem);
       
        changeGroupItem = new MenuItem(menuTemplate.createItem(
                            MediaResourceUtil.getInstance().getUsersSmallIcon().getSafeUri(),
                            SafeHtmlUtils.fromString(MessagesUtil.getInstance().getMessage("link.group.change"))), menuBar);
        changeGroupItem.setScheduledCommand(new ScheduledCommand() {
            @Override
            public void execute() {
                menuPopupPanel.hide();
                if(clientFactory.isEnableAccessGroups()) {
                    Dialogs.confirm(new String[] { MessagesUtil.getInstance().getMessage("changegroup.org_group.confirm") },
                            MessagesUtil.getInstance().getMessage("option.positive"),
                            MessagesUtil.getInstance().getMessage("option.negative"),
                            MediaResourceUtil.getInstance().getQuestionIcon(),
                            new ConfirmHandler() {
                        @Override
                        public void confirmed(boolean confirm) {
                            if (confirm) {
                                Window.Location.assign(GWT.getHostPageBaseURL()+"sessionAuth.do");
                            }
                        }
                    }, null, null, null, true);

                } else {
                    displayChangeGroupScreen(false);
                }
            }
        });
        menuBar.addItem(changeGroupItem);   
        
        setUpTimeZonePanel();
        timeZoneItem = new MenuItem(menuTemplate.createItem(MediaResourceUtil.getInstance().getClockImage().getSafeUri(), 
                                    SafeHtmlUtils.fromString(MessagesUtil.getInstance().getMessage("timezone.change"))),
                menuBar);
        timeZoneItem.setScheduledCommand(new ScheduledCommand() {
            @Override
            public void execute() {
                menuPopupPanel.hide();
                timeZonePopupPanel.setPopupPosition(Window.getClientWidth() / 3, 50);
                ((TimeZonePanel) timeZonePopupPanel.getWidget()).button
                        .setEnabled(((TabLayoutWorkspaceContainer) clientFactory.getWorkspaceContainer())
                                .getWorkspacesCount() == 0);
                timeZonePopupPanel.show();

            }
        });
        menuBar.addItem(timeZoneItem);  
        
        setupChangePasswordScreen();
        MenuItem item = new MenuItem(menuTemplate.createItem(
                            MediaResourceUtil.getInstance().getKeyIcon().getSafeUri(),
                            SafeHtmlUtils.fromString(MessagesUtil.getInstance().getMessage("password.change.header"))), menuBar);
        item.setScheduledCommand(new ScheduledCommand() {            
            @Override
            public void execute() {                
                menuPopupPanel.hide();
                passwordPopupPanel.setGlassEnabled(true);
                passwordPopupPanel.setModal(false);
                passwordPopupPanel.center();         
                changePasswordView.displayCurrentUser(false);
            }
        });
        menuBar.addItem(item);    
        
        dashboardItem = new MenuItem(menuTemplate.createItem(
                MediaResourceUtil.getInstance().getDashboardIcon().getSafeUri(),
                SafeHtmlUtils.fromString(MessagesUtil.getInstance().getMessage("dashboard.title"))), menuBar);
        dashboardItem.setScheduledCommand(new ScheduledCommand() {
        @Override
        public void execute() {
           clientFactory.getEventBus().fireEvent(new OpenDashboardEvent());
        }
        });
        menuBar.addItem(dashboardItem);
        
        adminDashboardItem = new MenuItem(menuTemplate.createItem(
                MediaResourceUtil.getInstance().getDashboardIcon().getSafeUri(),
                SafeHtmlUtils.fromString(MessagesUtil.getInstance().getMessage("admin.dashboard.title"))), menuBar);
        adminDashboardItem.setScheduledCommand(new ScheduledCommand() {
        @Override
        public void execute() {
           clientFactory.getEventBus().fireEvent(new OpenAdminDashboardEvent());
        }
        });
        menuBar.addItem(adminDashboardItem); 
        
        item = new MenuItem(menuTemplate.createItem(
                    MediaResourceUtil.getInstance().getLogoutIcon().getSafeUri(),
                    SafeHtmlUtils.fromString(MessagesUtil.getInstance().getMessage("link.logout"))), menuBar);
        item.setScheduledCommand(new ScheduledCommand() {            
            @Override
            public void execute() {     
                Window.Location.assign(GWT.getHostPageBaseURL()+"j_spring_security_logout");                
            }
        });
        menuBar.addItem(item);
        
        setupAboutPopupPanel();
        
        item = new MenuItem(menuTemplate.createItem(
                MediaResourceUtil.getInstance().getMenuHelpIcon().getSafeUri(),
                SafeHtmlUtils.fromString(MessagesUtil.getInstance().getMessage("link.about"))), menuBar);
        item.setScheduledCommand(new ScheduledCommand() {            
            @Override
            public void execute() {     
                menuPopupPanel.hide();
                aboutPopupPanel.setPopupPosition(menuButton.getAbsoluteLeft() - 200, menuBar.getAbsoluteTop());
                aboutPopupPanel.show();
            }
        });
        menuBar.addItem(item);
        
        menuPopupPanel = new PopupPanel(true, false);
        menuPopupPanel.setWidget(menuBar);                
        
        menuButton.addClickHandler(new ClickHandler() {            
            @Override
            public void onClick(ClickEvent event) {
                menuPopupPanel.setPopupPosition(menuButton.getAbsoluteLeft() - 150, menuBar.getAbsoluteTop());
                menuPopupPanel.show();
            }
        });
    }
    
    protected void hideChangeGroupLink() {
        menuBar.removeItem(changeGroupItem);
    }
    
    protected void hideChangeTimeZoneLink() {
        menuBar.removeItem(timeZoneItem);
    }
    
	protected void hideDashboardLink() {
		menuBar.removeItem(dashboardItem);
	}
	protected void hideAdminDashboardLink() {
		menuBar.removeItem(adminDashboardItem);
	}
    
    protected void setupChangeGroupScreen() { 
        setupChangeGroupScreen(false); 
    }
    protected void setupChangeGroupScreen(boolean setupLoginAccessGroup) {        
        popupPanel = new PopupPanel(false, false);
        popupPanel.setHeight("500px");
        popupPanel.setWidth("555px");
        popupPanel.addStyleName(MeterMngStatics.MAIN_POPUP_STYLE);
        changeGroupView = new ChangeGroupView(popupPanel, clientFactory, setupLoginAccessGroup);
        if(setupLoginAccessGroup) {
            popupPanel.setGlassEnabled(true);
        }
        popupPanel.setWidget(changeGroupView);     
    }
    
    public void displayChangeGroupScreen(SelectAccessGroupPlace p) {
        displayChangeGroupScreen(true);
    }
    public void displayChangeGroupScreen(boolean setupLoginAccessGroup) {
        setupChangeGroupScreen(setupLoginAccessGroup);      //create a new object every time for this view - need one created before UI complete for select AccessGroupCode, otherwise will overwrite when build UI 
        popupPanel.setModal(setupLoginAccessGroup);
        popupPanel.setPopupPosition(Window.getClientWidth() / 3, 50);
        
        Dialogs.displayWaitDialog(MediaResourceUtil.getInstance().getWaitIcon());
        clientFactory.getUserRpc().getCurrentUser(new ClientCallback<MeterMngUser>() {
            @Override
            public void onSuccess(MeterMngUser result) {
                WaitingDialogUtil.getCurrentInstance().hide();
                changeGroupView.setUser(result);
                popupPanel.show();
            }
        });
    }
    
    public void setUsername(String username) {
        this.currentUsername = username;
        refreshUserNameItem();
    }
    
    public void setCurrentGroup(String groupName) {
        this.currentGroup = groupName;
        refreshUserNameItem();
    }

    public void setCurrentRole(String currentRole) {
        this.currentRole = currentRole;
        refreshUserNameItem();
    }
    
    private String buildUserNameItem() {
        String item = currentUsername;
        if(ValidateUtil.isNotNullOrBlank(currentGroup)) {
            item += "<br/>" + currentGroup;
        }
        if(ValidateUtil.isNotNullOrBlank(currentRole)) {
            item += "<br/>" + currentRole;
        }
        return item;
    }

    private void refreshUserNameItem() {
        usernameItem.setHTML(buildUserNameItem());
    }

    protected void setupChangePasswordScreen() {        
        passwordPopupPanel = new PopupPanel(false, false);
        passwordPopupPanel.setModal(false);
        passwordPopupPanel.setHeight("350px");
        passwordPopupPanel.setWidth("300px");
        passwordPopupPanel.addStyleName(MeterMngStatics.MAIN_POPUP_STYLE);
        changePasswordView = new ChangePasswordView(clientFactory, passwordPopupPanel);
        passwordPopupPanel.setWidget(changePasswordView);     
    }
    
    public void displayChangePasswordScreen(MeterMngUser user) {
        passwordPopupPanel.setGlassEnabled(true);
        passwordPopupPanel.setModal(true);
        passwordPopupPanel.center(); 
        changePasswordView.displayUser(user, true);
    }
    
    public void displayChangePasswordScreen() {
        passwordPopupPanel.setGlassEnabled(true);
        passwordPopupPanel.setModal(false);
        passwordPopupPanel.center(); 
        changePasswordView.displayCurrentUser(false);
    }
    
    protected void setupAboutPopupPanel() {        
        aboutPopupPanel = new PopupPanel(true, false);
        aboutPopupPanel.setHeight("150px");
        aboutPopupPanel.setWidth("200px");
        aboutPopupPanel.addStyleName(MeterMngStatics.MAIN_POPUP_STYLE);
        aboutPopupPanel.setWidget(new AboutPanel(clientFactory.isUseMancoLogo(), clientFactory.getLogoUrl()));     
    }
    
    protected void setUpTimeZonePanel() {        
        timeZonePopupPanel = new PopupPanel(false, false);
        timeZonePopupPanel.setHeight("160px");
        timeZonePopupPanel.setWidth("250px");
        timeZonePopupPanel.addStyleName(MeterMngStatics.MAIN_POPUP_STYLE);
        TimeZonePanel timeZonePanel = new TimeZonePanel(clientFactory, timeZonePopupPanel);
        timeZonePanel.setTimeZone(FormatUtil.getInstance().getTimeZoneName());
        timeZonePopupPanel.setWidget(timeZonePanel);
        timeZonePopupPanel.setGlassEnabled(true);
    }

}
