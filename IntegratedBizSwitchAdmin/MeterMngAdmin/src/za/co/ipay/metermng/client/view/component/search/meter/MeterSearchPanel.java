package za.co.ipay.metermng.client.view.component.search.meter;

import com.google.gwt.core.client.GWT;
import com.google.gwt.event.dom.client.ChangeEvent;
import com.google.gwt.event.dom.client.ChangeHandler;
import com.google.gwt.event.dom.client.KeyDownHandler;
import com.google.gwt.event.logical.shared.ValueChangeEvent;
import com.google.gwt.event.logical.shared.ValueChangeHandler;
import com.google.gwt.uibinder.client.UiBinder;
import com.google.gwt.uibinder.client.UiField;
import com.google.gwt.user.client.ui.CheckBox;
import com.google.gwt.user.client.ui.ListBox;
import com.google.gwt.user.client.ui.RadioButton;
import com.google.gwt.user.client.ui.TextBox;
import com.google.gwt.user.client.ui.Widget;
import za.co.ipay.gwt.common.client.form.FormElement;
import za.co.ipay.gwt.common.client.widgets.IpayListBox;
import za.co.ipay.gwt.common.shared.LookupListItem;
import za.co.ipay.metermng.client.factory.ClientFactory;
import za.co.ipay.metermng.client.history.SearchPlace;
import za.co.ipay.metermng.client.rpc.callback.ClientCallback;
import za.co.ipay.metermng.client.view.component.BaseComponent;
import za.co.ipay.metermng.client.view.component.search.Search;
import za.co.ipay.metermng.client.view.workspace.search.AdvancedSearchWorkspaceView;
import za.co.ipay.metermng.datatypes.MeterTypeE;
import za.co.ipay.metermng.shared.MeterMngStatics;
import za.co.ipay.metermng.shared.SearchType;
import za.co.ipay.metermng.shared.dto.search.SearchData;
import za.co.ipay.metermng.shared.dto.search.SearchResultType;

import java.util.ArrayList;
import java.util.List;

@SuppressWarnings("deprecation")
public class MeterSearchPanel extends BaseComponent implements Search {

    private static MeterSearchPanelUiBinder uiBinder = GWT.create(MeterSearchPanelUiBinder.class);

    interface MeterSearchPanelUiBinder extends UiBinder<Widget, MeterSearchPanel> {
    }

    @UiField FormElement meterNumberElement;
    @UiField FormElement meterModelElement;
    @UiField FormElement currSGCElement;
    @UiField FormElement deviceStoreElement;
    @UiField FormElement meterNoUsagePointElement;

    @UiField TextBox meterNumberBox;
    @UiField IpayListBox lstbxMeterModel;
    @UiField ListBox lstbxSgKrn;
    @UiField ListBox lstbxSelectStore;
    @UiField CheckBox chckbxMeterNoUsagePoint;
    @UiField RadioButton startWithBox;
    @UiField RadioButton containsBox;
    
    public MeterSearchPanel(ClientFactory clientFactory, final AdvancedSearchWorkspaceView advancedSearchWorkspaceView) {
        this.clientFactory = clientFactory;
        initWidget(uiBinder.createAndBindUi(this));
        populateMeterModeListbox();
        populateSgKrnCodeListBox();
        populateStoresListBox();
        addHandlers(advancedSearchWorkspaceView);
    }
    
    public void clearFields() {
        meterNumberBox.setText("");
        clearMeterModelSelections();  
        currSGCElement.setVisible(true);
        lstbxSgKrn.setVisible(true);
        lstbxSgKrn.setSelectedIndex(0);
        lstbxSelectStore.setSelectedIndex(0);
        showCheckbox();
        startWithBox.setValue(true);
        containsBox.setValue(false);
    }
    
    public void clearMeterModelSelections(){
        if (lstbxMeterModel.getItemCount() > 0) {
            for(int i=0;i<lstbxMeterModel.getItemCount();i++) {
                lstbxMeterModel.setItemSelected(i, false);
                lstbxMeterModel.getElement().getElementsByTagName("option").getItem(i).removeAttribute("style");
            }
        }
    }
    
    public void clearErrors() {
        meterNumberElement.showErrorMsg(null);   
        meterModelElement.showErrorMsg(null);
        meterNoUsagePointElement.showErrorMsg(null); 
    }

    @Override
    public void clear() {
        clearFields();
        clearErrors();
    }

    public void hideCheckbox() {
        chckbxMeterNoUsagePoint.setVisible(false);
    }

    public void showCheckbox() {
        chckbxMeterNoUsagePoint.setValue(false);
        chckbxMeterNoUsagePoint.setVisible(true);
    }

    public void populateMeterModeListbox() {
        ClientCallback<ArrayList<LookupListItem>> lookupSvcAsyncCallback = new ClientCallback<ArrayList<LookupListItem>>() {
            @Override
            public void onSuccess(ArrayList<LookupListItem> result) {
                lstbxMeterModel.clear();
                lstbxMeterModel.setLookupItems(result);
            }
        };
        clientFactory.getLookupRpc().getMeterModelLookupList(lookupSvcAsyncCallback);
    }

    //populate SGC listbox
    private void populateSgKrnCodeListBox() {
        ClientCallback<ArrayList<LookupListItem>> lookupSvcAsyncCallback = new ClientCallback<ArrayList<LookupListItem>>() {
            @Override
            public void onSuccess(ArrayList<LookupListItem> result) {
                if (result != null) {
                    for (int i=0; i<result.size(); i++) {
                        lstbxSgKrn.addItem(result.get(i).getText(), result.get(i).getValue());
                     }
                }
            }
        };
        if (clientFactory != null) {
            lstbxSgKrn.clear();
            clientFactory.getLookupRpc().getSgKrnLookupList(lookupSvcAsyncCallback);
        }
    }
    
    //populate Device Store List
    private void populateStoresListBox() {
        ClientCallback<ArrayList<LookupListItem>> lookupSvcAsyncCallback = new ClientCallback<ArrayList<LookupListItem>>() {
            @Override
            public void onSuccess(ArrayList<LookupListItem> result) {
                if (result != null) {
                    result.add(0, new LookupListItem(null, ""));
                    for (int i=0; i<result.size(); i++) {
                        lstbxSelectStore.addItem(result.get(i).getText(), result.get(i).getValue());
                     }
                }
            }
        };
        if (clientFactory != null) {
            lstbxSelectStore.clear();
            clientFactory.getLookupRpc().getEndDeviceStoresLookupList(lookupSvcAsyncCallback);
        }
    }
    
    @Override
    public boolean displayCriteria(SearchPlace searchPlace) {
        clearMeterModelSelections();
        if (SearchResultType.METER.equals(searchPlace.getDataType())
                && searchPlace.getSearchText() != null && !searchPlace.getSearchText().trim().equals("")) {
            meterNumberBox.setText(searchPlace.getSearchText());
            containsBox.setValue(true);
            return true;
        } else {
            return false;
        }
    }

    @Override
    public boolean isValidInput() {
        clearErrors();
        if (isValid(meterNumberBox.getText())) {
            return true;
        } else if(isMultiSelected(lstbxMeterModel)) {
            return true;
        } else if(chckbxMeterNoUsagePoint.getValue()) {
            return true;
        } else if(lstbxSgKrn.getSelectedIndex() != 0 && lstbxSgKrn.getSelectedIndex() != -1) {
            return true;
        } else if(lstbxSelectStore.getSelectedIndex() != 0 && lstbxSelectStore.getSelectedIndex() != -1) {
            return true;
        }else {
             return false;
        }
    }
    
    private boolean isValid(String s) {
        if (s != null && !s.trim().equals("")) {
            return true;
        } else {
            return false;
        }
    }
    
    private boolean isMultiSelected(IpayListBox multiSelectBox) {
        for (int i = 0, l = multiSelectBox.getItemCount(); i < l; i++) {
            if (multiSelectBox.isItemSelected(i)) {
               return true;
            }
         }
        return false;
    }
    
    public boolean isMeterModelStsOnly() {
        for (int i = 0; i< lstbxMeterModel.getItemCount();i++) {
            if(lstbxMeterModel.isItemSelected(i)) {
                long meterModelId = Long.parseLong(lstbxMeterModel.getItem(i).getExtraInfo());
                if(meterModelId == MeterTypeE.GENERIC.getId()) {
                    return false;
                }
            }
        }
        return true;
    }
    
    private List<Long> getMultiSelected(IpayListBox multiSelectBox) {
        List<Long> selectedValues = new ArrayList<Long>();
        for (int i = 0, l = multiSelectBox.getItemCount(); i < l; i++) {
           if (multiSelectBox.isItemSelected(i)) {
              selectedValues.add(Long.valueOf(multiSelectBox.getValue(i)));
           }
        }
        return selectedValues;
    }

    @Override
    public void populateSearchCriteria(SearchData searchData) {
        populateSearchCriteria(searchData, true);
    }

    public void populateSearchCriteria(SearchData searchData, boolean processExtraMeterFields) {
        if (isValid(meterNumberBox.getText())) {
            searchData.addCriteria(MeterMngStatics.METER_NUMBER_SEARCH, meterNumberBox.getText().trim());
            if (startWithBox.getValue()) {
                searchData.addCriteria(MeterMngStatics.METER_NUMBER_SEARCH_TYPE, SearchType.STARTS_WITH.name());
            } else {
                searchData.addCriteria(MeterMngStatics.METER_NUMBER_SEARCH_TYPE, SearchType.CONTAINS.name());
            }
        }
        if (processExtraMeterFields) {
            List<Long> selectedMeterModels = getMultiSelected(lstbxMeterModel);
            if (selectedMeterModels.size() > 0) {
                searchData.addCriteria(MeterMngStatics.METER_MODEL_ID_SEARCH, selectedMeterModels);
            }
            if(lstbxSgKrn.getSelectedIndex() != 0) {
                searchData.addCriteria(MeterMngStatics.METER_SGC_KRN_SEARCH, lstbxSgKrn.getSelectedValue());
            }
            if(lstbxSelectStore.getSelectedIndex() != 0) {
                searchData.addCriteria(MeterMngStatics.METER_STORE_SEARCH, lstbxSelectStore.getSelectedValue());
            }
            if(chckbxMeterNoUsagePoint.getValue()) {
                searchData.addCriteria(MeterMngStatics.METER_NO_USAGE_POINT_SEARCH, "true");
            }
        }
   }

    @Override
    public void addDefaultKeyHandler(KeyDownHandler handler) {
        meterNumberBox.addKeyDownHandler(handler);
        lstbxMeterModel.addKeyDownHandler(handler);
        chckbxMeterNoUsagePoint.addKeyDownHandler(handler);
    }
    
    private void addHandlers(final AdvancedSearchWorkspaceView advancedSearchWorkspaceView) {
        lstbxMeterModel.addChangeHandler(new ChangeHandler() {
            @Override
            public void onChange(ChangeEvent event) {
                if(isMeterModelStsOnly()) {
                    currSGCElement.setVisible(true);
                    lstbxSgKrn.setVisible(true);
                } else {
                    currSGCElement.setVisible(false);
                    lstbxSgKrn.setVisible(false);
                }
            }
        });
        
        lstbxSgKrn.addChangeHandler(new ChangeHandler() {
            @Override
            public void onChange(ChangeEvent event) {
                int selectedIndex = lstbxSgKrn.getSelectedIndex();
                if (selectedIndex != 0) {
                    if (lstbxMeterModel.getItemCount() > 0) {
                        for(int i=0;i<lstbxMeterModel.getItemCount();i++) {
                            long meterModelId = Long.parseLong(lstbxMeterModel.getItem(i).getExtraInfo());
                            if (meterModelId == MeterTypeE.GENERIC.getId()) {
                                lstbxMeterModel.getItem(i).setSelected(false);
                                lstbxMeterModel.getElement().getElementsByTagName("option").getItem(i).setAttribute("style", "display:none");
                            }
                        }
                    }
                } else {
                    if (lstbxMeterModel.getItemCount() > 0) {
                        for(int i=0;i<lstbxMeterModel.getItemCount();i++) {
                            long meterModelId = Long.parseLong(lstbxMeterModel.getItem(i).getExtraInfo());
                            if (meterModelId == MeterTypeE.GENERIC.getId()) {
                                lstbxMeterModel.getElement().getElementsByTagName("option").getItem(i).removeAttribute("style");
                            }
                        }
                    }
                }
            }
        });

        lstbxSelectStore.addChangeHandler(new ChangeHandler() {
            @Override
            public void onChange(ChangeEvent event) {
                int selectedIndex = lstbxSelectStore.getSelectedIndex();
                if (!lstbxSelectStore.getItemText(selectedIndex).equals("")) {
                    advancedSearchWorkspaceView.getCustomerSearchForm().clear();
                    advancedSearchWorkspaceView.getCustomerSearchForm().disable();
                    advancedSearchWorkspaceView.getUsagePointSearchForm().clear();
                    advancedSearchWorkspaceView.getUsagePointSearchForm().disable();
                    advancedSearchWorkspaceView.getLocationSearchForm().clear();
                    advancedSearchWorkspaceView.getLocationSearchForm().disable();
                    hideCheckbox();
                } else {
                    advancedSearchWorkspaceView.getCustomerSearchForm().enable();
                    advancedSearchWorkspaceView.getUsagePointSearchForm().enable();
                    advancedSearchWorkspaceView.getLocationSearchForm().enable();
                    showCheckbox();
                }  
            }
        });
        
        chckbxMeterNoUsagePoint.addValueChangeHandler(new ValueChangeHandler<Boolean>() {
            @Override
            public void onValueChange(ValueChangeEvent<Boolean> event) {
                if (chckbxMeterNoUsagePoint.getValue()) {
                    advancedSearchWorkspaceView.getCustomerSearchForm().clear();
                    advancedSearchWorkspaceView.getCustomerSearchForm().disable();
                    advancedSearchWorkspaceView.getUsagePointSearchForm().clear();
                    advancedSearchWorkspaceView.getUsagePointSearchForm().disable();
                    advancedSearchWorkspaceView.getLocationSearchForm().clear();
                    advancedSearchWorkspaceView.getLocationSearchForm().disable();
                } else {
                    advancedSearchWorkspaceView.getCustomerSearchForm().enable();
                    advancedSearchWorkspaceView.getUsagePointSearchForm().enable();
                    advancedSearchWorkspaceView.getLocationSearchForm().enable();
                }                
            }
        });
    }
}