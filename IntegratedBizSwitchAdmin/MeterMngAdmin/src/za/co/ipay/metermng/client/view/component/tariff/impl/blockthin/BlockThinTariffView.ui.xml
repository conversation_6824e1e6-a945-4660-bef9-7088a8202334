<!DOCTYPE ui:UiBinder SYSTEM "http://dl.google.com/gwt/DTD/xhtml.ent">
<ui:UiBinder xmlns:ui="urn:ui:com.google.gwt.uibinder"
             xmlns:g="urn:import:com.google.gwt.user.client.ui" 
             xmlns:g2="urn:import:com.google.gwt.user.cellview.client"
             xmlns:t="urn:import:za.co.ipay.gwt.common.client.form"
             xmlns:w="urn:import:za.co.ipay.gwt.common.client.widgets"
             xmlns:p1="urn:import:za.co.ipay.gwt.common.client.workspace"
             xmlns:p2="urn:import:za.co.ipay.metermng.client.view.component.tariff">
	<ui:style>
	</ui:style>
  
  <ui:with field="msg" type="za.co.ipay.metermng.client.i18n.UiMessages" />
  
  <g:HTMLPanel>
    
        <t:FormRowPanel>
            <t:FormElement ui:field="taxElement" debugId="taxElement" labelText="{msg.getTariffTax}:" helpMsg="{msg.getTariffTaxHelp}" required="true">
                <w:PercentageTextBox ui:field="taxBox" debugId="taxBox" />
            </t:FormElement>  
        </t:FormRowPanel>

        <t:FormRowPanel>
            <t:FormElement ui:field="cycleListBoxElement" debugId="cycleListBoxElement" labelText="{msg.getTariffCycle}:" helpMsg="{msg.getTariffCycleHelp}" required="false">
                <g:ListBox ui:field="cycleListBox" debugId="cycleListBox" styleName="gwt-TextBox" />
            </t:FormElement>
            <t:FormElement ui:field="cyclicChargeNameElement" debugId="cyclicChargeNameElement" labelText="{msg.getTariffCyclicCostName}:" helpMsg="{msg.getTariffCyclicCostNameHelp}" required="false">
                <g:TextBox ui:field="cyclicChargeNameBox" debugId="cyclicChargeNameBox" maxLength="40" visibleLength="27" />
            </t:FormElement>
            <t:FormElement ui:field="cyclicChargeElement" debugId="cyclicChargeElement" labelText="{msg.getTariffCost}:" helpMsg="{msg.getTariffCostHelp}" required="false">
                <g:Label ui:field="cyclicChargeCurrencyLabel"></g:Label>
                <t:BigDecimalValueBox ui:field="cyclicChargeBox" debugId="cyclicChargeBox" styleName="gwt-TextBox largeNumericInput" />
            </t:FormElement>
        </t:FormRowPanel>   

        <t:FormRowPanel>
            <t:FormElement ui:field="percentChargeNameElement" debugId="percentChargeNameElement" labelText="{msg.getTariffPercentChargeName}:" helpMsg="{msg.getTariffPercentChargeNameHelp}" required="false">
                <g:TextBox ui:field="percentChargeNameBox" debugId="percentChargeNameBox" maxLength="40" visibleLength="30" />
            </t:FormElement>
            <t:FormElement ui:field="percentChargeElement" debugId="cyclicChargeElement" labelText="{msg.getTariffPercentCharge}:" helpMsg="{msg.getTariffPercentChargeHelp}" required="false">
                <w:PercentageTextBox ui:field="percentChargeBox" debugId="percentChargeBox" />
            </t:FormElement>
        </t:FormRowPanel>
        
        <t:FormRowPanel ui:field="paytypeDiscountFormRowPanel"></t:FormRowPanel>
	
        <t:FormRowPanel>
            <t:FormElement ui:field="blocksElement" debugId="blocksElement" helpMsg="{msg.getBlocksHelp}" labelText="{msg.getBlocks}" required="true">
                <g2:CellTable ui:field="blocksTable" debugId="blocksTable" />
             </t:FormElement>
        </t:FormRowPanel>
        
    </g:HTMLPanel>
	
</ui:UiBinder> 