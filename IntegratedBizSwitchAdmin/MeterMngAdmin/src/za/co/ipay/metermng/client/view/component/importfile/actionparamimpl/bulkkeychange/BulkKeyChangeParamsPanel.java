package za.co.ipay.metermng.client.view.component.importfile.actionparamimpl.bulkkeychange;

import java.util.ArrayList;
import java.util.Date;
import java.util.logging.Logger;

import com.google.gwt.core.client.GWT;
import com.google.gwt.event.dom.client.ChangeEvent;
import com.google.gwt.event.dom.client.ClickEvent;
import com.google.gwt.event.logical.shared.ValueChangeEvent;
import com.google.gwt.event.logical.shared.ValueChangeHandler;
import com.google.gwt.i18n.client.DateTimeFormat;
import com.google.gwt.uibinder.client.UiBinder;
import com.google.gwt.uibinder.client.UiField;
import com.google.gwt.uibinder.client.UiHandler;
import com.google.gwt.user.client.Timer;
import com.google.gwt.user.client.ui.CheckBox;
import com.google.gwt.user.client.ui.Image;
import com.google.gwt.user.client.ui.Label;
import com.google.gwt.user.client.ui.ProvidesResize;
import com.google.gwt.user.client.ui.RadioButton;
import com.google.gwt.user.client.ui.RequiresResize;
import com.google.gwt.user.client.ui.TextBox;
import com.google.gwt.user.client.ui.VerticalPanel;
import com.google.gwt.user.client.ui.Widget;
import za.co.ipay.gwt.common.client.Dialogs;
import za.co.ipay.gwt.common.client.form.FormElement;
import za.co.ipay.gwt.common.client.form.Format.FormatUtil;
import za.co.ipay.gwt.common.client.form.HasDirtyData;
import za.co.ipay.gwt.common.client.form.HasDirtyDataManager;
import za.co.ipay.gwt.common.client.form.LocalOnlyHasDirtyDataManager;
import za.co.ipay.gwt.common.client.handler.FormDataChangeHandler;
import za.co.ipay.gwt.common.client.handler.FormDataClickHandler;
import za.co.ipay.gwt.common.client.handler.FormDataValueChangeHandler;
import za.co.ipay.gwt.common.client.resource.Messages;
import za.co.ipay.gwt.common.client.resource.Messages.MessagesUtil;
import za.co.ipay.gwt.common.client.resource.StyleNames;
import za.co.ipay.gwt.common.client.widgets.IpayDateBox;
import za.co.ipay.gwt.common.client.widgets.IpayListBox;
import za.co.ipay.gwt.common.client.widgets.Message;
import za.co.ipay.gwt.common.client.widgets.StrictDateFormat;
import za.co.ipay.gwt.common.client.workspace.SessionCheckCallback;
import za.co.ipay.gwt.common.shared.LookupListItem;
import za.co.ipay.metermng.client.event.SupplyGroupAddedEvent;
import za.co.ipay.metermng.client.event.SupplyGroupAddedEventHandler;
import za.co.ipay.metermng.client.factory.ClientFactory;
import za.co.ipay.metermng.client.resource.image.MediaResource.MediaResourceUtil;
import za.co.ipay.metermng.client.rpc.callback.ClientCallback;
import za.co.ipay.metermng.client.view.component.importfile.actionparamimpl.ActionParamsComponent;
import za.co.ipay.metermng.client.view.component.meter.EngineeringTokenUserRefPanel;
import za.co.ipay.metermng.datatypes.KeyChangeInstructionE;
import za.co.ipay.metermng.mybatis.generated.model.AppSetting;
import za.co.ipay.metermng.shared.MeterMngStatics;
import za.co.ipay.metermng.shared.dto.BulkParamRecord;
import za.co.ipay.metermng.shared.integration.bulkkeychange.BulkKeyChangeParamRecord;

public class BulkKeyChangeParamsPanel extends ActionParamsComponent implements ProvidesResize, RequiresResize  {

    @UiField Label bulkKeyChangeParamHeading;

    @UiField VerticalPanel bulkKeyChangeParamsPanel;
    @UiField FormElement chckbxUseTargetDataElement;
    @UiField CheckBox chckbxUseTargetData;
    @UiField FormElement supGrCdeElement;
    @UiField IpayListBox lstbxSupGrCde;
    @UiField FormElement tariffIndxElement;
    @UiField TextBox txtbxTariffIndx;

    @UiField(provided=true) EngineeringTokenUserRefPanel engineeringTokenUserRefPanel;

    @UiField FormElement bulkInstrElement;
    @UiField IpayListBox lstbxBulkInstr;

    @UiField FormElement keyChangeAfterDateElement;
    @UiField IpayDateBox dtbxKeyChangeAfterDate;

    @UiField FormElement chckbxOverWriteExistingElement;
    @UiField RadioButton yesOverWriteExisting;
    @UiField RadioButton noOverWriteExisting;
    @UiField Message overWriteExistingFeedBack;

    private BulkKeyChangeParamRecord paramRecord;
    private boolean inputFileUploaded;
    private HasDirtyData hasDirtyData;
    HasDirtyDataManager hasDirtyDataManager;
    StrictDateFormat dateTimeFormat;

    private static Logger logger = Logger.getLogger(BulkKeyChangeParamsPanel.class.getName());

    private static BulkKeyChangeParamsPanelUiBinder uiBinder = GWT.create(BulkKeyChangeParamsPanelUiBinder.class);

    interface BulkKeyChangeParamsPanelUiBinder extends UiBinder<Widget, BulkKeyChangeParamsPanel> {
    }

    public BulkKeyChangeParamsPanel(ClientFactory clientFactory, String fileName, BulkKeyChangeParamRecord bulkKeyChangeParamRecord, boolean inputFileUploaded) {
        super();
        this.clientFactory = clientFactory;
        this.paramRecord = bulkKeyChangeParamRecord;
        this.inputFileUploaded = inputFileUploaded;
        hasDirtyDataManager = new LocalOnlyHasDirtyDataManager();
        hasDirtyData = hasDirtyDataManager.createAndRegisterHasDirtyData();
        engineeringTokenUserRefPanel = new EngineeringTokenUserRefPanel(clientFactory);
        engineeringTokenUserRefPanel.showPanel();
        initWidget(uiBinder.createAndBindUi(this));

        bulkKeyChangeParamHeading.setText(MessagesUtil.getInstance().getMessage("bulk.keychange.header", new String[] {fileName}));
        init();
        addHandlers();
        addFieldHandlers();
        overWriteExistingFeedBack.setType(Message.MESSAGE_TYPE_ERROR);
        overWriteExistingFeedBack.setText(MessagesUtil.getInstance().getMessage("bulk.keychange.overwrite.existing.error"));
        clientFactory.getAppSettingRpc().getAppSettingByKey(MeterMngStatics.APP_SETTING_ENG_TOKEN_USER_REF, new ClientCallback<AppSetting>() {
            @Override
            public void onSuccess(AppSetting result) {
                engineeringTokenUserRefPanel.updateEngineeringTokenUserRefStatus(result);
                populateSupGrCdeListBox();
            }
        });

    }

    private void init() {
        dateTimeFormat = new StrictDateFormat(DateTimeFormat.getFormat(FormatUtil.getInstance().getDateTimeFormat()));
        dtbxKeyChangeAfterDate.setFormat(dateTimeFormat);
        keyChangeAfterDateElement.setVisible(false);
        yesOverWriteExisting.setValue(false);
        noOverWriteExisting.setValue(false);
    }

    private void populateSupGrCdeListBox() {
        lstbxSupGrCde.clear();
        clientFactory.getLookupRpc().getSgKrnLookupList(true, new ClientCallback<ArrayList<LookupListItem>>() {
            @Override
            public void onSuccess(ArrayList<LookupListItem> result) {
                lstbxSupGrCde.setLookupItems(result);
                //do not prepopulate. If useTarget is selected, then don't need it, but CAN still have it as well
                populateBulkInstruction();
            }
        });
    }

    private void populateBulkInstruction() {
        lstbxBulkInstr.clearAll();
        Messages msgs = MessagesUtil.getInstance();
        lstbxBulkInstr.addItem("", "");
        lstbxBulkInstr.addItem(msgs.getMessage("bulk.keychange.instruction.generate.keychanges.now"), String.valueOf(KeyChangeInstructionE.GENERATENOW.getId()));
        lstbxBulkInstr.addItem(msgs.getMessage("bulk.keychange.instruction.generate.keychanges.next.vend"), String.valueOf(KeyChangeInstructionE.SETFORNEXTVEND.getId()));
        lstbxBulkInstr.addItem(msgs.getMessage("bulk.keychange.instruction.generate.keychanges.next.vend.after.date"), String.valueOf(KeyChangeInstructionE.SETNEXTVENDAFTERDATE.getId()));
        mapDataToForm();
    }

    @UiHandler("chckbxUseTargetData")
    void handleChckbxUseTargetData(ClickEvent event) {
        supGrCdeElement.setRequired(!chckbxUseTargetData.getValue());
        if (chckbxUseTargetData.getValue()) {
            Dialogs.centreMessage(MessagesUtil.getInstance().getMessage("bulk.keychange.use.target.selected.message"),
                    new Image(MediaResourceUtil.getInstance().getInformationIcon()),
                    StyleNames.POPUP_MESSAGE,
                    MessagesUtil.getInstance().getMessage("button.close"), null,
                    false, true);
        }
    }

    @UiHandler("txtbxTariffIndx")
    void handleCurrTIChange(ChangeEvent event) {
        if (txtbxTariffIndx.getText().trim().length()==1) {
            txtbxTariffIndx.setText("0"+txtbxTariffIndx.getText().trim());
        }
    }

    @UiHandler("lstbxBulkInstr")
    void handleBulkInstrChange(ChangeEvent event) {
        if (lstbxBulkInstr.getValue(lstbxBulkInstr.getSelectedIndex()).equals(String.valueOf(KeyChangeInstructionE.SETNEXTVENDAFTERDATE.getId()))) {
            keyChangeAfterDateElement.setVisible(true);
        } else {
            dtbxKeyChangeAfterDate.setValue(null);
            keyChangeAfterDateElement.setVisible(false);
        }
    }

    @SuppressWarnings({ "rawtypes", "unchecked" })
    private void addFieldHandlers() {
        chckbxUseTargetData.addClickHandler(new FormDataClickHandler(hasDirtyData));
        lstbxSupGrCde.addChangeHandler(new FormDataChangeHandler(hasDirtyData));
        txtbxTariffIndx.addChangeHandler(new FormDataChangeHandler(hasDirtyData));
        lstbxBulkInstr.addChangeHandler(new FormDataChangeHandler(hasDirtyData));
        engineeringTokenUserRefPanel.getTxtbxEngineeringTokenUserRef().addChangeHandler(new FormDataChangeHandler(hasDirtyData));
        dtbxKeyChangeAfterDate.addValueChangeHandler(new FormDataValueChangeHandler(hasDirtyData));
        dtbxKeyChangeAfterDate.addValueChangeHandler(new ValueChangeHandler<Date>() {
            @Override
            public void onValueChange(ValueChangeEvent<Date> event) {
                hasDirtyData.setDirtyData(true);
                keyChangeAfterDateElement.clearErrorMsg();
            }
        });
        yesOverWriteExisting.addClickHandler(new FormDataClickHandler(hasDirtyData));
        noOverWriteExisting.addClickHandler(new FormDataClickHandler(hasDirtyData));
    }

    private void addHandlers() {
        clientFactory.getEventBus().addHandler(SupplyGroupAddedEvent.TYPE, new SupplyGroupAddedEventHandler() {
            @Override
            public void processSupplyGroupAddedEvent(SupplyGroupAddedEvent event) {
                SessionCheckCallback sessionCheckCallback = new SessionCheckCallback() {
                    @Override
                    public void callback(SessionCheckResolution resolution) {
                        populateSupGrCdeListBox();
                    }
                };
                clientFactory.handleSessionCheckCallback(sessionCheckCallback);
            }
        });
    }

    @Override
    public void mapDataToForm() {
        clearErrorMessages();

        if (paramRecord != null) {

            //-------------  TO params ----------------------------------------
            chckbxUseTargetData.setValue(paramRecord.isUseTarget());

            if (paramRecord.getSupplyGroupId() != null) {
                lstbxSupGrCde.selectItemByValue(paramRecord.getSupplyGroupId().toString());
            }
            if (paramRecord.getTariffIndex()==null) {
                txtbxTariffIndx.setText("01");
            } else {
                txtbxTariffIndx.setText(paramRecord.getTariffIndex());
            }

            if (paramRecord.getTokenUserRef() != null) {
                engineeringTokenUserRefPanel.setUserReferenceValue(paramRecord.getTokenUserRef());
            }

            if (paramRecord.getBulkInstruction() != null) {
                lstbxBulkInstr.selectItemByValue(String.valueOf(paramRecord.getBulkInstruction().getId()));
            }

            Date pDate = paramRecord.getKeyChangeAfterDate();
            if (paramRecord.getBulkInstruction() != null
                    && paramRecord.getBulkInstruction().equals(KeyChangeInstructionE.SETNEXTVENDAFTERDATE)) {
                keyChangeAfterDateElement.setVisible(true);
                if (pDate != null) {
                    dtbxKeyChangeAfterDate.setValue(pDate);
                }
            } else {
                dtbxKeyChangeAfterDate.setValue(null);
                keyChangeAfterDateElement.setVisible(false);
            }

            if (paramRecord.getOverWriteExistingNewSGC() != null) {
                if (paramRecord.getOverWriteExistingNewSGC()) {
                    yesOverWriteExisting.setValue(true);
                    noOverWriteExisting.setValue(false);
                } else {
                    yesOverWriteExisting.setValue(false);
                    noOverWriteExisting.setValue(true);
                }
            } else {
                yesOverWriteExisting.setValue(false);
                noOverWriteExisting.setValue(false);
            }
        }
        supGrCdeElement.setRequired(!chckbxUseTargetData.getValue());
    }

    private void clearErrorMessages() {
        supGrCdeElement.clearErrorMsg();
        tariffIndxElement.clearErrorMsg();
        engineeringTokenUserRefPanel.clearErrorMessage();
        bulkInstrElement.clearErrorMsg();
        keyChangeAfterDateElement.clearErrorMsg();
        overWriteExistingFeedBack.setVisible(false);
    }

    @Override
    public boolean validateForm() {
        clearErrorMessages();
        boolean isValidated = true;

        //-------------  TO params ----------------------------------------
        if (!chckbxUseTargetData.getValue()
                && (lstbxSupGrCde.getValue((lstbxSupGrCde.getSelectedIndex())).equals("-1")
                        || lstbxSupGrCde.getValue((lstbxSupGrCde.getSelectedIndex())).isEmpty()) ) {
            supGrCdeElement.setErrorMsg(MessagesUtil.getInstance().getMessage("bulk.keychange.supplygroupcode.error.required.no.target"));
            isValidated = false;
        }

        if (!lstbxSupGrCde.getValue((lstbxSupGrCde.getSelectedIndex())).equals("-1")) {
            //check that not expired

        }

        if (txtbxTariffIndx.getText() == null || txtbxTariffIndx.getText().isEmpty()) {
            tariffIndxElement.setErrorMsg(MessagesUtil.getInstance().getMessage("bulk.keychange.tariffindex.required"));
            isValidated = false;
        }

        if (!engineeringTokenUserRefPanel.validateFormField()) {
            isValidated = false;
        }

        if (lstbxBulkInstr.getSelectedIndex() < 1) {
            bulkInstrElement.setErrorMsg(MessagesUtil.getInstance().getMessage("error.field.is.required",
                    new String[] { bulkInstrElement.getLabelText() }));
            isValidated = false;
        }

        if (lstbxBulkInstr.getValue(lstbxBulkInstr.getSelectedIndex()).equals(String.valueOf(KeyChangeInstructionE.SETNEXTVENDAFTERDATE.getId()))) {
            if (dtbxKeyChangeAfterDate.getValue() == null || dtbxKeyChangeAfterDate.getValue().toString().isEmpty()) {
                keyChangeAfterDateElement.setErrorMsg(MessagesUtil.getInstance().getMessage("bulk.keychange.after.date.required"));
                isValidated = false;
            } else {
                Date now = new Date();
                if (dtbxKeyChangeAfterDate.getValue().before(now)) {
                    keyChangeAfterDateElement.setErrorMsg(MessagesUtil.getInstance().getMessage("bulk.keychange.after.date.error",
                            new String[] { dateTimeFormat.getDateTimeFormat().format(now) }));
                    isValidated = false;
                }
            }
        }

        if (!yesOverWriteExisting.getValue() && !noOverWriteExisting.getValue()) {
            //chckbxOverWriteExistingElement.setErrorMsg(MessagesUtil.getInstance().getMessage("bulk.keychange.overwrite.existing.error"));
            overWriteExistingFeedBack.setVisible(true);
            isValidated = false;
        }

        return isValidated;
    }

    @Override
    public BulkParamRecord mapFormToData() {

        paramRecord = new BulkKeyChangeParamRecord();

        //-------------  TO params ----------------------------------------
        paramRecord.setUseTarget(chckbxUseTargetData.getValue());

        if (lstbxSupGrCde.getSelectedIndex() > 0) {
            paramRecord.setSupplyGroupId(Long.valueOf(lstbxSupGrCde.getSelectedValues().get(0)));
            LookupListItem lli = lstbxSupGrCde.getItem(lstbxSupGrCde.getListBox().getSelectedIndex());
            if (lli.getExtraInfo() != null) {
                String[] lliA = lli.getExtraInfo().split(":");
                for (int i = 0; i < lliA.length; i++) {
                    if (lliA[i].equals("SGC")) {
                        paramRecord.setSupplyGroupCode(lliA[i+1]);
                    } else if(lliA[i].equals("KRN")) {
                        paramRecord.setKeyRevisionNum(Integer.parseInt(lliA[i+1]));
                    }
                }
            }
        }

        String tich = txtbxTariffIndx.getText().trim();
        if (tich.length()==1) {
            tich = ("0"+tich);
        }
        paramRecord.setTariffIndex(tich);

        paramRecord.setTokenUserRef(engineeringTokenUserRefPanel.getUserReferenceValue());
        paramRecord.setBulkInstruction(KeyChangeInstructionE.fromId(Long.valueOf(lstbxBulkInstr.getValue(lstbxBulkInstr.getSelectedIndex()))));

        if (paramRecord.getBulkInstruction().equals(KeyChangeInstructionE.SETNEXTVENDAFTERDATE)) {
            paramRecord.setKeyChangeAfterDate(dtbxKeyChangeAfterDate.getValue());
        }

        if (yesOverWriteExisting.getValue()) {
            paramRecord.setOverWriteExistingNewSGC(Boolean.TRUE);
        } else {
            //already validated that one of the options must be selected
            paramRecord.setOverWriteExistingNewSGC(Boolean.FALSE);
        }

        return paramRecord;
    }

    public boolean checkDirtyData() {
       return hasDirtyData.isDirtyData();
    }

    @Override
    public void onResize() {
        new Timer() {
            @Override
            public void run() {
                bulkKeyChangeParamsPanel.setWidth("100%");
            }
        }.schedule(100);
    }
}
