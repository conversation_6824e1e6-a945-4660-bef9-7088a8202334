<!DOCTYPE ui:UiBinder SYSTEM "http://dl.google.com/gwt/DTD/xhtml.ent">
<ui:UiBinder xmlns:ui="urn:ui:com.google.gwt.uibinder"
             xmlns:g="urn:import:com.google.gwt.user.client.ui" 
             xmlns:g2="urn:import:com.google.gwt.user.cellview.client"
             xmlns:t="urn:import:za.co.ipay.gwt.common.client.form"
             xmlns:w="urn:import:za.co.ipay.gwt.common.client.widgets"
             xmlns:p1="urn:import:za.co.ipay.gwt.common.client.workspace">
	<ui:style>
	</ui:style>
  
  <ui:with field="msg" type="za.co.ipay.metermng.client.i18n.UiMessages" />
  
    <t:FormGroupPanel labelText="{msg.getTariffUnitChargeTitle}">
        <t:FormRowPanel>
            <t:FormElement ui:field="unitChargeNameElement" debugId="unitChargeNameElement" labelText="{msg.getTariffUnitChargeName}:" helpMsg="{msg.getTariffUnitChargeNameHelp}" required="false">
                <g:TextBox ui:field="unitChargeNameBox" debugId="unitChargeNameBox" maxLength="40" visibleLength="30" />
            </t:FormElement>
            <t:FormElement ui:field="isPercentElement" debugId="isPercentElement" labelText="{msg.getTariffUnitChargeIsPercent}:" helpMsg="{msg.getTariffUnitChargeIsPercentHelp}" required="false">
                <g:Label ui:field="isPercentLabel"></g:Label>
                <g:CheckBox checked="false" ui:field="isPercentCheckBox" debugId="isPercentCheckBox"/>
            </t:FormElement>
            <t:FormElement ui:field="isTaxableElement" debugId="isTaxableElement" labelText="{msg.getTariffUnitChargeIsTaxable}:" helpMsg="{msg.getTariffUnitChargeIsTaxableHelp}" required="false">
                <g:Label ui:field="isTaxableLabel"></g:Label>
                <g:CheckBox checked="true" ui:field="isTaxableCheckBox" debugId="isTaxableCheckBox"/>
            </t:FormElement>
            <t:FormElement ui:field="unitChargeElement" debugId="cyclicChargeElement" labelText="{msg.getTariffUnitCharge}:" helpMsg="{msg.getTariffUnitChargeHelp}" required="false">
                <g:Label ui:field="unitChargeCurrencyLabel"></g:Label>
                <t:BigDecimalValueBox ui:field="unitChargeBox" debugId="unitChargeBox" styleName="gwt-TextBox largeNumericInput" />
            </t:FormElement>
            <t:FormElement visible="false" ui:field="unitChargePercentElement" debugId="unitChargePercentElement" labelText="{msg.getTariffUnitChargePercent}:" helpMsg="{msg.getTariffUnitChargePercentHelp}" required="false">
                <w:PercentageTextBox ui:field="unitChargePercentBox" debugId="unitChargePercentBox" />
            </t:FormElement>
        </t:FormRowPanel>
    </t:FormGroupPanel>
	
</ui:UiBinder> 