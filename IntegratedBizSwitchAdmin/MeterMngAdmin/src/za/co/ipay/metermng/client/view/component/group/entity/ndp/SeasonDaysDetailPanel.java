package za.co.ipay.metermng.client.view.component.group.entity.ndp;

import java.util.ArrayList;
import java.util.Comparator;
import java.util.HashSet;
import java.util.List;
import java.util.Set;
import java.util.logging.Logger;

import com.google.gwt.cell.client.Cell.Context;
import com.google.gwt.cell.client.ImageResourceCell;
import com.google.gwt.core.client.GWT;
import com.google.gwt.dom.client.Element;
import com.google.gwt.dom.client.NativeEvent;
import com.google.gwt.event.dom.client.BlurEvent;
import com.google.gwt.event.dom.client.BlurHandler;
import com.google.gwt.event.dom.client.ChangeEvent;
import com.google.gwt.event.dom.client.ClickEvent;
import com.google.gwt.event.dom.client.FocusEvent;
import com.google.gwt.event.dom.client.FocusHandler;
import com.google.gwt.event.dom.client.KeyUpEvent;
import com.google.gwt.event.dom.client.KeyUpHandler;
import com.google.gwt.i18n.client.DateTimeFormat;
import com.google.gwt.resources.client.ImageResource;
import com.google.gwt.uibinder.client.UiBinder;
import com.google.gwt.uibinder.client.UiField;
import com.google.gwt.uibinder.client.UiHandler;
import com.google.gwt.user.cellview.client.CellTable;
import com.google.gwt.user.cellview.client.Column;
import com.google.gwt.user.cellview.client.ColumnSortEvent;
import com.google.gwt.user.cellview.client.ColumnSortEvent.ListHandler;
import com.google.gwt.user.cellview.client.ColumnSortList;
import com.google.gwt.user.cellview.client.TextColumn;
import com.google.gwt.user.client.ui.Button;
import com.google.gwt.user.client.ui.FocusWidget;
import com.google.gwt.user.client.ui.Focusable;
import com.google.gwt.user.client.ui.HTML;
import com.google.gwt.user.client.ui.Label;
import com.google.gwt.user.client.ui.ListBox;
import com.google.gwt.user.client.ui.TextBox;
import com.google.gwt.user.client.ui.Widget;
import com.google.gwt.user.datepicker.client.CalendarModel;
import com.google.gwt.view.client.ListDataProvider;

import za.co.ipay.gwt.common.client.Dialogs;
import za.co.ipay.gwt.common.client.factory.ResourcesFactoryUtil;
import za.co.ipay.gwt.common.client.form.FormElement;
import za.co.ipay.gwt.common.client.form.FormRowPanel;
import za.co.ipay.gwt.common.client.form.HasDirtyData;
import za.co.ipay.gwt.common.client.handler.ConfirmHandler;
import za.co.ipay.gwt.common.client.handler.FormDataChangeHandler;
import za.co.ipay.gwt.common.client.resource.Messages.MessagesUtil;
import za.co.ipay.gwt.common.client.widgets.TablePager;
import za.co.ipay.gwt.common.client.workspace.SessionCheckCallback;
import za.co.ipay.metermng.client.factory.ClientFactory;
import za.co.ipay.metermng.client.resource.image.MediaResource.MediaResourceUtil;
import za.co.ipay.metermng.client.rpc.callback.ClientCallback;
import za.co.ipay.metermng.client.view.component.BaseComponent;
import za.co.ipay.metermng.datatypes.RecordStatus;
import za.co.ipay.metermng.mybatis.generated.model.NdpDayProfile;
import za.co.ipay.metermng.mybatis.generated.model.NdpSeason;
import za.co.ipay.metermng.shared.NdpDayProfileData;
import za.co.ipay.metermng.shared.NdpScheduleData;

public class SeasonDaysDetailPanel extends BaseComponent {

    private static final int DEFAULT_PAGE_SIZE = 15;

    @UiField ListBox startDayListBox;
    @UiField ListBox startMonthListBox;
    @UiField ListBox endDayListBox;
    @UiField ListBox endMonthListBox;
    @UiField Label errorMsgSeason;

    @UiField ListBox daysOfWeekBox;
    @UiField FormElement startTimeElement;
    @UiField TextBox txtbxStartHour;
    @UiField TextBox txtbxStartMinute;
    @UiField FormElement endTimeElement;
    @UiField TextBox txtbxEndHour;
    @UiField TextBox txtbxEndMinute;
    @UiField Button btnAddSeasonDayTimes;

    @UiField Label errorMsgTimes;

    @UiField CellTable<NdpDayProfileData> ndpDaysTable;
    @UiField TablePager ndpDaysPager;

    @UiField FormRowPanel dowPanel;
    @UiField FormRowPanel enterTimesPanel;
    @UiField FormRowPanel btnPanel;
    @UiField Button btnSave;
    @UiField Button btnCancel;
    @UiField HTML seasonDayDescrip;

    private ListDataProvider<NdpDayProfileData> dataProvider;

    private CalendarModel calmod;
    private ListHandler<NdpDayProfileData> columnSortHandler;
    private ColumnSortList columnSortList;

    private TextColumn<NdpDayProfileData> dayOfWeek;
    private TextColumn<NdpDayProfileData> startColumn;
    private TextColumn<NdpDayProfileData> endColumn;
    private Column<NdpDayProfileData, ImageResource> deleteColumn;

    private SchedulePanel parentSchedulePanel;
    private NdpScheduleData ndpScheduleData;
    private Boolean isInherited = false;
    private Boolean isViewOnly = false;
    private NdpSeason ndpSeason;
    private ArrayList<NdpDayProfileData> deleteTheseDayProfiles = new ArrayList<NdpDayProfileData>();
    private Boolean isDeactivateSchedule = false;

    DateTimeFormat daysOfWeekFormat = DateTimeFormat.getFormat("EEE");
    private static final String[] dayOfWeekNames = new String[7];
    private StringBuilder sb;

    private HasDirtyData hasDirtyData;

    private static Logger logger = Logger.getLogger(SeasonDaysDetailPanel.class.getName());

    private static NdpSeasonDaysPanelUiBinder uiBinder = GWT.create(NdpSeasonDaysPanelUiBinder.class);

    interface NdpSeasonDaysPanelUiBinder extends UiBinder<Widget, SeasonDaysDetailPanel> {
    }

    public SeasonDaysDetailPanel(ClientFactory clientFactory, SchedulePanel parentSchedulePanel, NdpScheduleData ndpScheduleData, Boolean isInherited, Boolean isViewOnly) {
        this.clientFactory = clientFactory;
        this.parentSchedulePanel = parentSchedulePanel;
        this.ndpScheduleData = ndpScheduleData;
        this.isInherited = isInherited;
        this.isViewOnly = isViewOnly;

        calmod = new CalendarModel();
        createTable();

        initWidget(uiBinder.createAndBindUi(this));

        // manage dirty
        hasDirtyData = parentSchedulePanel.getHasDirtyDataManager().createAndRegisterHasDirtyData();

        initTable();
        initDaysOfWeek();
        populateListBoxes();
        addFieldHandlers();

        daysOfWeekBox.addFocusHandler(new FocusHandler() {
            @Override
            public void onFocus(FocusEvent event) {
                txtbxStartHour.setText("");
                txtbxStartMinute.setText("");
                txtbxEndHour.setText("");
                txtbxEndMinute.setText("");
                errorMsgTimes.setText("");
                errorMsgTimes.setVisible(false);
                btnAddSeasonDayTimes.setEnabled(true);
            }
        });

        txtbxStartHour.addFocusHandler(new FocusHandler() {
            @Override
            public void onFocus(FocusEvent event) {
                txtbxStartHour.setText("");
                errorMsgTimes.setText("");
                errorMsgTimes.setVisible(false);
                btnAddSeasonDayTimes.setEnabled(true);
            }
        });
        txtbxStartMinute.addFocusHandler(new FocusHandler() {
            @Override
            public void onFocus(FocusEvent event) {
                txtbxStartMinute.setText("");
                errorMsgTimes.setText("");
                errorMsgTimes.setVisible(false);
                btnAddSeasonDayTimes.setEnabled(true);
            }
        });
        txtbxEndHour.addFocusHandler(new FocusHandler() {
            @Override
            public void onFocus(FocusEvent event) {
                txtbxEndHour.setText("");
                errorMsgTimes.setText("");
                errorMsgTimes.setVisible(false);
                btnAddSeasonDayTimes.setEnabled(true);
            }
        });
        txtbxEndMinute.addFocusHandler(new FocusHandler() {
            @Override
            public void onFocus(FocusEvent event) {
                txtbxEndMinute.setText("");
                errorMsgTimes.setText("");
                errorMsgTimes.setVisible(false);
                btnAddSeasonDayTimes.setEnabled(true);
            }
        });

        txtbxStartHour.addBlurHandler(new BlurHandler() {
            @Override
            public void onBlur(BlurEvent event) {
                if (txtbxStartHour.getText().trim().length()==1) {
                    txtbxStartHour.setText("0"+txtbxStartHour.getText().trim());
                }
            }
        });
        txtbxStartHour.addKeyUpHandler(new KeyUpHandler() {
            @Override
            public void onKeyUp(KeyUpEvent event) {
                handleHourBox(txtbxStartHour, txtbxStartMinute);
            }

        });

        txtbxStartMinute.addBlurHandler(new BlurHandler() {
            @Override
            public void onBlur(BlurEvent event) {
                if (txtbxStartMinute.getText().trim().length()==1) {
                    txtbxStartMinute.setText("0"+txtbxStartMinute.getText().trim());
                }
            }
        });
        txtbxStartMinute.addKeyUpHandler(new KeyUpHandler() {
            @Override
            public void onKeyUp(KeyUpEvent event) {
               handleMinuteBox(txtbxStartMinute, txtbxEndHour);
            }

        });

        txtbxEndHour.addBlurHandler(new BlurHandler() {
            @Override
            public void onBlur(BlurEvent event) {
                if (txtbxEndHour.getText().trim().length()==1) {
                    txtbxEndHour.setText("0"+txtbxEndHour.getText().trim());
                }
            }
        });
        txtbxEndHour.addKeyUpHandler(new KeyUpHandler() {

            @Override
            public void onKeyUp(KeyUpEvent event) {
                handleHourBox(txtbxEndHour, txtbxEndMinute);
            }

        });

        txtbxEndMinute.addBlurHandler(new BlurHandler() {
            @Override
            public void onBlur(BlurEvent event) {
                if (txtbxEndMinute.getText().trim().length()==1) {
                    txtbxEndMinute.setText("0"+txtbxEndMinute.getText().trim());
                }
            }
        });
        txtbxEndMinute.addKeyUpHandler(new KeyUpHandler() {
            @Override
            public void onKeyUp(KeyUpEvent event) {
               handleMinuteBox(txtbxEndMinute, btnAddSeasonDayTimes);
            }

        });

    }

    public void addFieldHandlers() {
        startDayListBox.addChangeHandler(new FormDataChangeHandler(hasDirtyData));
        startMonthListBox.addChangeHandler(new FormDataChangeHandler(hasDirtyData));
        endDayListBox.addChangeHandler(new FormDataChangeHandler(hasDirtyData));
        endMonthListBox.addChangeHandler(new FormDataChangeHandler(hasDirtyData));
        daysOfWeekBox.addChangeHandler(new FormDataChangeHandler(hasDirtyData));

        txtbxStartMinute.addChangeHandler(new FormDataChangeHandler(hasDirtyData));
        txtbxStartHour.addChangeHandler(new FormDataChangeHandler(hasDirtyData));
        txtbxEndMinute.addChangeHandler(new FormDataChangeHandler(hasDirtyData));
        txtbxEndHour.addChangeHandler(new FormDataChangeHandler(hasDirtyData));
    }


    private void handleHourBox(TextBox hour, FocusWidget nextFocus) {
        boolean accept = true;
        String txt = hour.getText();
        try {
            if (txt.length()==1) {
                if (Integer.valueOf(txt) < 0 || Integer.valueOf(txt) > 9) {
                    accept = false;
                }
            } else {
                if (Integer.valueOf(txt) < 0 || Integer.valueOf(txt) > 23) {
                    accept = false;
                }
            }

        } catch (NumberFormatException nfe) {
            accept = false;
        }
        if (!accept) {
            hour.setText(txt.substring(0,txt.length()-1));
        } else {
            if (txt.length()==2) {
                nextFocus.setFocus(true);
            }
        }
    }

    private void handleMinuteBox(TextBox minuteBox, Focusable nextFocus) {
        boolean accept = true;
        String txt = minuteBox.getText();
        try {
            if (txt.length()==1) {
                if (Integer.valueOf(txt) < 0 || Integer.valueOf(txt) > 5) {
                    accept = false;
                }
            } else {
                if (Integer.valueOf(txt) < 0 || Integer.valueOf(txt) > 59) {
                    accept = false;
                }
            }

        } catch (NumberFormatException nfe) {
            accept = false;
        }
        if (!accept) {
            minuteBox.setText(txt.substring(0,txt.length()-1));
        } else {
            if (txt.length()==2) {
                nextFocus.setFocus(true);
            }
        }
    }

    private void createTable() {
        if (ResourcesFactoryUtil.getInstance() != null && ResourcesFactoryUtil.getInstance().getCellTableResources() != null) {
            ndpDaysTable = new CellTable<NdpDayProfileData>(DEFAULT_PAGE_SIZE, ResourcesFactoryUtil.getInstance().getCellTableResources());
        } else {
            ndpDaysTable = new CellTable<NdpDayProfileData>(DEFAULT_PAGE_SIZE);
        }
    }

   private void initTable() {
        if (dataProvider == null) {

            dayOfWeek = new TextColumn<NdpDayProfileData>() {
                @Override
                public String getValue(NdpDayProfileData data) {
                    return dayOfWeekNames[data.getNdpDayProfile().getDayOfWeek() - 1];
                }
            };

            startColumn = new TextColumn<NdpDayProfileData>() {
                @Override
                public String getValue(NdpDayProfileData data) {
                    if (data.getNdpDayProfile().getStartHour() == null || data.getNdpDayProfile().getStartMinute() == null) {
                        return null;
                    }
                    return prefixWithZero(data.getNdpDayProfile().getStartHour()) + ":" + prefixWithZero(data.getNdpDayProfile().getStartMinute());
                }


            };
            startColumn.setSortable(true);

            endColumn = new TextColumn<NdpDayProfileData>() {
                @Override
                public String getValue(NdpDayProfileData data) {
                    if (data.getNdpDayProfile().getEndHour() == null || data.getNdpDayProfile().getEndMinute() == null) {
                        return null;
                    }
                    return prefixWithZero(data.getNdpDayProfile().getEndHour()) + ":" + prefixWithZero(data.getNdpDayProfile().getEndMinute());
                }
            };
            endColumn.setSortable(true);

            ImageResourceCell deleteCell = new ImageResourceCell() {
                public Set<String> getConsumedEvents() {
                    HashSet<String> events = new HashSet<String>();
                    events.add("click");
                    return events;
                }
            };

            deleteColumn = new Column<NdpDayProfileData, ImageResource>(deleteCell) {
                @Override
                public ImageResource getValue(NdpDayProfileData dataObj) {
                    return MediaResourceUtil.getInstance().getDeleteImage();
                }

                @Override
                public void onBrowserEvent(Context context, Element elem,
                        final NdpDayProfileData object, NativeEvent event) {
                    super.onBrowserEvent(context, elem, object, event);
                    if ("click".equals(event.getType())) {
                        SessionCheckCallback sessionCheckCallback = new SessionCheckCallback() {
                            @Override
                            public void callback(SessionCheckResolution resolution) {
                                deleteDayProfile(object);
                            }
                        };
                        clientFactory.handleSessionCheckCallback(sessionCheckCallback);
                    }
                }
            };

            // Add the columns.
            ndpDaysTable.addColumn(dayOfWeek, "");
            ndpDaysTable.addColumn(startColumn, MessagesUtil.getInstance().getMessage("ndp.assign.dayperiod.start"));
            ndpDaysTable.addColumn(endColumn, MessagesUtil.getInstance().getMessage("ndp.assign.dayperiod.end"));
            ndpDaysTable.addColumn(deleteColumn, "");

            dataProvider = new ListDataProvider<NdpDayProfileData>();
            dataProvider.addDataDisplay(ndpDaysTable);
            ndpDaysPager.setDisplay(ndpDaysTable);
            ndpDaysTable.setPageSize(getPageSize());

            logger.info("Created Ndp Seasons Day Table");
        }
    }

   public void sortDataProviderList() {
       dataProvider.refresh();
       if (columnSortHandler == null || columnSortHandler.getList() == null) {
           columnSortHandler = new ListHandler<NdpDayProfileData>(dataProvider.getList());

           columnSortHandler.setComparator(dayOfWeek, new Comparator<NdpDayProfileData>() {
               public int compare(NdpDayProfileData o1, NdpDayProfileData o2) {
                   if (o1 == o2) {
                       return 0;
                   }
                   if (o1 == null) return -1;
                   if (o2 == null) return 1;

                   int result = o1.getNdpDayProfile().getDayOfWeek().compareTo(o2.getNdpDayProfile().getDayOfWeek());
                   if (result != 0) return result;

                   result = o1.getNdpDayProfile().getStartHour().compareTo(o2.getNdpDayProfile().getStartHour());
                   if (result != 0) return result;

                   return o1.getNdpDayProfile().getEndMinute().compareTo(o2.getNdpDayProfile().getEndMinute());
               }
           });

           ndpDaysTable.addColumnSortHandler(columnSortHandler);
           columnSortList = ndpDaysTable.getColumnSortList();
           columnSortList.push(dayOfWeek);
           ColumnSortEvent.fire(ndpDaysTable, columnSortList);
       } else {
           columnSortHandler.setList(dataProvider.getList());
           ColumnSortEvent.fire(ndpDaysTable, columnSortList);
       }
   }


   private String prefixWithZero (Integer i) {
       if (i < 10) {
           sb = new StringBuilder("0");
           sb.append(String.valueOf(i));
           return sb.toString();
       } else {
           return String.valueOf(i);
       }
   }

   private void initDaysOfWeek() {
       daysOfWeekBox.clear();
       String[] dayNamesArray = MessagesUtil.getInstance().getMessage("ndp.weekdays").split(",");
       for (int i=0; i<7; i++) {
           daysOfWeekBox.addItem(dayNamesArray[i], Integer.toString(i+1));
           dayOfWeekNames[i] = dayNamesArray[i];
       }
   }

   protected void populateListBoxes() {
       startMonthListBox.clear();
       startDayListBox.clear();
       endDayListBox.clear();
       endMonthListBox.clear();

       for (int i=0; i<12; i++) {
               startMonthListBox.addItem(calmod.formatMonth(i), String.valueOf(i+1));
               endMonthListBox.addItem(calmod.formatMonth(i), String.valueOf(i+1));
       }
       for (int i=1; i<32; i++) {
           startDayListBox.addItem(String.valueOf(i), String.valueOf(i));
           endDayListBox.addItem(String.valueOf(i), String.valueOf(i));
       }

       startMonthListBox.setSelectedIndex(0);
       startDayListBox.setSelectedIndex(0);
       endMonthListBox.setSelectedIndex(0);
       endDayListBox.setSelectedIndex(0);
       daysOfWeekBox.setSelectedIndex(0);

       txtbxStartHour.setText("");
       txtbxStartMinute.setText("");
       txtbxEndHour.setText("");
       txtbxEndMinute.setText("");
   }

    public void display(NdpSeason ndpSeason) {
        this.ndpSeason = ndpSeason;

        daysOfWeekBox.setSelectedIndex(0);
        txtbxStartHour.setText("");
        txtbxStartMinute.setText("");
        txtbxEndHour.setText("");
        txtbxEndMinute.setText("");

        if (ndpSeason != null) {
            btnSave.setText(MessagesUtil.getInstance().getMessage("button.update"));
            startMonthListBox.setSelectedIndex(ndpSeason.getStartMonth() - 1);
            startDayListBox.setSelectedIndex(ndpSeason.getStartDay() - 1);

            endMonthListBox.setSelectedIndex(ndpSeason.getEndMonth() - 1);
            endDayListBox.setSelectedIndex(ndpSeason.getEndDay() - 1);

            if (isInherited || isViewOnly) {
                setupInherited();
            }

            clientFactory.getNdpRpc().getNdpSeasonDayProfilesData(ndpSeason.getId(), new ClientCallback<ArrayList<NdpDayProfileData>>() {
                @Override
                public void onSuccess(ArrayList<NdpDayProfileData> result) {
                    dataProvider.getList().clear();
                    dataProvider.getList().addAll(result);
                    sortDataProviderList();
                }
            });
        } else {
            btnSave.setText(MessagesUtil.getInstance().getMessage("button.create"));
            startMonthListBox.setSelectedIndex(0);
            startDayListBox.setSelectedIndex(0);

            endMonthListBox.setSelectedIndex(0);
            endDayListBox.setSelectedIndex(0);

            if (isInherited) {             //if nothing to inherit!!  .i.e. no Global!!
                setupInherited();
            }

            dataProvider.getList().clear();
            dataProvider.refresh();
        }
    }

    private void disableDates() {
        startMonthListBox.setEnabled(false);
        startDayListBox.setEnabled(false);
        endMonthListBox.setEnabled(false);
        endDayListBox.setEnabled(false);
    }

    protected void setupInherited() {
        disableDates();
        seasonDayDescrip.removeFromParent();
        dowPanel.removeFromParent();
        enterTimesPanel.removeFromParent();
        btnSave.removeFromParent();
        btnCancel.removeFromParent();
        errorMsgSeason.removeFromParent();
        errorMsgTimes.removeFromParent();

        disableButtons();
        ndpDaysTable.removeColumn(deleteColumn);

        if (isInherited && !isViewOnly) {
            parentSchedulePanel.setInheritedPopupHeightForScrollBar(this.getOffsetHeight());
        }
    }

    @UiHandler("startMonthListBox")
    public void onStartMonthChange(ChangeEvent event) {
        onStartMonthChange();
    }

    //sets up the startDAYbox
    private void onStartMonthChange() {
        int s = getLastDayOfMonth(Integer.valueOf(startMonthListBox.getValue(startMonthListBox.getSelectedIndex())));
        startDayListBox.clear();
        for (int i=1; i<=s; i++) {
            startDayListBox.addItem(String.valueOf(i), String.valueOf(i));
        }
    }

    @UiHandler("endMonthListBox")
    public void onEndMonthChange(ChangeEvent event) {
        onEndMonthChange();
    }

    //sets up the endDAYbox
    public void onEndMonthChange() {
        int e = getLastDayOfMonth(Integer.valueOf(endMonthListBox.getValue(endMonthListBox.getSelectedIndex())));
        endDayListBox.clear();
        for (int i=1; i<=e; i++) {
            endDayListBox.addItem(String.valueOf(i), String.valueOf(i));
        }
    }

    private int getLastDayOfMonth(int month) {
        int e = 31;
        if (month == 4
                || month == 6
                || month == 9
                || month == 11) {
            e=30;
        } else if (month == 2) {
            e=29;
        }
        return e;
    }

    @UiHandler("btnAddSeasonDayTimes")
    void addSeasonDayTimes(ClickEvent e) {
        SessionCheckCallback sessionCheckCallback = new SessionCheckCallback() {
            @Override
            public void callback(SessionCheckResolution resolution) {
                addNewDay();
            }
        };
        clientFactory.handleSessionCheckCallback(sessionCheckCallback);
    }

    @UiHandler("btnSave")
    void save(ClickEvent e) {
        SessionCheckCallback sessionCheckCallback = new SessionCheckCallback() {
            @Override
            public void callback(SessionCheckResolution resolution) {
                //disable buttons to avoid clicking
                disableButtons();
                saveNow();
            }
        };
        clientFactory.handleSessionCheckCallback(sessionCheckCallback);
    }

    @UiHandler("btnCancel")
    void cancel(ClickEvent e) {
        hasDirtyData.checkDirtyData(new ConfirmHandler() {
            @Override
            public void confirmed(boolean confirm) {
                if (confirm) {
                    hasDirtyData.setDirtyData(false);
                    cancelPanel();
                }
            }
        });
    }


    //-----------------------------------add Day to table -----------------------------------------------
    private void addNewDay() {
        //validate & add to dayTable
         btnAddSeasonDayTimes.setEnabled(false);

        if (!isValidTimes()) {
            btnAddSeasonDayTimes.setEnabled(true);
            return;
        }
        final NdpDayProfileData ndpDayProfileData = dayTimesMapFormToData();

        //check overlap
        clientFactory.getNdpRpc().setTimeInMillis(ndpDayProfileData, new ClientCallback<NdpDayProfileData>() {
            @Override
            public void onSuccess(NdpDayProfileData result) {
                if (checkTimeOverlap(result)) {
                    setErrorMessage(MessagesUtil.getInstance().getMessage("ndp.assign.season.error.time.already.assigned"));
                    return;
                } else {
                    refreshDayTable(result);
                }
            }
        });
     }

    private boolean isValidTimes() {
        boolean isValid = true;
        startTimeElement.clearErrorMsg();
        endTimeElement.clearErrorMsg();
        errorMsgTimes.setText("");
        errorMsgTimes.setVisible(false);

        if (txtbxStartHour.getText() == null || txtbxStartHour.getText().isEmpty()
                || txtbxStartMinute.getText() == null || txtbxStartMinute.getText().isEmpty()
                || txtbxEndHour.getText() == null || txtbxEndHour.getText().isEmpty()
                || txtbxEndMinute.getText() == null || txtbxEndMinute.getText().isEmpty()) {
            setErrorMessage(MessagesUtil.getInstance().getMessage("ndp.assign.dayperiod.nulls"));
            return false;
        }

        Integer startHour = Integer.valueOf(txtbxStartHour.getText());
        Integer startMinute = Integer.valueOf(txtbxStartMinute.getText());
        Integer endHour = Integer.valueOf(txtbxEndHour.getText());
        Integer endMinute = Integer.valueOf(txtbxEndMinute.getText());

        if (startHour.equals(endHour)) {
            if (startMinute > endMinute) {
                logger.info("NDP day profile time end time before start time");
                setErrorMessage(MessagesUtil.getInstance().getMessage("ndp.assign.season.error.end.before.start"));
                isValid = false;
            }
        } else if (startHour > endHour) {
            logger.info("NDP day profile time end before start ");
            setErrorMessage(MessagesUtil.getInstance().getMessage("ndp.assign.season.error.end.before.start"));
            isValid =  false;
        }
        return isValid;
    }

    private void setErrorMessage(String msg) {
        if (msg == null) {
            msg = "";
        }
        errorMsgTimes.setText(msg);
        errorMsgTimes.setVisible(msg != null || !(msg.trim().equals("")));
    }

    private NdpDayProfileData dayTimesMapFormToData() {
        NdpDayProfile ndp = new NdpDayProfile();
        //Comment - don't setup the ndpSeasonId here - it is used in the backend to decide whether to save this day (if it is null)
        ndp.setDayOfWeek(daysOfWeekBox.getSelectedIndex() + 1);
        ndp.setStartHour(Integer.valueOf(txtbxStartHour.getText()));
        ndp.setStartMinute(Integer.valueOf(txtbxStartMinute.getText()));
        ndp.setEndHour(Integer.valueOf(txtbxEndHour.getText()));
        ndp.setEndMinute(Integer.valueOf(txtbxEndMinute.getText()));

        return new NdpDayProfileData(ndp);
    }

    private boolean checkTimeOverlap(NdpDayProfileData newNdpDayProfileData) {
        boolean overlap = false;
        for (NdpDayProfileData data : dataProvider.getList()) {
            if (newNdpDayProfileData.getNdpDayProfile().getDayOfWeek().equals(data.getNdpDayProfile().getDayOfWeek())) {
                if (newNdpDayProfileData.getStartMs() >= data.getStartMs() && newNdpDayProfileData.getStartMs() <= data.getEndMs()
                        || newNdpDayProfileData.getEndMs() >= data.getStartMs() && newNdpDayProfileData.getEndMs() <= data.getEndMs()) {
                    overlap = true;
                    break;
                }
            }
        }
        return overlap;
    }

    private void refreshDayTable(NdpDayProfileData ndpDayProfileData) {
        dataProvider.getList().add(ndpDayProfileData);
        sortDataProviderList();

        clearDayTimes();
        btnAddSeasonDayTimes.setEnabled(true);
        hasDirtyData.setDirtyData(true);
    }

    public void clearDayTimes() {
        clearErrors();
        daysOfWeekBox.setSelectedIndex(0);
        txtbxStartHour.setText("");
        txtbxStartMinute.setText("");
        txtbxEndHour.setText("");
        txtbxEndMinute.setText("");
    }

    public void clearErrors() {
        errorMsgSeason.setText("");
        errorMsgSeason.setVisible(false);
        startTimeElement.clearErrorMsg();
        endTimeElement.clearErrorMsg();
        errorMsgTimes.setText("");
        errorMsgTimes.setVisible(false);
    }

    //-----------------------save Panel -------------------------------------------------------------------
    private void saveNow() {

        if (!isValidSeason()) {
            enableButtons();
            return;
        }

        if (ndpSeason == null) {
            ndpSeason = new NdpSeason();
            ndpSeason.setNdpScheduleId(ndpScheduleData.getNdpSchedule().getId());
        }
        ndpSeason = seasonMapFormToData(ndpSeason);

        ArrayList<NdpDayProfileData> dayProfileList = new ArrayList<NdpDayProfileData>();
        for (NdpDayProfileData ndpd : dataProvider.getList()) {
            dayProfileList.add(ndpd);
        }
        final boolean isDayTimeEntered = dayProfileList.size() > 0;

        clientFactory.getNdpRpc().saveNdpSeasonAndDays(ndpSeason, dayProfileList, deleteTheseDayProfiles, new ClientCallback<NdpSeason>() {
            @Override
            public void onSuccess(NdpSeason result) {
                hasDirtyData.setDirtyData(false);
                parentSchedulePanel.addNewSeason(result, isDayTimeEntered);
                if (isDeactivateSchedule) {
                    if (ndpScheduleData.getNdpSchedule().getRecordStatus().equals(RecordStatus.ACT) || parentSchedulePanel.getNdpParentPanel().isActiveBoxTicked()) {
                        parentSchedulePanel.getNdpParentPanel().deactivateSchedule();
                    } else {
                        parentSchedulePanel.getNdpParentPanel().disableActivate();
                    }
                }
            }

            @Override
            public void onFailureClient() {
                enableButtons();
            }
        });

    }

    private boolean isValidSeason() {
        boolean valid = true;
        errorMsgSeason.setText("");
        errorMsgSeason.setVisible(false);

        int startDay = startDayListBox.getSelectedIndex()+1;
        int startMonth = startMonthListBox.getSelectedIndex()+1;
        int endDay = endDayListBox.getSelectedIndex()+1;
        int endMonth = endMonthListBox.getSelectedIndex()+1;

        // check if all there
        if (startMonth == endMonth) {
            if (startDay > endDay) {
                logger.info("season date end before start");
                errorMsgSeason.setText(MessagesUtil.getInstance().getMessage("ndp.assign.season.error.end.before.start"));
                errorMsgSeason.setVisible(true);
                return false;
            }
        } else if (startMonth > endMonth) {
            logger.info("season date end month before start month");
            errorMsgSeason.setText(MessagesUtil.getInstance().getMessage("ndp.assign.season.error.end.before.start"));
            errorMsgSeason.setVisible(true);
           return false;
        }

        NdpSeason tempSeason = seasonMapFormToData(new NdpSeason());
        if (ndpSeason != null) {
            tempSeason.setId(ndpSeason.getId());
        }

        if (parentSchedulePanel.checkOverlapNewSeason(tempSeason)) {
            logger.info("season dates overlap - already assigned");
            errorMsgSeason.setText(MessagesUtil.getInstance().getMessage("ndp.assign.season.error.date.already.assigned"));
            errorMsgSeason.setVisible(true);
            valid = false;
        }
        return valid;
    }

    private NdpSeason seasonMapFormToData(NdpSeason mapSeason) {
        mapSeason.setStartDay(startDayListBox.getSelectedIndex()+1);
        mapSeason.setStartMonth(startMonthListBox.getSelectedIndex()+1);
        mapSeason.setEndDay(endDayListBox.getSelectedIndex()+1);
        mapSeason.setEndMonth(endMonthListBox.getSelectedIndex()+1);
        return mapSeason;
    }

    private void disableButtons() {
        btnSave.setEnabled(false);
        btnCancel.setEnabled(false);
        btnAddSeasonDayTimes.setEnabled(false);
    }

    private void enableButtons() {
        btnSave.setEnabled(true);
        btnCancel.setEnabled(true);
        btnAddSeasonDayTimes.setEnabled(true);
    }

    private void cancelPanel() {
        hasDirtyData.checkDirtyData(new ConfirmHandler() {
                          @Override
                          public void confirmed(boolean confirm) {
                              if (confirm) {
                                  hasDirtyData.setDirtyData(false);
                                  parentSchedulePanel.returnSetup();
                              }
                              else {
                                  enableButtons();
                                  return;
                              }
                          }
                      });
    }

    //--------------DELETE DAY --------------------------------------------------------------------------------------------------------

    private void deleteDayProfile(final NdpDayProfileData dayProfileData) {
        logger.info("delete this dayProfile, day= " + dayProfileData.getNdpDayProfile().getDayOfWeek() + " startHour= "+ dayProfileData.getNdpDayProfile().getStartHour());
        if (ndpSeason == null) {
            confirmDeleteDay(dayProfileData);
        } else {
            clientFactory.getNdpRpc().isDayProfilePresentForSchedule(ndpScheduleData.getNdpSeasonList(), ndpScheduleData.getNdpSpecialDayList(), dayProfileData, deleteTheseDayProfiles, new ClientCallback<Boolean>() {
                @Override
                public void onSuccess(Boolean result) {
                    logger.info("****RC isDayProfilePresentForSchedule= " + result);
                    if (result) {
                        confirmDeleteDay(dayProfileData);
                    } else {
                        confirmDeleteAndDeactivateSchedule(dayProfileData);
                    }
                }
            });
        }
    }

    private void confirmDeleteDay(final NdpDayProfileData dayProfileData) {
        String dayToDelete = constructDayString(dayProfileData);
        //Confirm Delete
        Dialogs.confirm(
                ResourcesFactoryUtil.getInstance().getMessages().getMessage(MessagesUtil.getInstance().getMessage("ndp.day.profile.confirm.delete", new String[] {dayToDelete})),
                ResourcesFactoryUtil.getInstance().getMessages().getMessage("option.positive"),
                ResourcesFactoryUtil.getInstance().getMessages().getMessage("option.negative"),
                ResourcesFactoryUtil.getInstance().getQuestionIcon(),
                new ConfirmHandler() {
                    @Override
                    public void confirmed(boolean confirm) {
                        if (confirm) {
                            hasDirtyData.setDirtyData(true);
                            deleteDay(dayProfileData);
                        }
                        else {
                            return;
                        }
                    }
                }
         );
    }

    private void deleteDay(NdpDayProfileData dayProfileData) {
        clearDayTimes();
        //Take out of dataProvider List
        List<NdpDayProfileData> dayProfileList = dataProvider.getList();
        dayProfileList.remove(dayProfileData);
        sortDataProviderList();

        hasDirtyData.setDirtyData(true);
        deleteTheseDayProfiles.add(dayProfileData);
    }

    private void confirmDeleteAndDeactivateSchedule(final NdpDayProfileData dayProfileData) {
        String dayToDelete = constructDayString(dayProfileData);
        if (ndpScheduleData.getNdpSchedule().getRecordStatus().equals(RecordStatus.ACT) || parentSchedulePanel.getNdpParentPanel().isActiveBoxTicked()) {
            Dialogs.confirm (
                    ResourcesFactoryUtil.getInstance().getMessages().getMessage(MessagesUtil.getInstance().getMessage("ndp.day.profile.confirm.delete.and.deactivate", new String[] {dayToDelete})),
                    ResourcesFactoryUtil.getInstance().getMessages().getMessage("option.positive"),
                    ResourcesFactoryUtil.getInstance().getMessages().getMessage("option.negative"),
                    ResourcesFactoryUtil.getInstance().getQuestionIcon(),
                    new ConfirmHandler() {
                        @Override
                        public void confirmed(boolean confirm) {
                            if (confirm) {
                                deleteDay(dayProfileData);
                                isDeactivateSchedule = true;
                            }
                            else {
                                return;
                            }
                        }
                    }
                    );
        } else {
            confirmDeleteDay(dayProfileData);
        }
    }

    private String constructDayString(NdpDayProfileData dayProfileData) {
        return dayOfWeekNames[dayProfileData.getNdpDayProfile().getDayOfWeek() - 1]
                + " " + prefixWithZero(dayProfileData.getNdpDayProfile().getStartHour()) + ":" + prefixWithZero(dayProfileData.getNdpDayProfile().getStartMinute())
                + " - " + prefixWithZero(dayProfileData.getNdpDayProfile().getEndHour()) + ":" + prefixWithZero(dayProfileData.getNdpDayProfile().getEndMinute());
    }

    public HasDirtyData getHasDirtyData() {
        return hasDirtyData;
    }
}
