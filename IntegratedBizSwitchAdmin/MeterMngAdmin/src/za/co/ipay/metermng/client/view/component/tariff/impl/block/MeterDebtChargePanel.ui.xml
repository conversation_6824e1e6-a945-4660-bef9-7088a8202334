<!DOCTYPE ui:UiBinder SYSTEM "http://dl.google.com/gwt/DTD/xhtml.ent">
<ui:UiBinder xmlns:ui="urn:ui:com.google.gwt.uibinder"
             xmlns:g="urn:import:com.google.gwt.user.client.ui" 
             xmlns:g2="urn:import:com.google.gwt.user.cellview.client"
             xmlns:t="urn:import:za.co.ipay.gwt.common.client.form"
             xmlns:w="urn:import:za.co.ipay.gwt.common.client.widgets"
             xmlns:p1="urn:import:za.co.ipay.gwt.common.client.workspace">
	<ui:style>
	</ui:style>
  
  <ui:with field="msg" type="za.co.ipay.metermng.client.i18n.UiMessages" />
  
    <t:FormGroupPanel labelText="{msg.getMeterDebtTitle}">
        <t:FormRowPanel>
            <t:FormElement ui:field="singlePhaseElement" debugId="singlePhaseElement" labelText="{msg.getMeterDebtSinglePhaseLabel}:" helpMsg="{msg.getMeterDebtSinglePhaseLabelHelp}" required="false">
                <t:BigDecimalValueBox ui:field="singlePhaseChargeBox" debugId="singlePhaseChargeBox" styleName="gwt-TextBox largeNumericInput" />
            </t:FormElement>
            <t:FormElement ui:field="threePhaseElement" debugId="threePhaseElement" labelText="{msg.getMeterDebtThreePhaseLabel}:" helpMsg="{msg.getMeterDebtThreePhaseLabelHelp}" required="false">
                <t:BigDecimalValueBox ui:field="threePhaseChargeBox" debugId="threePhaseChargeBox" styleName="gwt-TextBox largeNumericInput" />
            </t:FormElement>
        </t:FormRowPanel>
    </t:FormGroupPanel>
	
</ui:UiBinder> 