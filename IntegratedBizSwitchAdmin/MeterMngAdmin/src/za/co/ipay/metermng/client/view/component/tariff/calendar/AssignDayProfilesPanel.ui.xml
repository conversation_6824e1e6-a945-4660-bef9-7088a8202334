<!DOCTYPE ui:UiBinder SYSTEM "http://dl.google.com/gwt/DTD/xhtml.ent">
<ui:UiBinder xmlns:ui="urn:ui:com.google.gwt.uibinder"
	xmlns:g="urn:import:com.google.gwt.user.client.ui" xmlns:g2="urn:import:com.google.gwt.user.cellview.client"
	xmlns:form="urn:import:za.co.ipay.gwt.common.client.form" xmlns:widget="urn:import:za.co.ipay.gwt.common.client.widgets">
	<ui:style>
	   .verticalheading {
	       background-color: lightgrey;
	       font-size: 15px;
           font-weight: bold;
           padding-top: 0.75EM;
           margin-bottom: 0.5EM;
	   }
	   
	</ui:style>

	<ui:with field="msg" type="za.co.ipay.metermng.client.i18n.UiMessages" />

	<g:FlowPanel width="99%" height="100%">
		<g:VerticalPanel spacing="5">
			<g:HTMLPanel ui:field="calendarDayProfilesPanel" debugId="calendarDayProfilesPanel">
				<g:HTML text="{msg.getCalendarAssignDayProfilesDescription}" styleName="dataDescription" />
			</g:HTMLPanel>
			<g:FlowPanel>
				<g:Grid cellPadding="15" ui:field="calendarSeasonsGrid" debugId="calendarSeasonsGrid" borderWidth="1">
					<g:row>
						<g:customCell styleName="{style.verticalheading}">
							<g:Label>Season</g:Label>
						</g:customCell>
						<g:customCell styleName="dataTitle">
							<g:Label>Mon</g:Label>
						</g:customCell>
						<g:customCell styleName="dataTitle">
							<g:Label>Tue</g:Label>
						</g:customCell>
						<g:customCell styleName="dataTitle">
							<g:Label>Wed</g:Label>
						</g:customCell>
						<g:customCell styleName="dataTitle">
							<g:Label>Thu</g:Label>
						</g:customCell>
						<g:customCell styleName="dataTitle">
							<g:Label>Fri</g:Label>
						</g:customCell>
						<g:customCell styleName="dataTitle">
							<g:Label>Sat</g:Label>
						</g:customCell>
						<g:customCell styleName="dataTitle">
							<g:Label>Sun</g:Label>
						</g:customCell>
					</g:row>
				</g:Grid>
			</g:FlowPanel>
            <g:HorizontalPanel spacing="5" ui:field="buttons">
                 <g:Button ui:field="btnSave" debugId="assDpBtnSave" text="{msg.getSaveButton}" />
                 <g:Button ui:field="btnCancel" debugId="assDpBCancel" text="{msg.getCancelButton}" />
            </g:HorizontalPanel>  
		</g:VerticalPanel>
	</g:FlowPanel>
</ui:UiBinder> 