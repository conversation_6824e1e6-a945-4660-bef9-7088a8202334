package za.co.ipay.metermng.client.view.menu;

import za.co.ipay.gwt.common.client.Dialogs;
import za.co.ipay.gwt.common.client.handler.EnterKeyHandler;
import za.co.ipay.gwt.common.client.resource.Messages.MessagesUtil;
import za.co.ipay.gwt.common.client.widgets.Message;
import za.co.ipay.metermng.client.event.MeterSearchEvent;
import za.co.ipay.metermng.client.event.UsagePointSearchEvent;
import za.co.ipay.metermng.client.factory.ClientFactory;
import za.co.ipay.metermng.client.history.MeterPlace;
import za.co.ipay.metermng.client.history.UsagePointPlace;
import za.co.ipay.metermng.client.resource.image.MediaResource.MediaResourceUtil;
import za.co.ipay.metermng.client.view.component.BaseComponent;
import za.co.ipay.metermng.shared.MeterSuggestion;
import za.co.ipay.metermng.shared.UsagePointSuggestion;

import com.google.gwt.core.client.GWT;
import com.google.gwt.core.client.Scheduler;
import com.google.gwt.event.dom.client.ClickEvent;
import com.google.gwt.event.dom.client.FocusEvent;
import com.google.gwt.event.dom.client.FocusHandler;
import com.google.gwt.event.dom.client.KeyDownEvent;
import com.google.gwt.event.logical.shared.SelectionEvent;
import com.google.gwt.uibinder.client.UiBinder;
import com.google.gwt.uibinder.client.UiField;
import com.google.gwt.uibinder.client.UiHandler;
import com.google.gwt.user.client.ui.Image;
import com.google.gwt.user.client.ui.Label;
import com.google.gwt.user.client.ui.SuggestBox;
import com.google.gwt.user.client.ui.SuggestOracle.Suggestion;
import com.google.gwt.user.client.ui.Widget;

public class MeterSearchView extends BaseComponent {

    private static MeterSearchUiBinder uiBinder = GWT.create(MeterSearchUiBinder.class);
    @UiField(provided=true) SuggestBox suggestBoxMeterNumber;
    @UiField Image imgSearch;
    @UiField Message errormessage;
    @UiField Label instruction;
    
    @UiField Label instructionUsagePoint;
    @UiField(provided=true) SuggestBox suggestBoxUsagePointName;
    @UiField Image imgSearchUsagePoint;
    @UiField Message errormessageUsagePoint;
    
    private ClientFactory clientFactory;
    private String meterNum;
         
    interface MeterSearchUiBinder extends UiBinder<Widget, MeterSearchView> {
    }

    public MeterSearchView(SuggestBox suggestBox, SuggestBox usagePointSuggestBox, ClientFactory clientFactory) {
    	suggestBoxMeterNumber = suggestBox;
    	suggestBoxUsagePointName = usagePointSuggestBox;
        this.clientFactory = clientFactory;
        initWidget(uiBinder.createAndBindUi(this));
        
        imgSearch.setResource(MediaResourceUtil.getInstance().getSearchImage());
        imgSearchUsagePoint.setResource(MediaResourceUtil.getInstance().getSearchImage());
        errormessage.setText("");
        errormessage.setVisible(false);
        errormessageUsagePoint.setText("");
        errormessageUsagePoint.setVisible(false);
        
        suggestBoxMeterNumber.addKeyDownHandler(new EnterKeyHandler() {            
            @Override
            public void enterKeyDown(KeyDownEvent event) {
                Scheduler.get().scheduleDeferred(new Scheduler.ScheduledCommand() {
                    @Override
                    public void execute() {
                        meterNum = suggestBoxMeterNumber.getValue().trim();
                        search();
                    }
                });
            }
        }); 
        suggestBoxMeterNumber.getValueBox().addFocusHandler(new FocusHandler() {            
            @Override
            public void onFocus(FocusEvent event) {
                suggestBoxMeterNumber.showSuggestionList();
            }
        });   
        
        
        suggestBoxUsagePointName.addKeyDownHandler(new EnterKeyHandler() {            
            @Override
            public void enterKeyDown(KeyDownEvent event) {
                Scheduler.get().scheduleDeferred(new Scheduler.ScheduledCommand() {
                    @Override
                    public void execute() {
                        searchUsagePoint(suggestBoxUsagePointName.getValue().trim());
                    }
                });
            }
        }); 
        suggestBoxUsagePointName.getValueBox().addFocusHandler(new FocusHandler() {            
            @Override
            public void onFocus(FocusEvent event) {
                suggestBoxUsagePointName.showSuggestionList();
            }
        });   
        
    }
    
    @UiHandler("imgSearch")
    void handleSearchButton(ClickEvent e) {
        clearMessage();
        meterNum = suggestBoxMeterNumber.getValue().trim();
        search();
    }
    
    @UiHandler("suggestBoxMeterNumber") 
    void handleSuggestBox(SelectionEvent<Suggestion> se) {
    	if (se.getSelectedItem() instanceof MeterSuggestion) {
    		meterNum = ((MeterSuggestion) se.getSelectedItem()).getMeter().getNumber().trim();
            search();
        }
    }
    
    @UiHandler("imgSearchUsagePoint")
    void handleUsagePointSearchButton(ClickEvent e) {
        clearUsagePointMessage();
        searchUsagePoint(suggestBoxUsagePointName.getValue().trim());
    }
    
    @UiHandler("suggestBoxUsagePointName") 
    void handleUsagePointSuggestBox(SelectionEvent<Suggestion> se) {
        if (se.getSelectedItem() instanceof UsagePointSuggestion) {
            searchUsagePoint(((UsagePointSuggestion) se.getSelectedItem()).getUsagePointName());
        }
    }
    
    public void setMessage(String message, int type) {
        errormessage.setText(message);
        errormessage.setType(type);
        errormessage.setVisible(true);
    }
    
    public void setUsagePointMessage(String message, int type) {
        errormessageUsagePoint.setText(message); 
        errormessageUsagePoint.setType(type);
        errormessageUsagePoint.setVisible(true);
    }

    public void clearMessage() {
        errormessage.setText("");
        errormessage.setVisible(false);
    }

    public void clearUsagePointMessage() {
        errormessageUsagePoint.setText("");
        errormessageUsagePoint.setVisible(false);
    }
    
    public void showMessage(String message) {
        Dialogs.displayInformationMessage(message, 
                                          MediaResourceUtil.getInstance().getInfoImage(), 
                                          imgSearch.getAbsoluteLeft(), 
                                          imgSearch.getAbsoluteTop(), 
                                          MessagesUtil.getInstance().getMessage("button.close"));
    }
    
    public void showUsagePointMessage(String message) {
        Dialogs.displayInformationMessage(message, 
                                          MediaResourceUtil.getInstance().getInfoImage(), 
                                          imgSearchUsagePoint.getAbsoluteLeft(), 
                                          imgSearchUsagePoint.getAbsoluteTop(), 
                                          MessagesUtil.getInstance().getMessage("button.close"));
    }
    
    private void search() {
        if (meterNum.isEmpty()) {
            setMessage(MessagesUtil.getInstance().getMessage("metersearch.error.nometer"), Message.MESSAGE_TYPE_ERROR);
        } else {
            clientFactory.getEventBus().fireEvent(new MeterSearchEvent(new MeterPlace(meterNum, false)));
        }
    }
    
    private void searchUsagePoint(String usagePointName) {
        if (usagePointName.isEmpty()) {
            setUsagePointMessage(MessagesUtil.getInstance().getMessage("usagepoint.error.none"), Message.MESSAGE_TYPE_ERROR);
        } else {    
            clientFactory.getEventBus().fireEvent(new UsagePointSearchEvent(new UsagePointPlace(usagePointName)));
        }    
    }
}
