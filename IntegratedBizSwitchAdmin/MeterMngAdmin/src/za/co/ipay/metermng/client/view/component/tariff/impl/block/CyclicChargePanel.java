package za.co.ipay.metermng.client.view.component.tariff.impl.block;

import java.math.BigDecimal;

import com.google.gwt.core.client.GWT;
import com.google.gwt.event.dom.client.ChangeEvent;
import com.google.gwt.event.dom.client.ChangeHandler;
import com.google.gwt.uibinder.client.UiBinder;
import com.google.gwt.uibinder.client.UiField;
import com.google.gwt.user.client.ui.CheckBox;
import com.google.gwt.user.client.ui.Label;
import com.google.gwt.user.client.ui.ListBox;
import com.google.gwt.user.client.ui.RadioButton;
import com.google.gwt.user.client.ui.TextBox;
import com.google.gwt.user.client.ui.Widget;

import za.co.ipay.gwt.common.client.form.BigDecimalValueBox;
import za.co.ipay.gwt.common.client.form.FormElement;
import za.co.ipay.gwt.common.client.form.FormGroupPanel;
import za.co.ipay.gwt.common.client.form.Format.FormatUtil;
import za.co.ipay.gwt.common.client.form.HasDirtyData;
import za.co.ipay.gwt.common.client.handler.FormDataChangeHandler;
import za.co.ipay.gwt.common.client.handler.FormDataClickHandler;
import za.co.ipay.gwt.common.client.resource.Messages.MessagesUtil;
import za.co.ipay.gwt.common.shared.validation.ValidateUtil;
import za.co.ipay.metermng.client.factory.ClientFactory;
import za.co.ipay.metermng.client.view.component.BaseComponent;
import za.co.ipay.metermng.datatypes.CycleE;
import za.co.ipay.metermng.datatypes.CyclicChargeCalcAtE;
import za.co.ipay.metermng.shared.tariff.cyclic.CyclicChargeData;

public class CyclicChargePanel extends BaseComponent {
    interface CyclicChargePanelUiBinder extends UiBinder<Widget, CyclicChargePanel> {
    }
    private static CyclicChargePanelUiBinder uiBinder = GWT.create(CyclicChargePanelUiBinder.class);

    @UiField FormElement cyclicChargeNameElement;
    @UiField FormElement cyclicChargeElement;
    @UiField FormElement nonAccruingMonthlyElement;
    @UiField FormElement cycleListBoxElement;

    @UiField ListBox cycleListBox;
    @UiField CheckBox nonAccruingMonthlyCheckBox;
    @UiField TextBox cyclicChargeNameBox;
    @UiField Label cyclicChargeCurrencyLabel;
    @UiField BigDecimalValueBox cyclicChargeBox;
    
    @UiField FormGroupPanel applyAtPanel;
    @UiField RadioButton applyAtVend;
    @UiField RadioButton applyAtBilling;
    
    protected boolean required;
    protected boolean useNameAsLabel;
    protected boolean enableNonAccruingMonthly;
    protected boolean enableCyclicChargeApplyAt;
    /**
     * No UI for this yet, this is currently only settable from template;
     */
    protected String[] levies;

    public CyclicChargePanel(ClientFactory clientFactory, boolean required, boolean useNameAsLabel, 
            boolean enableNonAccruingMonthly, boolean enableCyclicChargeApplyAt) {
        this.clientFactory = clientFactory;
        this.required = required;
        this.useNameAsLabel = useNameAsLabel;
        this.enableNonAccruingMonthly = enableNonAccruingMonthly;
        this.enableCyclicChargeApplyAt = enableCyclicChargeApplyAt;
        initWidget(uiBinder.createAndBindUi(this));
        initUi();
    }
    
    public CyclicChargePanel(ClientFactory clientFactory, boolean required, boolean useNameAsLabel, boolean enableNonAccruingMonthly) {
        this(clientFactory, required, useNameAsLabel, enableNonAccruingMonthly, false);
    }
    
    public void setCyclicCharge(CyclicChargeData cc) {
        cyclicChargeNameBox.setText(cc.getName());
        cyclicChargeBox.setValue(cc.getCharge());
        if(cc.getCycle() != null) {
            long cycleId = cc.getCycle().getId();
            for (int i = 1; i < cycleListBox.getItemCount(); i++) {
                cycleListBox.setSelectedIndex(i);
                if (Integer.parseInt(cycleListBox.getValue(cycleListBox.getSelectedIndex())) == cycleId) {
                    break;
                }
            }
        }
        
        if(enableNonAccruingMonthly && cc.getCycle() != null && cc.getCycle() == CycleE.MONTHLY) {
            nonAccruingMonthlyElement.setVisible(true);
        }
        nonAccruingMonthlyCheckBox.setValue(cc.isNonAccruingMonthly());

        if(useNameAsLabel && ValidateUtil.isNotNullOrBlank(cc.getName())) {
            cyclicChargeElement.setLabelText(cc.getName());
            cyclicChargeNameElement.setVisible(false);
            nonAccruingMonthlyElement.setVisible(false);
            cycleListBox.setEnabled(false);
        }
        levies = cc.getLevies();
        
        if(enableCyclicChargeApplyAt && cc.getCyclicChargeCalcAtE() != null) {
            if (cc.getCyclicChargeCalcAtE().equals(CyclicChargeCalcAtE.B)) {
                applyAtBilling.setValue(true);
            } else {
                getApplyAtVend().setValue(true);
            }
        }
    }
    
    public CyclicChargeData getCyclicCharge() throws Exception {
        boolean valid = true;
        //Cyclic cost and name
        BigDecimal cyclicCharge = cyclicChargeBox.getValue();
        String cyclicChargeName = cyclicChargeNameBox.getText();
        if (ValidateUtil.isNullOrBlank(cyclicChargeName) && cyclicCharge != null) {
            valid = false;
            cyclicChargeNameElement.setErrorMsg(MessagesUtil.getInstance().getMessage("tariff.error.cyclic.charge.name"));        
        } else if ((cyclicChargeName != null && !cyclicChargeName.trim().equals("")) && cyclicCharge == null) {
            valid = false;
            cyclicChargeElement.setErrorMsg(MessagesUtil.getInstance().getMessage("tariff.error.cyclic.charge")); 
        } else if (cyclicCharge != null && cyclicCharge.doubleValue() < 0) {
            valid = false;
            cyclicChargeElement.setErrorMsg(MessagesUtil.getInstance().getMessage("tariff.error.cyclic.charge.positive"));        
        }        
        CycleE cycle = null;
        try {
            if (cyclicCharge != null) {
                int index = cycleListBox.getSelectedIndex();
                if (index < 1) {
                    valid = false;
                    cycleListBoxElement.showErrorMsg(MessagesUtil.getInstance().getMessage("tariff.cost.cycle.error"));
                } else {
                    cycle = CycleE.fromId(Integer.parseInt(cycleListBox.getValue(cycleListBox.getSelectedIndex())));
                }
            }
        } catch (Exception e) {
            valid = false;
            cycleListBoxElement.showErrorMsg(MessagesUtil.getInstance().getMessage("cycle.error"));
        } 
        if(valid && required) {
            // these additional validations if this charge is required
            if(ValidateUtil.isNullOrBlank(cyclicChargeName)) {
                cycleListBoxElement.showErrorMsg(MessagesUtil.getInstance().getMessage("error.field.required"));
                valid = false;
            }
            if(cyclicCharge == null) {
                cycleListBoxElement.showErrorMsg(MessagesUtil.getInstance().getMessage("error.field.required"));
                valid = false;
            }
            if(cycle == null) {
                cycleListBoxElement.showErrorMsg(MessagesUtil.getInstance().getMessage("error.field.required"));
                valid = false;
            }
        }
        if(enableCyclicChargeApplyAt) {
            if (!isApplyAtValid()) {
                applyAtPanel.setErrorMsg(MessagesUtil.getInstance().getMessage("cyclic.charge.apply.at.error.required"));
                valid = false;
            }
        }
        if(! valid) {
            throw new Exception("Validation failed on cyclic charge");
        }
        if(cyclicCharge == null) {
            return null;
        }
        CyclicChargeData cc = new CyclicChargeData();
        cc.setCycle(cycle);
        // non accruing only relevant for monthly
        if(cycle != null && cycle == CycleE.MONTHLY) {
            cc.setNonAccruingMonthly(nonAccruingMonthlyCheckBox.getValue());
        } else {
            cc.setNonAccruingMonthly(false);
        }
        cc.setCharge(cyclicCharge);
        cc.setName(cyclicChargeName);
        cc.setLevies(levies);
        cc.setCyclicChargeCalcAtE(null);
        if(enableCyclicChargeApplyAt) {
            if (applyAtBilling.getValue()) {
                cc.setCyclicChargeCalcAtE(CyclicChargeCalcAtE.B);
            } else {
                cc.setCyclicChargeCalcAtE(CyclicChargeCalcAtE.V);
            }
        }
        return cc;
    }

    private void initUi() {
        if(required) {
            cyclicChargeNameElement.setRequired(true);
            cyclicChargeElement.setRequired(true);
            nonAccruingMonthlyElement.setRequired(true);
            cycleListBoxElement.setRequired(true);
        }

        createCurrencyLabel();
        createCycleList();
        nonAccruingMonthlyElement.setVisible(false);
        applyAtPanel.setVisible(enableCyclicChargeApplyAt);
    }

    private void createCurrencyLabel() {
        cyclicChargeCurrencyLabel.setText(FormatUtil.getInstance().getCurrencySymbol());
        if (FormatUtil.getInstance().isRightToLeft()) {
            cyclicChargeCurrencyLabel.setStyleName("btCurrency-right");
        } else {
            cyclicChargeCurrencyLabel.setStyleName("btCurrency-left");
        }
    }

    private void createCycleList() {
        cycleListBox.addItem("", "");
        cycleListBox.addItem(MessagesUtil.getInstance().getMessage("tariff.cost.cycle.daily"), "3");    //addItem(text, CycleE.id)
        cycleListBox.addItem(MessagesUtil.getInstance().getMessage("tariff.cost.cycle.monthly"), "4");
    }
    
    private CycleE getSelectedCycle() {
        int index = cycleListBox.getSelectedIndex();
        if (index < 1) {
            return null;
        } else {
            return CycleE.fromId(Integer.parseInt(cycleListBox.getValue(cycleListBox.getSelectedIndex())));
        }
    }

    public void clearErrors() {        
        cyclicChargeElement.setErrorMsg(null);
        cyclicChargeNameElement.setErrorMsg(null);
        cycleListBoxElement.setErrorMsg(null);
        nonAccruingMonthlyElement.setErrorMsg(null);
        if (enableCyclicChargeApplyAt) {
            applyAtPanel.clearErrorMsg();
        }
    }
    
    public void clearForm() {
        cycleListBox.setSelectedIndex(0);
        nonAccruingMonthlyCheckBox.setValue(false);
        nonAccruingMonthlyElement.setVisible(false);
        cyclicChargeBox.setValue(null);
        cyclicChargeNameBox.setText("");
        if (enableCyclicChargeApplyAt) {
            getApplyAtVend().setValue(true);
        }
        clearErrors();
    }
    
    public void addFieldHandlers(HasDirtyData hasDirtyData) {
        cycleListBox.addChangeHandler(new FormDataChangeHandler(hasDirtyData));
        cycleListBox.addChangeHandler(new ChangeHandler() {
            
            @Override
            public void onChange(ChangeEvent event) {
                CycleE cycle = getSelectedCycle();
                if(enableNonAccruingMonthly && cycle != null && cycle == CycleE.MONTHLY) {
                    nonAccruingMonthlyElement.setVisible(true);
                } else {
                    nonAccruingMonthlyCheckBox.setValue(false);
                    nonAccruingMonthlyElement.setVisible(false);
                }
            }
        });
        cyclicChargeBox.addChangeHandler(new FormDataChangeHandler(hasDirtyData));
        cyclicChargeNameBox.addChangeHandler(new FormDataChangeHandler(hasDirtyData));
        nonAccruingMonthlyCheckBox.addClickHandler(new FormDataClickHandler(hasDirtyData));   
        getApplyAtVend().addClickHandler(new FormDataClickHandler(hasDirtyData));
        applyAtBilling.addClickHandler(new FormDataClickHandler(hasDirtyData));
    }

    public boolean isApplyAtValid() {
        if (getApplyAtVend().getValue() == true || applyAtBilling.getValue() == true) {
            return true;
        }
        return false;
    }

    public RadioButton getApplyAtVend() {
        return applyAtVend;
    }
    
    public RadioButton getApplyAtBilling() {
        return applyAtBilling;
    }
}
